"use client"

import { AppShell } from "@/components/app-shell"
import { useI18n } from "@/components/i18n-provider"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { BookOpen, Copy, Edit, FileText, Trash2 } from "lucide-react"
import { useEffect, useState } from "react"

// Professional sample templates
const SAMPLE_SALES_TEMPLATE = `SALES CONTRACT

Contract No: {{contract_number}}
Date: {{contract_date}}
Status: {{contract_status}}

═══════════════════════════════════════════════════════════════

PARTIES TO THE CONTRACT

SELLER:
Company: {{company_name}}
Legal Name: {{company_legal_name}}
Address: {{company_full_address}}
Tax ID: {{company_tax_id}}
Registration: {{company_registration_number}}
Contact: {{company_phone}}
Email: {{company_email}}
Website: {{company_website}}

BUYER:
Company: {{customer_name}}
Address: {{customer_address}}
Email: {{customer_email}}
Contact: [Contact Person]

═══════════════════════════════════════════════════════════════

CONTRACT TERMS

1. PRODUCTS AND SPECIFICATIONS
{{product_list}}

Total Contract Value: {{total_amount}}
Currency: {{currency}}

2. PAYMENT TERMS
Payment Terms: {{payment_terms}}
Payment Method: [Bank Transfer/Letter of Credit/Other]
Bank Details: [Seller Bank Information]

3. DELIVERY TERMS
Delivery Terms: {{delivery_terms}}
Delivery Date: [Specify delivery schedule]
Shipping Port: [Port of shipment]
Destination: [Port of destination]

4. QUALITY STANDARDS
- All products shall conform to agreed specifications
- Quality certificates will be provided upon delivery
- Buyer has right to inspect goods within [X] days of delivery
- Any quality disputes must be reported within [X] days

5. FORCE MAJEURE
Neither party shall be liable for delays or failures in performance resulting from acts beyond reasonable control, including but not limited to:
- Natural disasters, wars, terrorism
- Government actions, labor strikes
- Pandemics or health emergencies
- Supply chain disruptions

6. WARRANTIES AND LIABILITY
- Seller warrants products are free from defects for [X] months
- Seller liability limited to contract value
- Buyer responsible for proper use and storage
- No consequential or indirect damages

7. INTELLECTUAL PROPERTY
- All designs, specifications remain Seller property
- Buyer may not reverse engineer or copy products
- Confidential information must be protected

8. COMPLIANCE
- Both parties comply with applicable laws and regulations
- Export/import licenses responsibility of respective parties
- Anti-corruption and sanctions compliance required

9. DISPUTE RESOLUTION
- Good faith negotiations for 30 days
- Mediation through [Mediation Center]
- Arbitration under [Arbitration Rules]
- Governing Law: [Jurisdiction]

10. TERMINATION
- Either party may terminate for material breach with 30 days notice
- Immediate termination for insolvency or legal violations
- Surviving clauses: IP, Confidentiality, Governing Law

11. GENERAL PROVISIONS
- Contract represents entire agreement
- Modifications must be in writing
- Severability clause applies
- Assignment requires written consent

═══════════════════════════════════════════════════════════════

SIGNATURES

SELLER:
_________________________    Date: ___________
[Name, Title]
[Company Name]

BUYER:
_________________________    Date: ___________
[Name, Title]
{{customer_name}}

═══════════════════════════════════════════════════════════════

Contract executed on {{current_date}}
Generated by Manufacturing ERP System`

const SAMPLE_PURCHASE_TEMPLATE = `PURCHASE CONTRACT

Contract No: {{contract_number}}
Date: {{contract_date}}
Status: {{contract_status}}

═══════════════════════════════════════════════════════════════

PARTIES TO THE CONTRACT

BUYER:
Company: {{company_name}}
Legal Name: {{company_legal_name}}
Address: {{company_full_address}}
Tax ID: {{company_tax_id}}
Registration: {{company_registration_number}}
Contact: {{company_phone}}
Email: {{company_email}}
Website: {{company_website}}

SUPPLIER:
Company: {{supplier_name}}
Address: {{supplier_address}}
Email: {{supplier_email}}
Contact: [Contact Person]

═══════════════════════════════════════════════════════════════

CONTRACT TERMS

1. MATERIALS AND SPECIFICATIONS
{{product_list}}

Total Contract Value: {{total_amount}}
Currency: {{currency}}

2. PAYMENT TERMS
Payment Terms: {{payment_terms}}
Payment Method: [Bank Transfer/Letter of Credit/Other]
Bank Details: [Buyer Bank Information]

3. DELIVERY TERMS
Delivery Terms: {{delivery_terms}}
Delivery Date: [Specify delivery schedule]
Shipping Port: [Port of shipment]
Destination: [Port of destination]

4. QUALITY STANDARDS
- All materials shall conform to agreed specifications
- Quality certificates must be provided with each shipment
- Buyer has right to inspect materials within [X] days of delivery
- Rejected materials must be replaced at Supplier cost

5. FORCE MAJEURE
Neither party shall be liable for delays or failures in performance resulting from acts beyond reasonable control, including but not limited to:
- Natural disasters, wars, terrorism
- Government actions, labor strikes
- Pandemics or health emergencies
- Raw material shortages

6. WARRANTIES AND LIABILITY
- Supplier warrants materials are free from defects for [X] months
- Supplier provides full warranty on quality and specifications
- Supplier liable for any defects or non-conformance
- Buyer may claim damages for defective materials

7. INTELLECTUAL PROPERTY
- All technical specifications remain Buyer property
- Supplier may not use designs for other customers
- Confidential information must be protected
- No reverse engineering permitted

8. COMPLIANCE
- Supplier complies with all applicable laws and regulations
- Environmental and safety standards must be met
- Ethical sourcing and labor practices required
- Anti-corruption compliance mandatory

9. DISPUTE RESOLUTION
- Good faith negotiations for 30 days
- Mediation through [Mediation Center]
- Arbitration under [Arbitration Rules]
- Governing Law: [Jurisdiction]

10. TERMINATION
- Either party may terminate for material breach with 30 days notice
- Immediate termination for quality failures or legal violations
- Surviving clauses: IP, Confidentiality, Governing Law

11. GENERAL PROVISIONS
- Contract represents entire agreement
- Modifications must be in writing
- Severability clause applies
- Assignment requires written consent

═══════════════════════════════════════════════════════════════

SIGNATURES

BUYER:
_________________________    Date: ___________
[Name, Title]
[Company Name]

SUPPLIER:
_________________________    Date: ___________
[Name, Title]
{{supplier_name}}

═══════════════════════════════════════════════════════════════

Contract executed on {{current_date}}
Generated by Manufacturing ERP System`

export default function ContractTemplatesPage() {
  const { t } = useI18n()
  return (
    <AppShell>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Contract Templates</h1>
            <p className="text-muted-foreground">
              Manage reusable contract templates for sales and purchase agreements
            </p>
          </div>
        </div>

        <div className="grid gap-6">
          <SalesTemplatesCard />
          <PurchaseTemplatesCard />
        </div>
      </div>
    </AppShell>
  )
}

function SalesTemplatesCard() {
  const { t } = useI18n()
  const [templates, setTemplates] = useState<any[]>([])
  const [form, setForm] = useState({
    name: "",
    type: "sales",
    currency: "USD",
    payment_terms: "30 days",
    delivery_terms: "FOB",
    template_content: ""
  })

  async function load() {
    try {
      const response = await fetch("/api/contracts/templates?type=sales")
      if (response.ok) {
        const data = await response.json()
        setTemplates(data.data?.templates || [])
      } else {
        setTemplates([])
      }
    } catch (error) {
      console.error("Failed to load sales templates:", error)
      setTemplates([])
    }
  }

  useEffect(() => {
    load()
  }, [])

  async function create() {
    if (!form.name || !form.template_content) return
    const templateData = {
      name: form.name,
      type: form.type,
      content: form.template_content,
      currency: form.currency,
      payment_terms: form.payment_terms,
      delivery_terms: form.delivery_terms
    }
    await fetch("/api/contracts/templates", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(templateData)
    })
    setForm({ name: "", type: "sales", currency: "USD", payment_terms: "30 days", delivery_terms: "FOB", template_content: "" })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/contracts/templates/${id}`, { method: "DELETE" })
    await load()
  }

  async function duplicate(template: any) {
    const newTemplate = {
      name: `${template.name} (Copy)`,
      type: template.type,
      content: template.content,
      currency: template.currency,
      payment_terms: template.payment_terms,
      delivery_terms: template.delivery_terms
    }
    await fetch("/api/contracts/templates", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(newTemplate)
    })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Sales Contract Templates
            </CardTitle>
            <CardDescription>
              Create and manage templates for sales contracts
            </CardDescription>
          </div>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Sample Templates
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Professional Sales Contract Template</DialogTitle>
                <DialogDescription>
                  Copy this professional template and paste it into the Template Content field below.
                  You can then customize it for your business needs.
                </DialogDescription>
              </DialogHeader>
              <div className="mt-4">
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Professional Sales Contract Template</span>
                    <Button
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(SAMPLE_SALES_TEMPLATE)
                        alert("Template copied to clipboard!")
                      }}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy Template
                    </Button>
                  </div>
                  <pre className="whitespace-pre-wrap text-xs bg-white p-3 rounded border max-h-96 overflow-y-auto">
                    {SAMPLE_SALES_TEMPLATE}
                  </pre>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <div>
            <Label htmlFor="template-name">Template Name</Label>
            <Input
              id="template-name"
              value={form.name}
              onChange={(e) => setForm({ ...form, name: e.target.value })}
              placeholder="Standard Sales Contract"
            />
          </div>
          <div>
            <Label htmlFor="currency">Currency</Label>
            <Select value={form.currency} onValueChange={(currency) => setForm({ ...form, currency })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="USD">USD</SelectItem>
                <SelectItem value="EUR">EUR</SelectItem>
                <SelectItem value="CNY">CNY</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="payment-terms">Payment Terms</Label>
            <Input
              id="payment-terms"
              value={form.payment_terms}
              onChange={(e) => setForm({ ...form, payment_terms: e.target.value })}
              placeholder="30 days"
            />
          </div>
          <div>
            <Label htmlFor="delivery-terms">Delivery Terms</Label>
            <Input
              id="delivery-terms"
              value={form.delivery_terms}
              onChange={(e) => setForm({ ...form, delivery_terms: e.target.value })}
              placeholder="FOB"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="template-content">Template Content</Label>
          <textarea
            id="template-content"
            className="w-full min-h-[120px] p-3 border rounded-md"
            value={form.template_content}
            onChange={(e) => setForm({ ...form, template_content: e.target.value })}
            placeholder="Enter contract template content with placeholders like {{customer_name}}, {{product_name}}, etc."
          />
        </div>

        <Button onClick={create} disabled={!form.name || !form.template_content}>
          Create Template
        </Button>

        {templates.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-3">Existing Templates</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Currency</TableHead>
                  <TableHead>Payment Terms</TableHead>
                  <TableHead>Delivery Terms</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">{template.name}</TableCell>
                    <TableCell>{template.currency}</TableCell>
                    <TableCell>{template.payment_terms}</TableCell>
                    <TableCell>{template.delivery_terms}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" onClick={() => duplicate(template)}>
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="destructive" onClick={() => remove(template.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function PurchaseTemplatesCard() {
  const { t } = useI18n()
  const [templates, setTemplates] = useState<any[]>([])
  const [form, setForm] = useState({
    name: "",
    type: "purchase",
    currency: "USD",
    payment_terms: "30 days",
    delivery_terms: "CIF",
    template_content: ""
  })

  async function load() {
    try {
      const response = await fetch("/api/contracts/templates?type=purchase")
      if (response.ok) {
        const data = await response.json()
        setTemplates(data.data?.templates || [])
      } else {
        setTemplates([])
      }
    } catch (error) {
      console.error("Failed to load purchase templates:", error)
      setTemplates([])
    }
  }

  useEffect(() => {
    load()
  }, [])

  async function create() {
    if (!form.name || !form.template_content) return
    const templateData = {
      name: form.name,
      type: form.type,
      content: form.template_content,
      currency: form.currency,
      payment_terms: form.payment_terms,
      delivery_terms: form.delivery_terms
    }
    await fetch("/api/contracts/templates", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(templateData)
    })
    setForm({ name: "", type: "purchase", currency: "USD", payment_terms: "30 days", delivery_terms: "CIF", template_content: "" })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/contracts/templates/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Purchase Contract Templates
            </CardTitle>
            <CardDescription>
              Create and manage templates for purchase contracts
            </CardDescription>
          </div>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Sample Templates
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Professional Purchase Contract Template</DialogTitle>
                <DialogDescription>
                  Copy this professional template and paste it into the Template Content field below.
                  You can then customize it for your business needs.
                </DialogDescription>
              </DialogHeader>
              <div className="mt-4">
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Professional Purchase Contract Template</span>
                    <Button
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(SAMPLE_PURCHASE_TEMPLATE)
                        alert("Template copied to clipboard!")
                      }}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy Template
                    </Button>
                  </div>
                  <pre className="whitespace-pre-wrap text-xs bg-white p-3 rounded border max-h-96 overflow-y-auto">
                    {SAMPLE_PURCHASE_TEMPLATE}
                  </pre>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <div>
            <Label htmlFor="purchase-template-name">Template Name</Label>
            <Input
              id="purchase-template-name"
              value={form.name}
              onChange={(e) => setForm({ ...form, name: e.target.value })}
              placeholder="Standard Purchase Contract"
            />
          </div>
          <div>
            <Label htmlFor="purchase-currency">Currency</Label>
            <Select value={form.currency} onValueChange={(currency) => setForm({ ...form, currency })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="USD">USD</SelectItem>
                <SelectItem value="EUR">EUR</SelectItem>
                <SelectItem value="CNY">CNY</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="purchase-payment-terms">Payment Terms</Label>
            <Input
              id="purchase-payment-terms"
              value={form.payment_terms}
              onChange={(e) => setForm({ ...form, payment_terms: e.target.value })}
              placeholder="30 days"
            />
          </div>
          <div>
            <Label htmlFor="purchase-delivery-terms">Delivery Terms</Label>
            <Input
              id="purchase-delivery-terms"
              value={form.delivery_terms}
              onChange={(e) => setForm({ ...form, delivery_terms: e.target.value })}
              placeholder="CIF"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="purchase-template-content">Template Content</Label>
          <textarea
            id="purchase-template-content"
            className="w-full min-h-[120px] p-3 border rounded-md"
            value={form.template_content}
            onChange={(e) => setForm({ ...form, template_content: e.target.value })}
            placeholder="Enter contract template content with placeholders like {{supplier_name}}, {{material_name}}, etc."
          />
        </div>

        <Button onClick={create} disabled={!form.name || !form.template_content}>
          Create Template
        </Button>

        {templates.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-3">Existing Templates</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Currency</TableHead>
                  <TableHead>Payment Terms</TableHead>
                  <TableHead>Delivery Terms</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">{template.name}</TableCell>
                    <TableCell>{template.currency}</TableCell>
                    <TableCell>{template.payment_terms}</TableCell>
                    <TableCell>{template.delivery_terms}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="destructive" onClick={() => remove(template.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}