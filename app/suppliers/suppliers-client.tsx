"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FilePen, Plus, Trash2, Users, Phone, Mail } from "lucide-react";
import { Toaster, toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AddSupplierForm } from "./add-supplier-form";
import { EditSupplierForm } from "./edit-supplier-form";
import { useRouter } from "next/navigation";

// Type definition for Supplier - should match the schema
type Supplier = {
  id: string;
  name: string;
  contact_name: string | null;
  contact_phone: string | null;
  contact_email: string | null;
  address: string | null;
  tax_id: string | null;
  bank: string | null;
  status: string | null;
  created_at: Date | null;
};

export function SuppliersClientPage({ initialSuppliers }: { initialSuppliers: Supplier[] }) {
  const router = useRouter();
  const [isAddOpen, setAddOpen] = useState(false);
  const [supplierToEdit, setSupplierToEdit] = useState<Supplier | null>(null);
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);

  const handleDelete = async () => {
    if (!supplierToDelete) return;
    const response = await fetch(`/api/suppliers/${supplierToDelete.id}`, { method: "DELETE" });
    if (response.ok) {
      toast.success(`Supplier "${supplierToDelete.name}" deleted successfully.`);
      setSupplierToDelete(null);
      router.refresh();
    } else {
      toast.error("Failed to delete supplier.");
    }
  };

  const getStatusColor = (status: string | null) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "default";
      case "inactive":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Suppliers</h1>
          <p className="text-muted-foreground">Manage your company's suppliers.</p>
        </div>
        <Dialog open={isAddOpen} onOpenChange={setAddOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Supplier
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Supplier</DialogTitle>
              <DialogDescription>Enter the details of the new supplier.</DialogDescription>
            </DialogHeader>
            <AddSupplierForm setOpen={setAddOpen} />
          </DialogContent>
        </Dialog>
      </div>
      <Toaster richColors />

      <Card>
        <CardHeader>
          <CardTitle>Supplier List</CardTitle>
          <CardDescription>A list of all suppliers in your system.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {initialSuppliers.map((supplier) => (
              <Card key={supplier.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CardTitle className="text-lg">{supplier.name}</CardTitle>
                        <Badge variant={getStatusColor(supplier.status)}>{supplier.status}</Badge>
                      </div>
                      <CardDescription>{supplier.address}</CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="icon" onClick={() => setSupplierToEdit(supplier)}>
                        <FilePen className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => setSupplierToDelete(supplier)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-3">
                      <h4 className="font-semibold">Contact Information</h4>
                      <div className="space-y-2">
                        {supplier.contact_name && (
                          <div className="flex items-center gap-2 text-sm">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            {supplier.contact_name}
                          </div>
                        )}
                        {supplier.contact_email && (
                          <div className="flex items-center gap-2 text-sm">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            {supplier.contact_email}
                          </div>
                        )}
                        {supplier.contact_phone && (
                          <div className="flex items-center gap-2 text-sm">
                            <Phone className="h-4 w-4 text-muted-foreground" />
                            {supplier.contact_phone}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-semibold">Bank Details</h4>
                      <div className="space-y-2 text-sm">
                        <p>{supplier.bank || "N/A"}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={!!supplierToEdit} onOpenChange={(open) => !open && setSupplierToEdit(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Supplier</DialogTitle>
            <DialogDescription>Update the details of the supplier.</DialogDescription>
          </DialogHeader>
          {supplierToEdit && <EditSupplierForm setOpen={(open) => !open && setSupplierToEdit(null)} supplier={supplierToEdit} />}
        </DialogContent>
      </Dialog>

      {/* Delete Alert Dialog */}
      <AlertDialog open={!!supplierToDelete} onOpenChange={(open) => !open && setSupplierToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the supplier "{supplierToDelete?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
