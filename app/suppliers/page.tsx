import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { suppliers } from "@/lib/schema-postgres";
import { desc, eq } from "drizzle-orm";
import { SuppliersClientPage } from "./suppliers-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect } from "next/navigation";

export default async function SuppliersPage() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch suppliers for the current company
  const allSuppliers = await db.query.suppliers.findMany({
    where: eq(suppliers.company_id, context.companyId),
    orderBy: [desc(suppliers.created_at)],
  });

  return (
    <AppShell>
      <SuppliersClientPage initialSuppliers={allSuppliers} />
    </AppShell>
  );
}
