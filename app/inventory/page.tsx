"use client"

import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"

export default function InventoryPage() {
  return (
    <AppShell>
      <div className="grid gap-6">
        <InboundCard />
        <OutboundCard />
        <StockCard />
        <TransactionsCard />
      </div>
    </AppShell>
  )
}

function InboundCard() {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [form, setForm] = useState({ product_id: "", qty: 100, location: "Main", note: "" })

  useEffect(() => {
    safeJson("/api/products", []).then(setProducts)
  }, [])

  async function add() {
    if (!form.product_id || !form.location || !form.qty) return
    await fetch("/api/inventory/inbound", { method: "POST", body: JSON.stringify(form) })
    setForm({ product_id: "", qty: 100, location: "Main", note: "" })
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    await (window as any).refreshInventory?.()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("inventory.inbound.title")}</CardTitle>
        <CardDescription>{t("inventory.inbound.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-3 md:grid-cols-5">
        <div className="grid gap-1">
          <Label>{t("field.product")}</Label>
          <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
            <SelectTrigger>
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {products.map((p) => (
                <SelectItem key={p.id} value={p.id}>
                  {p.sku} - {p.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-1">
          <Label>{t("field.qty")}</Label>
          <Input type="number" value={form.qty} onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })} />
        </div>
        <div className="grid gap-1">
          <Label>{t("field.location")}</Label>
          <Input value={form.location} onChange={(e) => setForm({ ...form, location: e.target.value })} />
        </div>
        <div className="grid gap-1 md:col-span-2">
          <Label>{t("field.note")}</Label>
          <Input value={form.note} onChange={(e) => setForm({ ...form, note: e.target.value })} />
        </div>
        <div className="md:col-span-5">
          <Button onClick={add}>{t("action.addInbound")}</Button>
        </div>
      </CardContent>
    </Card>
  )
}

function OutboundCard() {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [form, setForm] = useState({ product_id: "", qty: 10, location: "Main", ref: "" })

  useEffect(() => {
    safeJson("/api/products", []).then(setProducts)
  }, [])

  async function ship() {
    if (!form.product_id || !form.qty || !form.location) return
    const res = await fetch("/api/inventory/outbound", { method: "POST", body: JSON.stringify(form) })
    if (!res.ok) {
      alert(await res.text())
      return
    }
    setForm({ product_id: "", qty: 10, location: "Main", ref: "" })
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    await (window as any).refreshInventory?.()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("inventory.outbound.title")}</CardTitle>
        <CardDescription>{t("inventory.outbound.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-3 md:grid-cols-5">
        <div className="grid gap-1">
          <Label>{t("field.product")}</Label>
          <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
            <SelectTrigger>
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {products.map((p) => (
                <SelectItem key={p.id} value={p.id}>
                  {p.sku} - {p.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-1">
          <Label>{t("field.qty")}</Label>
          <Input type="number" value={form.qty} onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })} />
        </div>
        <div className="grid gap-1">
          <Label>{t("field.location")}</Label>
          <Input value={form.location} onChange={(e) => setForm({ ...form, location: e.target.value })} />
        </div>
        <div className="grid gap-1 md:col-span-2">
          <Label>{t("field.reference")}</Label>
          <Input
            value={form.ref}
            onChange={(e) => setForm({ ...form, ref: e.target.value })}
            placeholder="SO/Shipment"
          />
        </div>
        <div className="md:col-span-5">
          <Button onClick={ship}>{t("action.addOutbound")}</Button>
        </div>
      </CardContent>
    </Card>
  )
}

function StockCard() {
  const { t } = useI18n()
  const [lots, setLots] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  async function refresh() {
    try {
      setLoading(true)
      setError(null)
      const [l, p] = await Promise.all([safeJson("/api/inventory/lots", []), safeJson("/api/products", [])])

      // Ensure we always have arrays
      setLots(Array.isArray(l) ? l : [])
      setProducts(Array.isArray(p) ? p : [])
    } catch (err) {
      console.error('Error refreshing inventory:', err)
      setError('Failed to load inventory data')
      setLots([])
      setProducts([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    ;(window as any).refreshInventory = refresh
    refresh()
    return () => {
      ;(window as any).refreshInventory = undefined
    }
  }, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("inventory.stock.title")}</CardTitle>
        <CardDescription>{t("inventory.stock.desc")}</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading inventory...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 mb-2">{error}</p>
            <button
              onClick={refresh}
              className="text-blue-600 hover:underline"
            >
              Try again
            </button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("field.product")}</TableHead>
                <TableHead>Lot</TableHead>
                <TableHead>{t("field.qty")}</TableHead>
                <TableHead>{t("field.location")}</TableHead>
                <TableHead>{t("field.note")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.isArray(lots) && lots.map((l) => (
                <TableRow key={l.id}>
                  <TableCell>{products.find((p) => p.id === l.product_id)?.name || l.product_id}</TableCell>
                  <TableCell>{l.id}</TableCell>
                  <TableCell>{l.qty}</TableCell>
                  <TableCell>{l.location}</TableCell>
                  <TableCell>{l.note || ""}</TableCell>
                </TableRow>
              ))}
              {(!Array.isArray(lots) || lots.length === 0) && (
                <TableRow>
                  <TableCell colSpan={5} className="text-muted-foreground">
                    {t("inventory.stock.empty")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}

function TransactionsCard() {
  const { t } = useI18n()
  const [txns, setTxns] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])

  async function refresh() {
    try {
      const [tRows, p] = await Promise.all([safeJson("/api/inventory/txns", []), safeJson("/api/products", [])])
      setTxns(Array.isArray(tRows) ? tRows : [])
      setProducts(Array.isArray(p) ? p : [])
    } catch (err) {
      console.error('Error refreshing transactions:', err)
      setTxns([])
      setProducts([])
    }
  }

  useEffect(() => {
    refresh()
  }, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("inventory.txns.title")}</CardTitle>
        <CardDescription>{t("inventory.txns.desc")}</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Type</TableHead>
              <TableHead>{t("field.product")}</TableHead>
              <TableHead>{t("field.qty")}</TableHead>
              <TableHead>{t("field.location")}</TableHead>
              <TableHead>{t("field.reference")}</TableHead>
              <TableHead>Time</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.isArray(txns) && txns.map((tRow) => (
              <TableRow key={tRow.id}>
                <TableCell>{tRow.type}</TableCell>
                <TableCell>{products.find((p) => p.id === tRow.product_id)?.name || tRow.product_id}</TableCell>
                <TableCell>{tRow.qty}</TableCell>
                <TableCell>{tRow.location}</TableCell>
                <TableCell>{tRow.ref || ""}</TableCell>
                <TableCell>{new Date(tRow.created_at).toLocaleString()}</TableCell>
              </TableRow>
            ))}
            {(!Array.isArray(txns) || txns.length === 0) && (
              <TableRow>
                <TableCell colSpan={6} className="text-muted-foreground">
                  {t("inventory.txns.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
