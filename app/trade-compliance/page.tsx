"use client"

import { AppShell } from "@/components/app-shell"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Shield, Globe, FileText, AlertTriangle, CheckCircle, Search } from "lucide-react"

export default function TradeCompliancePage() {
  return (
    <AppShell>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Trade Compliance</h1>
            <p className="text-muted-foreground">
              Manage HS codes, trade regulations, and compliance requirements
            </p>
          </div>
        </div>
        
        {/* Coming Soon Notice */}
        <div className="text-center py-12">
          <Shield className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-2xl font-semibold mb-2">Trade Compliance Module Coming Soon</h2>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            This module will manage HS codes, trade regulations, duty calculations, and compliance requirements 
            for international trade operations.
          </p>
          <div className="grid gap-4 md:grid-cols-3 max-w-2xl mx-auto">
            <div className="p-4 border rounded-lg">
              <Search className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <h3 className="font-medium">HS Code Management</h3>
              <p className="text-sm text-muted-foreground">Classify products correctly</p>
            </div>
            <div className="p-4 border rounded-lg">
              <Globe className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <h3 className="font-medium">Trade Regulations</h3>
              <p className="text-sm text-muted-foreground">Stay compliant globally</p>
            </div>
            <div className="p-4 border rounded-lg">
              <FileText className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <h3 className="font-medium">Documentation</h3>
              <p className="text-sm text-muted-foreground">Compliance certificates</p>
            </div>
          </div>
          
          <div className="mt-8 grid gap-4 md:grid-cols-2 max-w-4xl mx-auto">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Compliance Features
              </h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• HS code database and search</li>
                <li>• Country-specific duty rates</li>
                <li>• Trade agreement preferences</li>
                <li>• Restricted party screening</li>
                <li>• Certificate of origin management</li>
              </ul>
            </div>
            <div className="p-4 bg-amber-50 rounded-lg">
              <h3 className="font-medium text-amber-900 mb-2 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                Risk Management
              </h3>
              <ul className="text-sm text-amber-700 space-y-1">
                <li>• Export control compliance</li>
                <li>• Sanctions screening</li>
                <li>• Anti-dumping duty tracking</li>
                <li>• Quota and license management</li>
                <li>• Audit trail and reporting</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </AppShell>
  )
}
