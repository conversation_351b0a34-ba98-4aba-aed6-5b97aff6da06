"use client"

import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"

export default function FinancePage() {
  return (
    <AppShell>
      <div className="grid gap-6">
        <ARCard />
        <APCard />
      </div>
    </AppShell>
  )
}

function ARCard() {
  const { t } = useI18n()
  const [customers, setCustomers] = useState<any[]>([])
  const [rows, setRows] = useState<any[]>([])
  const [form, setForm] = useState({ number: "", customer_id: "", currency: "USD", amount: 1000, received: 0 })

  async function load() {
    const [cs, rs] = await Promise.all([safeJson("/api/customers", []), safeJson("/api/finance/ar", [])])
    setCustomers(cs)
    setRows(rs)
  }
  useEffect(() => {
    load()
  }, [])

  async function create() {
    if (!form.number || !form.customer_id || !form.amount) return
    await fetch("/api/finance/ar", { method: "POST", body: JSON.stringify(form) })
    setForm({ number: "", customer_id: "", currency: "USD", amount: 1000, received: 0 })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/finance/ar/${id}`, { method: "DELETE" })
    await load()
  }

  const aging = (date: string) => {
    const days = Math.floor((Date.now() - new Date(date).getTime()) / (1000 * 60 * 60 * 24))
    if (days <= 30) return "0–30"
    if (days <= 60) return "31–60"
    if (days <= 90) return "61–90"
    return "90+"
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("finance.ar.title")}</CardTitle>
        <CardDescription>{t("finance.ar.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-5">
          <div className="grid gap-1">
            <Label>{t("field.invoiceNo")}</Label>
            <Input value={form.number} onChange={(e) => setForm({ ...form, number: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.customer")}</Label>
            <Select value={form.customer_id} onValueChange={(v) => setForm({ ...form, customer_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {customers.map((c) => (
                  <SelectItem key={c.id} value={c.id}>
                    {c.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.amount")}</Label>
            <Input
              type="number"
              value={form.amount}
              onChange={(e) => setForm({ ...form, amount: Number(e.target.value) })}
            />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.received")}</Label>
            <Input
              type="number"
              value={form.received}
              onChange={(e) => setForm({ ...form, received: Number(e.target.value) })}
            />
          </div>
          <div className="flex items-end">
            <Button onClick={create}>{t("action.createAR")}</Button>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("field.invoiceNo")}</TableHead>
              <TableHead>{t("field.customer")}</TableHead>
              <TableHead>{t("field.amount")}</TableHead>
              <TableHead>{t("field.received")}</TableHead>
              <TableHead>Aging</TableHead>
              <TableHead>{t("table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((r) => (
              <TableRow key={r.id}>
                <TableCell>{r.number}</TableCell>
                <TableCell>{customers.find((c) => c.id === r.customer_id)?.name || r.customer_id}</TableCell>
                <TableCell>{Number(r.amount).toFixed(2)}</TableCell>
                <TableCell>{Number(r.received).toFixed(2)}</TableCell>
                <TableCell>{aging(r.date)}</TableCell>
                <TableCell className="text-right">
                  <Button size="sm" variant="ghost" onClick={() => remove(r.id)}>
                    {t("action.delete")}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {rows.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} className="text-muted-foreground">
                  {t("finance.ar.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

function APCard() {
  const { t } = useI18n()
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [rows, setRows] = useState<any[]>([])
  const [form, setForm] = useState({ number: "", supplier_id: "", currency: "USD", amount: 800, paid: 0 })

  async function load() {
    const [ss, rs] = await Promise.all([safeJson("/api/suppliers", []), safeJson("/api/finance/ap", [])])
    setSuppliers(ss)
    setRows(rs)
  }
  useEffect(() => {
    load()
  }, [])

  async function create() {
    if (!form.number || !form.supplier_id || !form.amount) return
    await fetch("/api/finance/ap", { method: "POST", body: JSON.stringify(form) })
    setForm({ number: "", supplier_id: "", currency: "USD", amount: 800, paid: 0 })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/finance/ap/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("finance.ap.title")}</CardTitle>
        <CardDescription>{t("finance.ap.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-5">
          <div className="grid gap-1">
            <Label>{t("field.invoiceNo")}</Label>
            <Input value={form.number} onChange={(e) => setForm({ ...form, number: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.supplier")}</Label>
            <Select value={form.supplier_id} onValueChange={(v) => setForm({ ...form, supplier_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map((s) => (
                  <SelectItem key={s.id} value={s.id}>
                    {s.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.amount")}</Label>
            <Input
              type="number"
              value={form.amount}
              onChange={(e) => setForm({ ...form, amount: Number(e.target.value) })}
            />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.paid")}</Label>
            <Input
              type="number"
              value={form.paid}
              onChange={(e) => setForm({ ...form, paid: Number(e.target.value) })}
            />
          </div>
          <div className="flex items-end">
            <Button onClick={create}>{t("action.createAP")}</Button>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("field.invoiceNo")}</TableHead>
              <TableHead>{t("field.supplier")}</TableHead>
              <TableHead>{t("field.amount")}</TableHead>
              <TableHead>{t("field.paid")}</TableHead>
              <TableHead>{t("table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((r) => (
              <TableRow key={r.id}>
                <TableCell>{r.number}</TableCell>
                <TableCell>{suppliers.find((s) => s.id === r.supplier_id)?.name || r.supplier_id}</TableCell>
                <TableCell>{Number(r.amount).toFixed(2)}</TableCell>
                <TableCell>{Number(r.paid).toFixed(2)}</TableCell>
                <TableCell className="text-right">
                  <Button size="sm" variant="ghost" onClick={() => remove(r.id)}>
                    {t("action.delete")}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {rows.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-muted-foreground">
                  {t("finance.ap.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
