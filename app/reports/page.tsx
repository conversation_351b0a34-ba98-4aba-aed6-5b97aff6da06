"use client"

import { AppShell } from "@/components/app-shell"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3, TrendingUp, FileText, PieChart, Calendar, Download } from "lucide-react"

export default function ReportsPage() {
  return (
    <AppShell>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reports & Analytics</h1>
            <p className="text-muted-foreground">
              Comprehensive business intelligence and performance analytics
            </p>
          </div>
        </div>
        
        {/* Coming Soon Notice */}
        <div className="text-center py-12">
          <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-2xl font-semibold mb-2">Reports & Analytics Coming Soon</h2>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            Advanced reporting and analytics dashboard will provide comprehensive insights 
            into your manufacturing operations, sales performance, and business metrics.
          </p>
          <div className="grid gap-4 md:grid-cols-3 max-w-2xl mx-auto">
            <div className="p-4 border rounded-lg">
              <TrendingUp className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <h3 className="font-medium">Sales Analytics</h3>
              <p className="text-sm text-muted-foreground">Revenue and performance</p>
            </div>
            <div className="p-4 border rounded-lg">
              <PieChart className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <h3 className="font-medium">Production Reports</h3>
              <p className="text-sm text-muted-foreground">Manufacturing insights</p>
            </div>
            <div className="p-4 border rounded-lg">
              <FileText className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <h3 className="font-medium">Custom Reports</h3>
              <p className="text-sm text-muted-foreground">Tailored analytics</p>
            </div>
          </div>
          
          <div className="mt-8 grid gap-4 md:grid-cols-2 max-w-4xl mx-auto">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Analytics Features
              </h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Real-time dashboard metrics</li>
                <li>• Sales and revenue tracking</li>
                <li>• Production efficiency reports</li>
                <li>• Customer behavior analysis</li>
                <li>• Inventory turnover insights</li>
              </ul>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2 flex items-center gap-2">
                <Download className="h-4 w-4" />
                Export Options
              </h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• PDF report generation</li>
                <li>• Excel data exports</li>
                <li>• Scheduled email reports</li>
                <li>• Interactive dashboards</li>
                <li>• Custom date ranges</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </AppShell>
  )
}
