"use client"

import { AppShell } from "@/components/app-shell"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useI18n } from "@/components/i18n-provider"
import { FileText, Image, Download } from "lucide-react"

export default function PlanImagesPage() {
  const { t } = useI18n()
  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Documentation</h1>
          <p className="text-muted-foreground">
            System architecture and planning documentation
          </p>
        </div>

        {/* Coming Soon Notice */}
        <div className="text-center py-12">
          <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-2xl font-semibold mb-2">System Documentation Coming Soon</h2>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            Comprehensive system documentation, architecture diagrams, and implementation guides
            will be available here once the system is fully deployed.
          </p>
          <div className="grid gap-4 md:grid-cols-3 max-w-2xl mx-auto">
            <div className="p-4 border rounded-lg">
              <Image className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <h3 className="font-medium">Architecture Diagrams</h3>
              <p className="text-sm text-muted-foreground">System design and flow</p>
            </div>
            <div className="p-4 border rounded-lg">
              <FileText className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <h3 className="font-medium">User Guides</h3>
              <p className="text-sm text-muted-foreground">Step-by-step tutorials</p>
            </div>
            <div className="p-4 border rounded-lg">
              <Download className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <h3 className="font-medium">API Documentation</h3>
              <p className="text-sm text-muted-foreground">Developer resources</p>
            </div>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Documentation Status
            </CardTitle>
            <CardDescription>
              Current implementation status of planned modules
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="p-3 bg-green-50 rounded-lg">
                <h3 className="font-medium text-green-900">✅ Completed Modules</h3>
                <ul className="text-sm text-green-700 mt-2 space-y-1">
                  <li>• Customer Management</li>
                  <li>• Product Catalog</li>
                  <li>• Supplier Management</li>
                  <li>• Sales Contracts</li>
                  <li>• Purchase Contracts</li>
                  <li>• Contract Templates</li>
                </ul>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <h3 className="font-medium text-blue-900">🚧 In Development</h3>
                <ul className="text-sm text-blue-700 mt-2 space-y-1">
                  <li>• Sample Management</li>
                  <li>• Export Declarations</li>
                  <li>• Finance (AR/AP)</li>
                  <li>• Inventory Management</li>
                  <li>• Production Orders</li>
                  <li>• Quality Control</li>
                </ul>
              </div>
              <div className="p-3 bg-amber-50 rounded-lg">
                <h3 className="font-medium text-amber-900">📋 Planned</h3>
                <ul className="text-sm text-amber-700 mt-2 space-y-1">
                  <li>• Shipping & Tracking</li>
                  <li>• Trade Compliance</li>
                  <li>• Document Management</li>
                  <li>• Reporting & Analytics</li>
                  <li>• Mobile App</li>
                  <li>• API Integrations</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
