"use client"

import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"
import { CheckCircle, AlertTriangle, XCircle, Eye, FileText } from "lucide-react"

export default function QualityPage() {
  const { t } = useI18n()
  return (
    <AppShell>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Quality Control</h1>
            <p className="text-muted-foreground">
              Manage quality inspections, defect tracking, and compliance reports
            </p>
          </div>
        </div>
        
        <div className="grid gap-6">
          <QualityMetricsCard />
          <InspectionsCard />
          <DefectsCard />
        </div>
      </div>
    </AppShell>
  )
}

function QualityMetricsCard() {
  const [metrics, setMetrics] = useState({
    pass_rate: 0,
    total_inspections: 0,
    pending_inspections: 0,
    defect_rate: 0
  })

  async function loadMetrics() {
    try {
      const response = await fetch("/api/quality/metrics?period=30")
      if (response.ok) {
        const data = await response.json()
        setMetrics({
          pass_rate: data.derived?.passRate || 0,
          total_inspections: data.inspections?.total || 0,
          pending_inspections: data.inspections?.byStatus?.scheduled || 0,
          defect_rate: data.derived?.defectRate || 0
        })
      }
    } catch (error) {
      console.error("Failed to load metrics:", error)
      // Keep default values on error
    }
  }

  useEffect(() => {
    loadMetrics()
  }, [])

  return (
    <div className="grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pass Rate</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{metrics.pass_rate}%</div>
          <p className="text-xs text-muted-foreground">+2.1% from last month</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Inspections</CardTitle>
          <Eye className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.total_inspections}</div>
          <p className="text-xs text-muted-foreground">This month</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pending</CardTitle>
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">{metrics.pending_inspections}</div>
          <p className="text-xs text-muted-foreground">Awaiting inspection</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Defect Rate</CardTitle>
          <XCircle className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{metrics.defect_rate}%</div>
          <p className="text-xs text-muted-foreground">-0.8% from last month</p>
        </CardContent>
      </Card>
    </div>
  )
}

function InspectionsCard() {
  const { t } = useI18n()
  const [inspections, setInspections] = useState<any[]>([])
  const [workOrders, setWorkOrders] = useState<any[]>([])
  const [form, setForm] = useState({ 
    work_order_id: "", 
    inspector: "", 
    inspection_type: "incoming",
    status: "pending",
    notes: ""
  })

  async function load() {
    try {
      const [i, w] = await Promise.all([
        safeJson("/api/quality/inspections", []),
        safeJson("/api/production/work-orders", [])
      ])
      setInspections(Array.isArray(i) ? i : [])
      setWorkOrders(Array.isArray(w) ? w : [])
    } catch (error) {
      console.error("Failed to load quality data:", error)
      setInspections([])
      setWorkOrders([])
    }
  }
  
  useEffect(() => {
    load()
  }, [])

  async function create() {
    if (!form.work_order_id || !form.inspector) {
      alert("Please select a work order and enter an inspector name")
      return
    }

    try {
      const response = await fetch("/api/quality/inspections", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...form,
          scheduled_date: new Date().toISOString().split('T')[0] // Today's date
        })
      })

      if (response.ok) {
        alert("Quality inspection scheduled successfully!")
        setForm({ work_order_id: "", inspector: "", inspection_type: "incoming", status: "scheduled", notes: "" })
        await load()
      } else {
        const error = await response.text()
        alert(`Failed to schedule inspection: ${error}`)
      }
    } catch (error) {
      alert(`Error: ${error.message}`)
    }
  }

  async function updateStatus(id: string, status: string, notes?: string) {
    await fetch(`/api/quality/inspections/${id}`, { 
      method: "PATCH", 
      body: JSON.stringify({ status, notes }) 
    })
    await load()
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "passed": return <CheckCircle className="h-4 w-4 text-green-600" />
      case "failed": return <XCircle className="h-4 w-4 text-red-600" />
      case "pending": return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      default: return <Eye className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Quality Inspections
        </CardTitle>
        <CardDescription>
          Schedule and track quality inspections for production orders
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          <div>
            <Label htmlFor="work-order">Work Order</Label>
            <Select value={form.work_order_id} onValueChange={(work_order_id) => setForm({ ...form, work_order_id })}>
              <SelectTrigger>
                <SelectValue placeholder="Select work order" />
              </SelectTrigger>
              <SelectContent>
                {(workOrders || []).map((wo) => (
                  <SelectItem key={wo.id} value={wo.id}>{wo.number}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="inspector">Inspector</Label>
            <Input
              id="inspector"
              value={form.inspector}
              onChange={(e) => setForm({ ...form, inspector: e.target.value })}
              placeholder="Inspector name"
            />
          </div>
          <div>
            <Label htmlFor="inspection-type">Type</Label>
            <Select value={form.inspection_type} onValueChange={(inspection_type) => setForm({ ...form, inspection_type })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="incoming">Incoming Material</SelectItem>
                <SelectItem value="in-process">In-Process</SelectItem>
                <SelectItem value="final">Final Inspection</SelectItem>
                <SelectItem value="pre-shipment">Pre-Shipment</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Input
              id="notes"
              value={form.notes}
              onChange={(e) => setForm({ ...form, notes: e.target.value })}
              placeholder="Inspection notes"
            />
          </div>
          <div className="flex items-end">
            <Button onClick={create} disabled={!form.work_order_id || !form.inspector}>
              Schedule Inspection
            </Button>
          </div>
        </div>

        {inspections.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-3">Recent Inspections</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Work Order</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Inspector</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(inspections || []).map((inspection) => (
                  <TableRow key={inspection.id}>
                    <TableCell className="font-medium">{inspection.work_order_number}</TableCell>
                    <TableCell>{inspection.inspection_type}</TableCell>
                    <TableCell>{inspection.inspector}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(inspection.status)}
                        <span className="capitalize">{inspection.status}</span>
                      </div>
                    </TableCell>
                    <TableCell>{new Date(inspection.created_at).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {inspection.status === "pending" && (
                          <>
                            <Button size="sm" variant="outline" onClick={() => updateStatus(inspection.id, "passed")}>
                              Pass
                            </Button>
                            <Button size="sm" variant="destructive" onClick={() => updateStatus(inspection.id, "failed")}>
                              Fail
                            </Button>
                          </>
                        )}
                        <Button size="sm" variant="outline">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function DefectsCard() {
  const { t } = useI18n()
  const [defects, setDefects] = useState<any[]>([])
  const [form, setForm] = useState({ 
    work_order_id: "", 
    defect_type: "",
    severity: "minor",
    quantity: 1,
    description: "",
    corrective_action: ""
  })

  async function load() {
    try {
      const data = await safeJson("/api/quality/defects", [])
      setDefects(Array.isArray(data) ? data : [])
    } catch (error) {
      console.error("Failed to load defects:", error)
      setDefects([])
    }
  }
  
  useEffect(() => {
    load()
  }, [])

  async function create() {
    if (!form.work_order_id || !form.defect_type || !form.description) {
      alert("Please fill in work order, defect type, and description")
      return
    }

    try {
      const response = await fetch("/api/quality/defects", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...form,
          quantity: form.quantity.toString(),
          status: "open"
        })
      })

      if (response.ok) {
        alert("Quality defect recorded successfully!")
        setForm({ work_order_id: "", defect_type: "", severity: "minor", quantity: 1, description: "", corrective_action: "" })
        await load()
      } else {
        const error = await response.text()
        alert(`Failed to record defect: ${error}`)
      }
    } catch (error) {
      alert(`Error: ${error.message}`)
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "text-red-600"
      case "major": return "text-orange-600"
      case "minor": return "text-yellow-600"
      default: return "text-gray-600"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <XCircle className="h-5 w-5" />
          Defect Tracking
        </CardTitle>
        <CardDescription>
          Record and track quality defects and corrective actions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <div>
            <Label htmlFor="defect-work-order">Work Order ID</Label>
            <Input
              id="defect-work-order"
              value={form.work_order_id}
              onChange={(e) => setForm({ ...form, work_order_id: e.target.value })}
              placeholder="WO-001"
            />
          </div>
          <div>
            <Label htmlFor="defect-type">Defect Type</Label>
            <Select value={form.defect_type} onValueChange={(defect_type) => setForm({ ...form, defect_type })}>
              <SelectTrigger>
                <SelectValue placeholder="Select defect type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="color-variation">Color Variation</SelectItem>
                <SelectItem value="fabric-defect">Fabric Defect</SelectItem>
                <SelectItem value="stitching-issue">Stitching Issue</SelectItem>
                <SelectItem value="sizing-error">Sizing Error</SelectItem>
                <SelectItem value="print-defect">Print Defect</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="severity">Severity</Label>
            <Select value={form.severity} onValueChange={(severity) => setForm({ ...form, severity })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="minor">Minor</SelectItem>
                <SelectItem value="major">Major</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="quantity">Affected Quantity</Label>
            <Input
              id="quantity"
              type="number"
              value={form.quantity}
              onChange={(e) => setForm({ ...form, quantity: Number(e.target.value) })}
              min="1"
            />
          </div>
          <div>
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={form.description}
              onChange={(e) => setForm({ ...form, description: e.target.value })}
              placeholder="Describe the defect"
            />
          </div>
        </div>
        
        <div>
          <Label htmlFor="corrective-action">Corrective Action</Label>
          <Input
            id="corrective-action"
            value={form.corrective_action}
            onChange={(e) => setForm({ ...form, corrective_action: e.target.value })}
            placeholder="Action taken to resolve the issue"
          />
        </div>
        
        <Button onClick={create} disabled={!form.work_order_id || !form.defect_type || !form.description}>
          Record Defect
        </Button>

        {defects.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-3">Recent Defects</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Work Order</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(defects || []).map((defect) => (
                  <TableRow key={defect.id}>
                    <TableCell className="font-medium">{defect.work_order_id}</TableCell>
                    <TableCell>{defect.defect_type}</TableCell>
                    <TableCell>
                      <span className={`capitalize font-medium ${getSeverityColor(defect.severity)}`}>
                        {defect.severity}
                      </span>
                    </TableCell>
                    <TableCell>{defect.quantity}</TableCell>
                    <TableCell>{defect.description}</TableCell>
                    <TableCell>{new Date(defect.created_at).toLocaleDateString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}