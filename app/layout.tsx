import "@/app/globals.css"
import { Inter } from "next/font/google"
import type { ReactNode } from "react"
import { ThemeProvider } from "@/components/theme-provider"
import { I18nProvider } from "@/components/i18n-provider"
import { ToastProvider } from "@/components/toast-provider"
import { UserProvider } from '@auth0/nextjs-auth0/client'

const inter = Inter({ subsets: ["latin"] })

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <UserProvider>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
            <I18nProvider>
              {children}
              <ToastProvider />
            </I18nProvider>
          </ThemeProvider>
        </UserProvider>
      </body>
    </html>
  )
}

export const metadata = {
      generator: 'v0.dev'
    };
