"use client"

import { AppShell } from "@/components/app-shell"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useI18n } from "@/components/i18n-provider"
import { Truck, Ship, Plane, Package, MapPin, FileText } from "lucide-react"

export default function ShippingPage() {
  const { t } = useI18n()
  return (
    <AppShell>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Shipping & Tracking</h1>
            <p className="text-muted-foreground">
              Manage shipments, track deliveries, and handle container loading documentation
            </p>
          </div>
        </div>
        
        {/* Coming Soon Notice */}
        <div className="text-center py-12">
          <Ship className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-2xl font-semibold mb-2">Shipping Module Coming Soon</h2>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            This module will handle shipment management, tracking, and container loading documentation. 
            It will be connected to your sales contracts and export declarations.
          </p>
          <div className="grid gap-4 md:grid-cols-3 max-w-2xl mx-auto">
            <div className="p-4 border rounded-lg">
              <Truck className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <h3 className="font-medium">Shipment Management</h3>
              <p className="text-sm text-muted-foreground">Track all your shipments</p>
            </div>
            <div className="p-4 border rounded-lg">
              <MapPin className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <h3 className="font-medium">Real-time Tracking</h3>
              <p className="text-sm text-muted-foreground">Monitor delivery status</p>
            </div>
            <div className="p-4 border rounded-lg">
              <FileText className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <h3 className="font-medium">Documentation</h3>
              <p className="text-sm text-muted-foreground">Container loading docs</p>
            </div>
          </div>
          
          <div className="mt-8 p-4 bg-blue-50 rounded-lg max-w-lg mx-auto">
            <h3 className="font-medium text-blue-900 mb-2">Future Features</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Integration with export declarations</li>
              <li>• Real-time carrier tracking APIs</li>
              <li>• Container loading photo documentation</li>
              <li>• Automated shipping notifications</li>
              <li>• Multi-modal transport support</li>
            </ul>
          </div>
        </div>
      </div>
    </AppShell>
  )
}
