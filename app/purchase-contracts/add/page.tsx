import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { suppliers, products, contractTemplates } from "@/lib/schema-postgres";
import { eq } from "drizzle-orm";
import { AddPurchaseContractPage } from "./add-purchase-contract-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect } from "next/navigation";

export default async function AddPurchaseContractPageServer() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch data for the current company
  const allSuppliers = await db.query.suppliers.findMany({
    where: eq(suppliers.company_id, context.companyId),
    orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
  });

  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
    orderBy: (products, { asc }) => [asc(products.name)],
  });

  const allTemplates = await db.query.contractTemplates.findMany({
    where: eq(contractTemplates.company_id, context.companyId),
    orderBy: (contractTemplates, { asc }) => [asc(contractTemplates.name)],
  });

  return (
    <AppShell>
      <AddPurchaseContractPage
        suppliers={allSuppliers}
        products={allProducts}
        templates={allTemplates}
      />
    </AppShell>
  );
}
