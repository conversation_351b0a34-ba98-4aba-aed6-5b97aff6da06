import { notFound } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { eq, and } from "drizzle-orm"
import { purchaseContracts } from "@/lib/schema-postgres"
import { PurchaseContractViewClient } from "./purchase-contract-view-client"

interface PurchaseContractViewPageProps {
  params: Promise<{ id: string }>
}

export default async function PurchaseContractViewPage({ params }: PurchaseContractViewPageProps) {
  const { id } = await params
  const context = await getTenantContext()
  
  if (!context) {
    notFound()
  }

  // Fetch the purchase contract with related data
  const contract = await db.query.purchaseContracts.findFirst({
    where: and(
      eq(purchaseContracts.id, id),
      eq(purchaseContracts.company_id, context.companyId)
    ),
    with: {
      supplier: true,
      template: true,
      items: {
        with: {
          product: true
        }
      }
    }
  })

  if (!contract) {
    notFound()
  }

  return (
    <PurchaseContractViewClient 
      contract={contract}
      companyId={context.companyId}
    />
  )
}
