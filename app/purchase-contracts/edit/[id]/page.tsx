import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { suppliers, products, contractTemplates, purchaseContracts } from "@/lib/schema-postgres";
import { eq, and } from "drizzle-orm";
import { EditPurchaseContractPage } from "./edit-purchase-contract-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect, notFound } from "next/navigation";

export default async function EditPurchaseContractPageServer({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  const { id } = await params;

  // 🛡️ SECURE: Get the contract with tenant isolation
  const contract = await db.query.purchaseContracts.findFirst({
    where: and(
      eq(purchaseContracts.id, id),
      eq(purchaseContracts.company_id, context.companyId)
    ),
    with: {
      supplier: true,
      items: {
        with: {
          product: true,
        },
      },
    },
  });

  if (!contract) {
    notFound();
  }

  // 🛡️ SECURE: Only fetch data for the current company
  const allSuppliers = await db.query.suppliers.findMany({
    where: eq(suppliers.company_id, context.companyId),
    orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
  });

  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
    orderBy: (products, { asc }) => [asc(products.name)],
  });

  const allTemplates = await db.query.contractTemplates.findMany({
    where: eq(contractTemplates.company_id, context.companyId),
    orderBy: (contractTemplates, { asc }) => [asc(contractTemplates.name)],
  });

  return (
    <AppShell>
      <EditPurchaseContractPage
        contract={contract}
        suppliers={allSuppliers}
        products={allProducts}
        templates={allTemplates}
      />
    </AppShell>
  );
}
