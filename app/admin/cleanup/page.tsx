"use client"

import { useState } from "react"
import { AppShell } from "@/components/app-shell"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Trash2, AlertTriangle, CheckCircle, Loader2 } from "lucide-react"
import { toast } from "sonner"

interface CleanupResult {
  message: string
  deletedCounts: Record<string, number>
  deletedProducts: Array<{ id: string; name: string }>
  deletedCustomers: Array<{ id: string; name: string }>
}

export default function AdminCleanupPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<CleanupResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleCustomerCleanup = async () => {
    if (!confirm("⚠️ WARNING: This will delete ALL customers and related data!\n\nThis action cannot be undone. Are you sure you want to continue?")) {
      return
    }

    if (!confirm("🚨 FINAL CONFIRMATION: This will permanently delete all customers, samples, AR invoices, and sales contracts.\n\nType 'DELETE' in the next prompt to confirm.")) {
      return
    }

    const confirmation = prompt("Type 'DELETE' to confirm the customer cleanup:")
    if (confirmation !== "DELETE") {
      toast.error("Cleanup cancelled - confirmation text did not match")
      return
    }

    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch("/api/admin/cleanup-customers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (response.ok) {
        const data = await response.json()
        setResult(data)
        toast.success("Customer cleanup completed successfully!")
      } else {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Customer cleanup failed")
      }
    } catch (err: any) {
      setError(err.message)
      toast.error("Customer cleanup failed: " + err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleFullCleanup = async () => {
    if (!confirm("⚠️ WARNING: This will delete ALL products, customers and related data!\n\nThis action cannot be undone. Are you sure you want to continue?")) {
      return
    }

    if (!confirm("🚨 FINAL CONFIRMATION: This will permanently delete all products, customers, contracts, work orders, inventory, and quality records.\n\nType 'DELETE' in the next prompt to confirm.")) {
      return
    }

    const confirmation = prompt("Type 'DELETE' to confirm the cleanup:")
    if (confirmation !== "DELETE") {
      toast.error("Cleanup cancelled - confirmation text did not match")
      return
    }

    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch("/api/admin/cleanup-products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (response.ok) {
        const data = await response.json()
        setResult(data)
        toast.success("Database cleanup completed successfully!")
      } else {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Cleanup failed")
      }
    } catch (err: any) {
      setError(err.message)
      toast.error("Cleanup failed: " + err.message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-red-600">⚠️ Admin Database Cleanup</h1>
          <p className="text-muted-foreground">
            Dangerous operations for development and testing environments only
          </p>
        </div>

        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertTitle className="text-red-800">Danger Zone</AlertTitle>
          <AlertDescription className="text-red-700">
            These operations will permanently delete data and cannot be undone. 
            Only use in development/testing environments.
          </AlertDescription>
        </Alert>

        <div className="grid gap-6 md:grid-cols-2">
          <Card className="border-orange-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-orange-600">
                <Trash2 className="h-5 w-5" />
                Clean Up Customer Data Only
              </CardTitle>
              <CardDescription>
                This will delete ALL customers and their related records, but leave products intact.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">What will be deleted:</h4>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• All customers</li>
                  <li>• Samples and AR invoices</li>
                  <li>• Sales contracts and items</li>
                  <li>• Work orders (sales-related)</li>
                </ul>
              </div>

              <Button
                onClick={handleCustomerCleanup}
                disabled={isLoading}
                variant="outline"
                className="w-full border-orange-200 text-orange-600 hover:bg-orange-50"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Cleaning up customers...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Clean Up Customers Only
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <Trash2 className="h-5 w-5" />
                Clean Up All Product & Customer Data
              </CardTitle>
              <CardDescription>
                This will delete ALL products, customers and their related records in the correct order to avoid foreign key constraints.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">What will be deleted:</h4>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• All customers and products</li>
                  <li>• Samples and AR invoices</li>
                  <li>• Sales contract items</li>
                  <li>• Purchase contract items</li>
                  <li>• Work orders</li>
                  <li>• Stock lots and transactions</li>
                  <li>• Declaration items</li>
                  <li>• Quality defects and standards</li>
                  <li>• Related contracts and declarations</li>
                  <li>• Quality inspections and certificates</li>
                </ul>
              </div>

              <Button
                onClick={handleFullCleanup}
                disabled={isLoading}
                variant="destructive"
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Cleaning up database...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Clean Up All Product & Customer Data
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertTitle className="text-red-800">Cleanup Failed</AlertTitle>
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {result && (
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                Cleanup Completed Successfully
              </CardTitle>
              <CardDescription className="text-green-700">
                {result.message}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Records Deleted:</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {Object.entries(result.deletedCounts).map(([key, count]) => (
                    <div key={key} className="flex justify-between items-center">
                      <span className="text-sm capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                      <Badge variant="secondary">{count}</Badge>
                    </div>
                  ))}
                </div>
              </div>

              {result.deletedProducts.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Deleted Products:</h4>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {result.deletedProducts.map((product) => (
                      <div key={product.id} className="text-sm text-muted-foreground">
                        • {product.name} ({product.id})
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {result.deletedCustomers?.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Deleted Customers:</h4>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {result.deletedCustomers.map((customer) => (
                      <div key={customer.id} className="text-sm text-muted-foreground">
                        • {customer.name} ({customer.id})
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </AppShell>
  )
}
