"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { FilePen, Plus, Trash2, Package, Search, Eye, Pencil } from "lucide-react";
import { Toaster, toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AddProductForm } from "./add-product-form";
import { EditProductForm } from "./edit-product-form";
import { useRouter } from "next/navigation";

// Type definition for Product - should match the schema
type Product = {
  id: string;
  sku: string;
  name: string;
  unit: string;
  hs_code: string | null;
  origin: string | null;
  package: string | null;
  image: string | null;
  created_at: Date | null;
};

export function ProductsClientPage({ initialProducts }: { initialProducts: Product[] }) {
  const router = useRouter();
  const [isAddOpen, setAddOpen] = useState(false);
  const [productToEdit, setProductToEdit] = useState<Product | null>(null);
  const [productToView, setProductToView] = useState<Product | null>(null);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const handleDelete = async () => {
    if (!productToDelete) return;

    const productName = productToDelete.name;

    try {
      const response = await fetch(`/api/products/${productToDelete.id}`, {
        method: "DELETE",
      });

      // Close dialog immediately
      setProductToDelete(null);

      if (response.ok) {
        // Use requestAnimationFrame to ensure we're outside the render cycle
        requestAnimationFrame(() => {
          toast.success(`Product "${productName}" deleted successfully.`);
          router.refresh();
        });
      } else {
        let errorData = {};
        try {
          const responseText = await response.text();
          if (responseText) {
            errorData = JSON.parse(responseText);
          }
        } catch (parseError) {
          console.warn("Failed to parse error response:", parseError);
        }

        // Use requestAnimationFrame to ensure we're outside the render cycle
        requestAnimationFrame(() => {
          if (response.status === 409 || errorData.error?.includes("FOREIGN KEY")) {
            toast.error("Cannot delete product", {
              description: "This product is being used in contracts, work orders, or inventory. Please remove all references first.",
            });
          } else {
            toast.error("Failed to delete product", {
              description: errorData.error || "An unexpected error occurred.",
            });
          }
        });
      }
    } catch (error) {
      // Close dialog immediately
      setProductToDelete(null);

      // Use requestAnimationFrame to ensure we're outside the render cycle
      requestAnimationFrame(() => {
        toast.error("Failed to delete product", {
          description: "Network error. Please check your connection and try again.",
        });
      });
    }
  };

  const filteredProducts = initialProducts.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.hs_code && product.hs_code.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (product.origin && product.origin.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Products</h1>
          <p className="text-muted-foreground">
            Manage your company's products and materials.
          </p>
        </div>
        <Dialog open={isAddOpen} onOpenChange={setAddOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Product</DialogTitle>
              <DialogDescription>
                Enter the details of the new product.
              </DialogDescription>
            </DialogHeader>
            <AddProductForm setOpen={setAddOpen} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <Toaster richColors />

      {/* Professional Products Table */}
      {filteredProducts.length === 0 ? (
        <div className="border rounded-lg">
          <div className="flex flex-col items-center justify-center py-12">
            <Package className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No products found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchTerm ? "No products match your search criteria." : "Get started by adding your first product."}
            </p>
            <Button onClick={() => setAddOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </div>
        </div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product Name</TableHead>
                <TableHead>SKU</TableHead>
                <TableHead>Unit</TableHead>
                <TableHead>HS Code</TableHead>
                <TableHead>Origin</TableHead>
                <TableHead>Package</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.map((product) => (
                <TableRow key={product.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-muted-foreground" />
                      {product.name}
                    </div>
                  </TableCell>
                  <TableCell className="font-mono text-sm">{product.sku}</TableCell>
                  <TableCell>{product.unit}</TableCell>
                  <TableCell>{product.hs_code || "-"}</TableCell>
                  <TableCell>{product.origin || "-"}</TableCell>
                  <TableCell>{product.package || "-"}</TableCell>
                  <TableCell>
                    <Badge variant="default">Active</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex gap-2 justify-end">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setProductToView(product)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setProductToEdit(product)}
                      >
                        <Pencil className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => setProductToDelete(product)}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog
        open={!!productToEdit}
        onOpenChange={(open) => !open && setProductToEdit(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Product</DialogTitle>
            <DialogDescription>
              Update the details of the product.
            </DialogDescription>
          </DialogHeader>
          {productToEdit && (
            <EditProductForm
              setOpen={(open) => !open && setProductToEdit(null)}
              product={productToEdit}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Dialog (Read-only) */}
      <Dialog
        open={!!productToView}
        onOpenChange={(open) => !open && setProductToView(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Product Details</DialogTitle>
            <DialogDescription>
              View product information (read-only).
            </DialogDescription>
          </DialogHeader>
          {productToView && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Product Name</label>
                  <p className="text-sm font-medium">{productToView.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">SKU</label>
                  <p className="text-sm font-mono">{productToView.sku}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Unit</label>
                  <p className="text-sm">{productToView.unit}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">HS Code</label>
                  <p className="text-sm">{productToView.hs_code || "-"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Origin</label>
                  <p className="text-sm">{productToView.origin || "-"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Package</label>
                  <p className="text-sm">{productToView.package || "-"}</p>
                </div>
              </div>
              <div className="flex justify-end pt-4">
                <Button variant="outline" onClick={() => setProductToView(null)}>
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Alert Dialog */}
      <AlertDialog
        open={!!productToDelete}
        onOpenChange={(open) => !open && setProductToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the product "{productToDelete?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
