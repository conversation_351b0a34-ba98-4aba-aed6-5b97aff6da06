import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { products } from "@/lib/schema-postgres";
import { desc, eq } from "drizzle-orm";
import { ProductsClientPage } from "./products-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect } from "next/navigation";

export default async function ProductsPage() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch products for the current company
  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
    orderBy: [desc(products.created_at)],
  });

  return (
    <AppShell>
      <ProductsClientPage initialProducts={allProducts} />
    </AppShell>
  );
}
