"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Package, Clock, CheckCircle, XCircle, Send, Eye, Download, AlertCircle } from "lucide-react"
import { AppShell } from "@/components/app-shell"

export default function SamplesPage() {
  const [activeTab, setActiveTab] = useState("requests")
  const [samples, setSamples] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch real samples data from API
  useEffect(() => {
    const fetchSamples = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await fetch('/api/samples')

        if (!response.ok) {
          throw new Error('Failed to load samples data')
        }

        const data = await response.json()
        setSamples(Array.isArray(data) ? data : [])
      } catch (err) {
        console.error('Error fetching samples:', err)
        setError('Failed to load samples data. Please check your authentication.')
        setSamples([])
      } finally {
        setLoading(false)
      }
    }

    fetchSamples()
  }, [])

  // Show loading state
  if (loading) {
    return (
      <AppShell>
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Sample Management</h1>
              <p className="text-muted-foreground">Loading sample data...</p>
            </div>
          </div>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </AppShell>
    )
  }

  // Show error state
  if (error) {
    return (
      <AppShell>
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Sample Management</h1>
              <p className="text-muted-foreground">Manage customer sample requests, approvals, and inventory</p>
            </div>
          </div>
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Unable to Load Samples</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Retry
            </Button>
          </div>
        </div>
      </AppShell>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved": return "default"
      case "pending approval": return "secondary"
      case "in production": return "outline"
      case "rejected": return "destructive"
      default: return "secondary"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high": return "destructive"
      case "medium": return "secondary"
      case "low": return "outline"
      default: return "secondary"
    }
  }

  return (
    <AppShell>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Sample Management</h1>
            <p className="text-muted-foreground">Manage customer sample requests, approvals, and inventory</p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Sample Request
          </Button>
        </div>

        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Samples</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{samples.length}</div>
              <p className="text-xs text-muted-foreground">Sample records</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Samples</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {samples.filter(s => s.status === 'active').length}
              </div>
              <p className="text-xs text-muted-foreground">Currently active</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved Samples</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {samples.filter(s => s.status === 'approved').length}
              </div>
              <p className="text-xs text-muted-foreground">Ready for production</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Sample Types</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(samples.map(s => s.type)).size}
              </div>
              <p className="text-xs text-muted-foreground">Different types</p>
            </CardContent>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="samples">Sample Records</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="samples" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Sample Records</CardTitle>
                <CardDescription>
                  {samples.length === 0
                    ? "No samples found. Create your first sample to get started."
                    : `Manage your ${samples.length} sample records`
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                {samples.length === 0 ? (
                  <div className="text-center py-12">
                    <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Samples Yet</h3>
                    <p className="text-muted-foreground mb-4">
                      Start by creating your first sample record to track customer requests and approvals.
                    </p>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create First Sample
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Sample ID</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {samples.map((sample) => (
                        <TableRow key={sample.id}>
                          <TableCell className="font-medium">{sample.id}</TableCell>
                          <TableCell>{sample.type || 'General'}</TableCell>
                          <TableCell>{sample.description || sample.name || 'No description'}</TableCell>
                          <TableCell>
                            <Badge variant={sample.status === 'active' ? 'default' : 'secondary'}>
                              {sample.status || 'pending'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {sample.created_at ? new Date(sample.created_at).toLocaleDateString() : 'N/A'}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button size="sm" variant="outline">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Sample Statistics</CardTitle>
                  <CardDescription>Overview of your sample data</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Total Samples</span>
                      <span className="text-sm font-medium">{samples.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Active Samples</span>
                      <span className="text-sm font-medium text-green-600">
                        {samples.filter(s => s.status === 'active').length}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Sample Types</span>
                      <span className="text-sm font-medium text-blue-600">
                        {new Set(samples.map(s => s.type)).size}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>Latest sample updates</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {samples.length === 0 ? (
                      <p className="text-sm text-muted-foreground">No recent activity</p>
                    ) : (
                      samples.slice(0, 3).map((sample) => (
                        <div key={sample.id} className="flex justify-between items-center">
                          <span className="text-sm">{sample.type || 'Sample'}</span>
                          <span className="text-sm text-muted-foreground">
                            {sample.created_at ? new Date(sample.created_at).toLocaleDateString() : 'Recent'}
                          </span>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppShell>
  )
}