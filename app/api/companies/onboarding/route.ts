import { NextRequest } from 'next/server'
import { db } from '@/lib/db'
import { companies } from '@/lib/schema-postgres'
import { eq } from 'drizzle-orm'
import { getSession } from '@auth0/nextjs-auth0'
import { createSuccessResponse, createErrorResponse } from '@/lib/api-helpers'

// POST /api/companies/onboarding - Update onboarding step
export async function POST(request: NextRequest) {
  try {
    const session = await getSession(request)
    if (!session?.user) {
      return createErrorResponse('Unauthorized', 401)
    }

    const body = await request.json()
    const { step, completed } = body

    if (!step) {
      return createErrorResponse('Onboarding step is required', 400)
    }

    // Valid onboarding steps
    const validSteps = ['basic_info', 'business_details', 'banking', 'export_info', 'complete']
    if (!validSteps.includes(step)) {
      return createErrorResponse('Invalid onboarding step', 400)
    }

    // Find the company
    const company = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, session.user.sub),
    })

    if (!company) {
      return createErrorResponse('Company profile not found', 404)
    }

    // Update onboarding step
    const updateData: any = {
      onboarding_step: step,
      updated_at: new Date(),
    }

    // If marking as completed, update the completion status
    if (completed === true || step === 'complete') {
      updateData.onboarding_completed = 'true'
      updateData.onboarding_step = 'complete'
    }

    await db.update(companies)
      .set(updateData)
      .where(eq(companies.id, company.id))

    const updatedCompany = await db.query.companies.findFirst({
      where: eq(companies.id, company.id),
    })

    return createSuccessResponse({ 
      company: updatedCompany,
      message: 'Onboarding step updated successfully'
    })
  } catch (error) {
    console.error('Error updating onboarding step:', error)
    return createErrorResponse('Failed to update onboarding step')
  }
}

// GET /api/companies/onboarding - Get onboarding status
export async function GET(request: NextRequest) {
  try {
    const session = await getSession(request)
    if (!session?.user) {
      return createErrorResponse('Unauthorized', 401)
    }

    const company = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, session.user.sub),
    })

    if (!company) {
      return createSuccessResponse({ 
        onboarding_completed: false,
        onboarding_step: 'basic_info',
        company: null
      })
    }

    return createSuccessResponse({ 
      onboarding_completed: company.onboarding_completed === 'true',
      onboarding_step: company.onboarding_step || 'basic_info',
      company
    })
  } catch (error) {
    console.error('Error fetching onboarding status:', error)
    return createErrorResponse('Failed to fetch onboarding status')
  }
}
