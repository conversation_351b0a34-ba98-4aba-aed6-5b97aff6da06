import { NextRequest } from 'next/server'
import { db } from '@/lib/db'
import { companies } from '@/lib/schema-postgres'
import { eq } from 'drizzle-orm'
import { createSuccessResponse, createErrorResponse } from '@/lib/api-helpers'

// GET /api/companies/by-user/[userId] - Get company by Auth0 user ID
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params

    if (!userId) {
      return createErrorResponse('User ID is required', 400)
    }

    const company = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, userId),
    })

    return createSuccessResponse({ 
      company: company || null,
      onboarding_completed: company?.onboarding_completed === 'true' || false
    })
  } catch (error) {
    console.error('Error fetching company by user ID:', error)
    return createErrorResponse('Failed to fetch company profile')
  }
}
