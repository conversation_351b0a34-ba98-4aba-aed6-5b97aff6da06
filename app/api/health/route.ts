import { jsonError, jsonOk } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { customers } from "@/lib/schema-postgres"
import * as Sentry from '@sentry/nextjs'

export async function GET() {
  try {
    let connected = false
    if (process.env.DATABASE_URL) {
      // A simple query to check the connection using schema
      await db.select().from(customers).limit(1)
      connected = true
    }
    return jsonOk({ ok: true, hasDb: !!process.env.DATABASE_URL, connected })
  } catch (e) {
    // Enhanced Sentry error tracking for database connection issues
    Sentry.captureException(e, {
      tags: {
        endpoint: '/api/health',
        operation: 'database_health_check',
      },
      extra: {
        databaseUrl: process.env.DATABASE_URL ? 'SET (length: ' + process.env.DATABASE_URL.length + ')' : 'MISSING',
        databaseUrlPostgresql: process.env.DATABASE_URL_POSTGRESQL ? 'SET (length: ' + process.env.DATABASE_URL_POSTGRESQL.length + ')' : 'MISSING',
        nodeEnv: process.env.NODE_ENV,
        usePostgresql: process.env.USE_POSTGRESQL,
        errorType: e instanceof Error ? e.constructor.name : 'Unknown',
        errorMessage: e instanceof Error ? e.message : String(e),
      }
    })

    return jsonError(e)
  }
}
