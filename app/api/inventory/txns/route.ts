import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { stockTxns } from "@/lib/schema-postgres"
import { desc, eq } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.stockTxns.findMany({
      where: eq(stockTxns.company_id, context.companyId),
      orderBy: [desc(stockTxns.created_at)],
      with: {
        product: true,
      },
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})
