import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { stockLots, stockTxns, products } from "@/lib/schema-postgres"
import { withTransaction } from "@/lib/tx"
import { eq } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { inboundSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { createReferenceNotFoundError } from "@/lib/errors"
import { qualityWorkflowService } from "@/lib/services/quality-workflow"

export async function POST(req: NextRequest) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, inboundSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const lotId = uid("lot")
    const txnId = uid("txn")

    // Check if product exists and get quality requirements
    const product = await db.query.products.findFirst({
      where: eq(products.id, body.product_id)
    })

    if (!product) {
      throw createReferenceNotFoundError("Product", body.product_id)
    }

    // Determine initial quality status based on product requirements
    let initialQualityStatus = "pending"
    if (product.inspection_required === "false") {
      initialQualityStatus = "approved" // Auto-approve if no inspection required
    } else if (product.inspection_required === "conditional") {
      // Could add logic here to determine if inspection is needed based on supplier, quantity, etc.
      initialQualityStatus = "pending"
    }

    await withTransaction(db, async (tx) => {
      await tx.insert(stockLots).values({
        id: lotId,
        product_id: body.product_id,
        qty: body.qty.toString(),
        location: body.location,
        note: body.note,
        quality_status: initialQualityStatus,
        quality_notes: product.inspection_required === "true"
          ? "Inspection required before use"
          : product.inspection_required === "false"
          ? "Auto-approved - no inspection required"
          : "Conditional inspection - review required",
      })
      await tx.insert(stockTxns).values({
        id: txnId,
        type: "inbound",
        product_id: body.product_id,
        qty: body.qty.toString(),
        location: body.location,
        ref: lotId,
      })
    })

    // Trigger quality workflow if inspections are required
    let inspectionIds: string[] = []
    if (product.inspection_required === "true" || product.inspection_required === "conditional") {
      try {
        inspectionIds = await qualityWorkflowService.triggerQualityInspections({
          triggerType: "stock_received",
          entityId: lotId,
          productId: body.product_id,
          quantity: body.qty.toString(),
          notes: `Stock received: ${body.qty} ${product.unit} at ${body.location}`,
        })
      } catch (workflowError) {
        console.warn("Quality workflow trigger failed:", workflowError)
        // Don't fail the entire operation if workflow trigger fails
      }
    }

    const lot = await db.query.stockLots.findFirst({
      where: eq(stockLots.id, lotId),
      with: {
        product: true,
        inspection: {
          with: {
            workOrder: true,
          },
        },
      },
    })

    const response = {
      ...lot,
      qualityInspectionsCreated: inspectionIds.length,
      inspectionIds: inspectionIds,
    }

    return createSuccessResponse(response, "Stock received successfully", 201)
  } catch (error) {
    return createErrorResponse(error)
  }
}
