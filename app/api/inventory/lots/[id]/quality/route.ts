import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { stockLots } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { z } from "zod"
import { 
  createReferenceNotFoundError,
  createInvalidQualityResultError 
} from "@/lib/errors"

const qualityUpdateSchema = z.object({
  quality_status: z.enum(["pending", "approved", "rejected", "quarantined"], {
    errorMap: () => ({ message: "Invalid quality status" })
  }),
  inspection_id: z.string().min(1).optional(),
  quality_notes: z.string().max(1000).optional(),
})

export async function PATCH(req: Request, { params }: { params: { id: string } }) {
  try {
    const body = await req.json()
    const data = qualityUpdateSchema.parse(body)

    // Check if stock lot exists
    const existing = await db.query.stockLots.findFirst({
      where: eq(stockLots.id, params.id),
      with: {
        product: true,
        inspection: true,
      },
    })

    if (!existing) {
      throw createReferenceNotFoundError("Stock Lot", params.id)
    }

    // Validate status transitions
    const validTransitions: Record<string, string[]> = {
      "pending": ["approved", "rejected", "quarantined"],
      "approved": ["quarantined"], // Can quarantine approved stock if issues found
      "rejected": ["pending"], // Can re-test rejected stock
      "quarantined": ["pending", "rejected"] // Can re-evaluate quarantined stock
    }

    if (!validTransitions[existing.quality_status]?.includes(data.quality_status)) {
      throw createInvalidQualityResultError(
        `Cannot change quality status from "${existing.quality_status}" to "${data.quality_status}"`
      )
    }

    // Validate inspection reference if provided
    if (data.inspection_id) {
      const inspection = await db.query.qualityInspections.findFirst({
        where: (inspections, { eq }) => eq(inspections.id, data.inspection_id)
      })
      
      if (!inspection) {
        throw createReferenceNotFoundError("Quality Inspection", data.inspection_id)
      }
    }

    // Update stock lot quality status
    const updated = await db
      .update(stockLots)
      .set({
        quality_status: data.quality_status,
        inspection_id: data.inspection_id,
        quality_notes: data.quality_notes,
      })
      .where(eq(stockLots.id, params.id))
      .returning()

    if (updated.length === 0) {
      throw createReferenceNotFoundError("Stock Lot", params.id)
    }

    // Fetch the updated record with relations
    const lot = await db.query.stockLots.findFirst({
      where: eq(stockLots.id, params.id),
      with: {
        product: true,
        inspection: {
          with: {
            workOrder: true,
          },
        },
      },
    })

    return createSuccessResponse(lot, "Stock lot quality status updated successfully")
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function GET(_: Request, { params }: { params: { id: string } }) {
  try {
    const lot = await db.query.stockLots.findFirst({
      where: eq(stockLots.id, params.id),
      with: {
        product: true,
        inspection: {
          with: {
            workOrder: true,
            defects: true,
            results: {
              with: {
                standard: true,
              },
            },
          },
        },
      },
    })

    if (!lot) {
      throw createReferenceNotFoundError("Stock Lot", params.id)
    }

    return createSuccessResponse(lot)
  } catch (error) {
    return createErrorResponse(error)
  }
}
