import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import {
  products,
  customers,
  samples,
  salesContractItems,
  purchaseContractItems,
  workOrders,
  stockLots,
  stockTxns,
  declarationItems,
  qualityDefects,
  qualityStandards,
  qualityInspections,
  qualityCertificates,
  salesContracts,
  purchaseContracts,
  declarations,
  arInvoices
} from "@/lib/schema-postgres"

/**
 * ADMIN ENDPOINT: Clean up all product and customer-related data
 *
 * This endpoint safely removes all products, customers and their related records
 * in the correct order to avoid foreign key constraint violations.
 *
 * ⚠️  WARNING: This will delete ALL product and customer-related data!
 * Only use this for testing/development environments.
 */
export async function POST() {
  try {
    console.log("🧹 Starting product and customer data cleanup...")

    // Step 1: Delete records that reference customers (must be done before deleting customers)
    console.log("📋 Deleting samples...")
    const deletedSamples = await db.delete(samples).returning({ id: samples.id })
    console.log(`   ✅ Deleted ${deletedSamples.length} samples`)

    console.log("📋 Deleting AR invoices...")
    const deletedArInvoices = await db.delete(arInvoices).returning({ id: arInvoices.id })
    console.log(`   ✅ Deleted ${deletedArInvoices.length} AR invoices`)

    // Step 2: Delete records that reference products (in dependency order)
    console.log("📋 Deleting sales contract items...")
    const deletedSalesContractItems = await db.delete(salesContractItems).returning({ id: salesContractItems.id })
    console.log(`   ✅ Deleted ${deletedSalesContractItems.length} sales contract items`)

    console.log("📋 Deleting purchase contract items...")
    const deletedPurchaseContractItems = await db.delete(purchaseContractItems).returning({ id: purchaseContractItems.id })
    console.log(`   ✅ Deleted ${deletedPurchaseContractItems.length} purchase contract items`)

    console.log("📋 Deleting declaration items...")
    const deletedDeclarationItems = await db.delete(declarationItems).returning({ id: declarationItems.id })
    console.log(`   ✅ Deleted ${deletedDeclarationItems.length} declaration items`)

    console.log("📋 Deleting quality defects...")
    const deletedQualityDefects = await db.delete(qualityDefects).returning({ id: qualityDefects.id })
    console.log(`   ✅ Deleted ${deletedQualityDefects.length} quality defects`)

    console.log("📋 Deleting quality standards...")
    const deletedQualityStandards = await db.delete(qualityStandards).returning({ id: qualityStandards.id })
    console.log(`   ✅ Deleted ${deletedQualityStandards.length} quality standards`)

    console.log("📋 Deleting work orders...")
    const deletedWorkOrders = await db.delete(workOrders).returning({ id: workOrders.id })
    console.log(`   ✅ Deleted ${deletedWorkOrders.length} work orders`)

    console.log("📋 Deleting stock transactions...")
    const deletedStockTxns = await db.delete(stockTxns).returning({ id: stockTxns.id })
    console.log(`   ✅ Deleted ${deletedStockTxns.length} stock transactions`)

    console.log("📋 Deleting stock lots...")
    const deletedStockLots = await db.delete(stockLots).returning({ id: stockLots.id })
    console.log(`   ✅ Deleted ${deletedStockLots.length} stock lots`)

    // Step 3: Delete empty parent records (optional - keeps contracts/declarations that don't have items)
    console.log("📋 Deleting empty sales contracts...")
    const deletedSalesContracts = await db.delete(salesContracts).returning({ id: salesContracts.id })
    console.log(`   ✅ Deleted ${deletedSalesContracts.length} sales contracts`)

    console.log("📋 Deleting empty purchase contracts...")
    const deletedPurchaseContracts = await db.delete(purchaseContracts).returning({ id: purchaseContracts.id })
    console.log(`   ✅ Deleted ${deletedPurchaseContracts.length} purchase contracts`)

    console.log("📋 Deleting empty declarations...")
    const deletedDeclarations = await db.delete(declarations).returning({ id: declarations.id })
    console.log(`   ✅ Deleted ${deletedDeclarations.length} declarations`)

    console.log("📋 Deleting quality inspections...")
    const deletedQualityInspections = await db.delete(qualityInspections).returning({ id: qualityInspections.id })
    console.log(`   ✅ Deleted ${deletedQualityInspections.length} quality inspections`)

    console.log("📋 Deleting quality certificates...")
    const deletedQualityCertificates = await db.delete(qualityCertificates).returning({ id: qualityCertificates.id })
    console.log(`   ✅ Deleted ${deletedQualityCertificates.length} quality certificates`)

    // Step 4: Delete all products and customers
    console.log("🎯 Deleting all products...")
    const deletedProducts = await db.delete(products).returning({ id: products.id, name: products.name })
    console.log(`   ✅ Deleted ${deletedProducts.length} products`)

    console.log("👥 Deleting all customers...")
    const deletedCustomers = await db.delete(customers).returning({ id: customers.id, name: customers.name })
    console.log(`   ✅ Deleted ${deletedCustomers.length} customers`)

    // Summary
    const summary = {
      message: "Product and customer data cleanup completed successfully",
      deletedCounts: {
        customers: deletedCustomers.length,
        products: deletedProducts.length,
        samples: deletedSamples.length,
        arInvoices: deletedArInvoices.length,
        salesContractItems: deletedSalesContractItems.length,
        purchaseContractItems: deletedPurchaseContractItems.length,
        declarationItems: deletedDeclarationItems.length,
        qualityDefects: deletedQualityDefects.length,
        qualityStandards: deletedQualityStandards.length,
        workOrders: deletedWorkOrders.length,
        stockTxns: deletedStockTxns.length,
        stockLots: deletedStockLots.length,
        salesContracts: deletedSalesContracts.length,
        purchaseContracts: deletedPurchaseContracts.length,
        declarations: deletedDeclarations.length,
        qualityInspections: deletedQualityInspections.length,
        qualityCertificates: deletedQualityCertificates.length
      },
      deletedProducts: deletedProducts.map(p => ({ id: p.id, name: p.name })),
      deletedCustomers: deletedCustomers.map(c => ({ id: c.id, name: c.name }))
    }

    console.log("🎉 Cleanup completed successfully!")
    console.log("📊 Summary:", summary.deletedCounts)

    return jsonOk(summary)

  } catch (error) {
    console.error("❌ Cleanup failed:", error)
    return jsonError(error)
  }
}
