import { jsonError, jsonOk } from "@/lib/api-helpers"
import { clearSampleData, seedSampleData } from "@/lib/sample-data"

export async function POST() {
  try {
    await clearSampleData()
    await seedSampleData()
    return jsonOk({ ok: true, action: "reset" })
  } catch (e) {
    return jsonError(e)
  }
}

export async function DELETE() {
  try {
    await clearSampleData()
    return jsonOk({ ok: true, action: "clear" })
  } catch (e) {
    return jsonError(e)
  }
}
