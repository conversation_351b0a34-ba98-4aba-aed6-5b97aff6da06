import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { 
  customers,
  samples,
  salesContracts,
  arInvoices,
  salesContractItems,
  workOrders
} from "@/lib/schema-postgres"

/**
 * ADMIN ENDPOINT: Clean up all customer-related data only
 * 
 * This endpoint safely removes all customers and their related records
 * in the correct order to avoid foreign key constraint violations.
 * 
 * ⚠️  WARNING: This will delete ALL customer-related data!
 * Only use this for testing/development environments.
 */
export async function POST() {
  try {
    console.log("🧹 Starting customer data cleanup...")
    
    // Step 1: Delete records that reference customers (in dependency order)
    console.log("📋 Deleting samples...")
    const deletedSamples = await db.delete(samples).returning({ id: samples.id })
    console.log(`   ✅ Deleted ${deletedSamples.length} samples`)

    console.log("📋 Deleting AR invoices...")
    const deletedArInvoices = await db.delete(arInvoices).returning({ id: arInvoices.id })
    console.log(`   ✅ Deleted ${deletedArInvoices.length} AR invoices`)

    // Step 2: Delete sales contract items (which reference sales contracts)
    console.log("📋 Deleting sales contract items...")
    const deletedSalesContractItems = await db.delete(salesContractItems).returning({ id: salesContractItems.id })
    console.log(`   ✅ Deleted ${deletedSalesContractItems.length} sales contract items`)

    // Step 3: Delete work orders (which reference sales contracts)
    console.log("📋 Deleting work orders...")
    const deletedWorkOrders = await db.delete(workOrders).returning({ id: workOrders.id })
    console.log(`   ✅ Deleted ${deletedWorkOrders.length} work orders`)

    // Step 4: Delete sales contracts (which reference customers)
    console.log("📋 Deleting sales contracts...")
    const deletedSalesContracts = await db.delete(salesContracts).returning({ id: salesContracts.id })
    console.log(`   ✅ Deleted ${deletedSalesContracts.length} sales contracts`)

    // Step 5: Finally delete all customers
    console.log("👥 Deleting all customers...")
    const deletedCustomers = await db.delete(customers).returning({ id: customers.id, name: customers.name })
    console.log(`   ✅ Deleted ${deletedCustomers.length} customers`)

    // Summary
    const summary = {
      message: "Customer data cleanup completed successfully",
      deletedCounts: {
        customers: deletedCustomers.length,
        samples: deletedSamples.length,
        arInvoices: deletedArInvoices.length,
        salesContractItems: deletedSalesContractItems.length,
        workOrders: deletedWorkOrders.length,
        salesContracts: deletedSalesContracts.length
      },
      deletedCustomers: deletedCustomers.map(c => ({ id: c.id, name: c.name }))
    }

    console.log("🎉 Customer cleanup completed successfully!")
    console.log("📊 Summary:", summary.deletedCounts)

    return jsonOk(summary)

  } catch (error) {
    console.error("❌ Customer cleanup failed:", error)
    return jsonError(error)
  }
}
