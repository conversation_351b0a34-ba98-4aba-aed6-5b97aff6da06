import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { suppliers } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      orderBy: [desc(suppliers.created_at)],
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: Request, context) {
  try {
    const body = await req.json()
    const id = uid("supp")
    const newSupplier = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      ...body,
      status: "active",
    }

    await db.insert(suppliers).values(newSupplier)

    // 🛡️ SECURE: Only return supplier if it belongs to current company
    const row = await db.query.suppliers.findFirst({
      where: and(
        eq(suppliers.id, id),
        eq(suppliers.company_id, context.companyId)
      ),
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
})
