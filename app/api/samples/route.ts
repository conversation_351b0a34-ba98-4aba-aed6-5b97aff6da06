import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { samples } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.samples.findMany({
      where: eq(samples.company_id, context.companyId),
      orderBy: [desc(samples.created_at)],
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: Request, context) {
  try {
    const body = await req.json()
    const id = uid("smp")
    const newSample = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      ...body,
      available_from_stock: body.available_from_stock ?? false,
    }

    await db.insert(samples).values(newSample)

    // 🛡️ SECURE: Only return sample if it belongs to current company
    const row = await db.query.samples.findFirst({
      where: and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ),
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
})
