import { db } from "@/lib/db"
import { jsonError } from "@/lib/api-helpers"
import { samples } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { withTenantAuth, createForbiddenResponse } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant DELETE endpoint with proper isolation
export const DELETE = withTenantAuth(async function DELETE(_: Request, context, { params }: { params: { id: string } }) {
  try {
    // 🛡️ CRITICAL: Verify sample belongs to current company before deleting
    const existingSample = await db.query.samples.findFirst({
      where: and(
        eq(samples.id, params.id),
        eq(samples.company_id, context.companyId)
      ),
    })

    if (!existingSample) {
      return createForbiddenResponse()
    }

    await db.delete(samples).where(and(
      eq(samples.id, params.id),
      eq(samples.company_id, context.companyId) // 🛡️ Double-check tenant isolation
    ))

    return new Response(null, { status: 204 })
  } catch (e) {
    return jsonError(e)
  }
})
