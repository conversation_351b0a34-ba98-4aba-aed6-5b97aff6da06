import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { workOrders, workOperations } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { z } from "zod"
import { qualityWorkflowService } from "@/lib/services/quality-workflow"
import {
  createReferenceNotFoundError,
  createInvalidWorkflowTransitionError
} from "@/lib/errors"

const patchSchema = z.object({
  workOperationId: z.string().optional(),
  status: z.string(),
  action: z.enum(["update_operation", "start_production", "complete_production"]).optional(),
})

export async function GET(_: Request, { params }: { params: { id: string } }) {
  try {
    const workOrder = await db.query.workOrders.findFirst({
      where: eq(workOrders.id, params.id),
      with: {
        salesContract: {
          with: {
            customer: true,
          },
        },
        product: true,
        qualityInspections: {
          with: {
            defects: true,
            results: true,
          },
        },
      },
    })

    if (!workOrder) {
      throw createReferenceNotFoundError("Work Order", params.id)
    }

    return createSuccessResponse(workOrder)
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function PATCH(req: Request, { params }: { params: { id: string } }) {
  try {
    const body = await req.json()
    const data = patchSchema.parse(body)

    // Handle different types of updates
    if (data.action === "update_operation" && data.workOperationId) {
      // Update work operation status
      const updated = await db
        .update(workOperations)
        .set({ status: data.status })
        .where(eq(workOperations.id, data.workOperationId))
        .returning()

      return createSuccessResponse(updated[0], "Operation status updated")

    } else if (data.action === "start_production") {
      // Check quality gate before starting production
      const canStart = await qualityWorkflowService.validateQualityGate(params.id, "start_production")

      if (!canStart) {
        throw createInvalidWorkflowTransitionError(
          "Cannot start production: Quality approval required"
        )
      }

      const updated = await db
        .update(workOrders)
        .set({ status: "in_progress" })
        .where(eq(workOrders.id, params.id))
        .returning()

      return createSuccessResponse(updated[0], "Production started")

    } else if (data.action === "complete_production") {
      // Check quality gate before completing production
      const canComplete = await qualityWorkflowService.validateQualityGate(params.id, "complete_production")

      if (!canComplete) {
        throw createInvalidWorkflowTransitionError(
          "Cannot complete production: Quality approval required"
        )
      }

      const updated = await db
        .update(workOrders)
        .set({ status: "completed" })
        .where(eq(workOrders.id, params.id))
        .returning()

      return createSuccessResponse(updated[0], "Production completed")

    } else {
      // Legacy support: update work operation if workOperationId provided
      if (data.workOperationId) {
        const updated = await db
          .update(workOperations)
          .set({ status: data.status })
          .where(eq(workOperations.id, data.workOperationId))
          .returning()

        return createSuccessResponse(updated[0], "Operation status updated")
      } else {
        throw createReferenceNotFoundError("Work Operation or Action", "N/A")
      }
    }
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function DELETE(_: Request, { params }: { params: { id: string } }) {
  try {
    await db.delete(workOrders).where(eq(workOrders.id, params.id))
    return new Response(null, { status: 204 })
  } catch (e) {
    return jsonError(e)
  }
}
