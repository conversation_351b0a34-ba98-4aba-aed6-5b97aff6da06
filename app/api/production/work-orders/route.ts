import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { workOrders, qualityInspections, products } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { withTransaction } from "@/lib/tx"
import { createReferenceNotFoundError } from "@/lib/errors"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.workOrders.findMany({
      where: eq(workOrders.company_id, context.companyId),
      orderBy: [desc(workOrders.created_at)],
      with: {
        salesContract: true,
        product: true,
      },
    })
    return createSuccessResponse(rows)
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: Request, context) {
  try {
    const body = await req.json()
    const id = uid("wo")

    // 🛡️ CRITICAL: Validate that product exists and belongs to current company
    const product = await db.query.products.findFirst({
      where: and(
        eq(products.id, body.product_id),
        eq(products.company_id, context.companyId)
      )
    })

    if (!product) {
      throw createReferenceNotFoundError("Product", body.product_id)
    }

    // Determine quality requirements for this work order
    let qualityRequired = "auto" // Default
    let qualityStatus = "pending"

    if (product.inspection_required === "true") {
      qualityRequired = "required"
    } else if (product.inspection_required === "false") {
      qualityRequired = "not_required"
      qualityStatus = "approved" // Auto-approve if no inspection required
    }

    const newWorkOrder = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      number: body.number,
      sales_contract_id: body.sales_contract_id,
      product_id: body.product_id,
      qty: body.qty,
      quality_required: qualityRequired,
      quality_status: qualityStatus,
    }

    const operations = (body.operations || []).map((op: any) => ({
      id: uid("wop"),
      work_order_id: id,
      name: op.name,
      status: "not-started",
    }))

    // Auto-create quality inspections if required
    const inspections = []
    if (qualityRequired === "required" || qualityRequired === "auto") {
      // Create standard inspections based on product type
      const inspectionTypes = ["in-process", "final"]

      for (const inspectionType of inspectionTypes) {
        inspections.push({
          id: uid("qi"),
          company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
          work_order_id: id,
          inspection_type: inspectionType,
          inspector: "TBD", // To be assigned
          status: "scheduled",
          notes: `Auto-created ${inspectionType} inspection for work order ${body.number}`,
        })
      }
    }

    await withTransaction(db, async (tx) => {
      await tx.insert(workOrders).values(newWorkOrder)

      if (operations.length > 0) {
        await tx.insert(workOperations).values(operations)
      }

      if (inspections.length > 0) {
        await tx.insert(qualityInspections).values(inspections)
      }
    })

    // 🛡️ SECURE: Only return work order if it belongs to current company
    const row = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId)
      ),
      with: {
        salesContract: true,
        product: true,
        operations: true,
        qualityInspections: true,
      },
    })
    return createSuccessResponse(row, "Work order created successfully", 201)
  } catch (error) {
    return createErrorResponse(error)
  }
})
