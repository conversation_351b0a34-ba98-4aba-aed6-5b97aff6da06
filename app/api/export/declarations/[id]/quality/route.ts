import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { declarations, declarationItems, qualityCertificates } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"
import { 
  createReferenceNotFoundError,
  createInvalidQualityResultError 
} from "@/lib/errors"

const qualityValidationSchema = z.object({
  autoAssignCertificates: z.boolean().default(false),
  requireValidCertificates: z.boolean().default(true),
})

export async function GET(_: Request, { params }: { params: { id: string } }) {
  try {
    // Get declaration with all items and their quality status
    const declaration = await db.query.declarations.findFirst({
      where: eq(declarations.id, params.id),
      with: {
        items: {
          with: {
            product: true,
            qualityCertificate: {
              with: {
                inspection: {
                  with: {
                    workOrder: {
                      with: {
                        product: true,
                      },
                    },
                    defects: true,
                    results: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    if (!declaration) {
      throw createReferenceNotFoundError("Declaration", params.id)
    }

    // Analyze quality status for each item
    const qualityAnalysis = await analyzeDeclarationQuality(declaration)

    return createSuccessResponse({
      declaration: {
        id: declaration.id,
        number: declaration.number,
        status: declaration.status,
      },
      qualityAnalysis,
      canExport: qualityAnalysis.overallStatus === "approved",
      generatedAt: new Date().toISOString(),
    })
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function POST(req: Request, { params }: { params: { id: string } }) {
  try {
    const body = await req.json()
    const data = qualityValidationSchema.parse(body)

    // Get declaration with items
    const declaration = await db.query.declarations.findFirst({
      where: eq(declarations.id, params.id),
      with: {
        items: {
          with: {
            product: true,
          },
        },
      },
    })

    if (!declaration) {
      throw createReferenceNotFoundError("Declaration", params.id)
    }

    const results = []

    for (const item of declaration.items) {
      let result = {
        itemId: item.id,
        productName: item.product.name,
        action: "no_action",
        certificateId: null,
        status: "pending",
        message: "",
      }

      if (data.autoAssignCertificates) {
        // Find the most recent valid certificate for this product
        const certificate = await findValidCertificateForProduct(item.product_id)
        
        if (certificate) {
          // Assign certificate to declaration item
          await db
            .update(declarationItems)
            .set({
              quality_certificate_id: certificate.id,
              quality_status: "approved",
              quality_notes: `Auto-assigned certificate ${certificate.certificate_number}`,
            })
            .where(eq(declarationItems.id, item.id))

          result = {
            ...result,
            action: "certificate_assigned",
            certificateId: certificate.id,
            status: "approved",
            message: `Assigned certificate ${certificate.certificate_number}`,
          }
        } else {
          result = {
            ...result,
            action: "no_certificate_found",
            status: "rejected",
            message: "No valid quality certificate found for this product",
          }
        }
      }

      results.push(result)
    }

    // Update declaration status based on results
    const allApproved = results.every(r => r.status === "approved")
    const newDeclarationStatus = allApproved ? "quality_approved" : "quality_pending"

    return createSuccessResponse({
      declarationId: params.id,
      qualityValidationResults: results,
      overallStatus: allApproved ? "approved" : "pending",
      message: allApproved 
        ? "All items have valid quality certificates - ready for export"
        : "Some items missing quality certificates - export blocked",
    })
  } catch (error) {
    return createErrorResponse(error)
  }
}

async function analyzeDeclarationQuality(declaration: any) {
  const itemAnalysis = []
  let overallStatus = "approved"

  for (const item of declaration.items) {
    const analysis = {
      itemId: item.id,
      productId: item.product.id,
      productName: item.product.name,
      quantity: item.qty,
      qualityStatus: item.quality_status || "pending",
      certificateStatus: "none",
      certificateDetails: null,
      issues: [],
      recommendations: [],
    }

    // Check if product requires quality certification
    if (item.product.inspection_required === "true") {
      if (!item.quality_certificate_id) {
        analysis.issues.push("No quality certificate assigned")
        analysis.recommendations.push("Assign a valid quality certificate")
        overallStatus = "pending"
      } else if (item.qualityCertificate) {
        // Check certificate validity
        const cert = item.qualityCertificate
        const today = new Date().toISOString().split('T')[0]
        
        if (cert.valid_until && cert.valid_until < today) {
          analysis.certificateStatus = "expired"
          analysis.issues.push(`Certificate ${cert.certificate_number} expired on ${cert.valid_until}`)
          analysis.recommendations.push("Obtain a new quality certificate")
          overallStatus = "rejected"
        } else {
          analysis.certificateStatus = "valid"
          analysis.certificateDetails = {
            certificateNumber: cert.certificate_number,
            certificateType: cert.certificate_type,
            issuedDate: cert.issued_date,
            validUntil: cert.valid_until,
            inspectionPassed: cert.inspection?.status === "completed",
            defectCount: cert.inspection?.defects?.length || 0,
          }

          // Check for critical defects
          const criticalDefects = cert.inspection?.defects?.filter(d => d.severity === "critical") || []
          if (criticalDefects.length > 0) {
            analysis.issues.push(`${criticalDefects.length} critical defects found`)
            analysis.recommendations.push("Resolve critical defects before export")
            overallStatus = "rejected"
          }
        }
      }
    } else if (item.product.inspection_required === "false") {
      analysis.certificateStatus = "not_required"
    } else {
      // Conditional inspection required
      analysis.recommendations.push("Review if quality certificate is needed for this export")
    }

    itemAnalysis.push(analysis)
  }

  return {
    overallStatus,
    itemCount: declaration.items.length,
    approvedItems: itemAnalysis.filter(i => i.certificateStatus === "valid" || i.certificateStatus === "not_required").length,
    pendingItems: itemAnalysis.filter(i => i.qualityStatus === "pending").length,
    rejectedItems: itemAnalysis.filter(i => i.certificateStatus === "expired" || i.issues.length > 0).length,
    items: itemAnalysis,
  }
}

async function findValidCertificateForProduct(productId: string) {
  // Find the most recent valid certificate for products of this type
  const certificates = await db.query.qualityCertificates.findMany({
    where: eq(qualityCertificates.certificate_type, "COC"), // Certificate of Compliance for export
    with: {
      inspection: {
        with: {
          workOrder: {
            with: {
              product: true,
            },
          },
        },
      },
    },
    orderBy: (certs, { desc }) => [desc(certs.created_at)],
  })

  // Filter for the specific product and valid certificates
  const today = new Date().toISOString().split('T')[0]
  const validCertificates = certificates.filter(cert => {
    return (
      cert.inspection?.workOrder?.product?.id === productId &&
      (!cert.valid_until || cert.valid_until >= today) &&
      cert.inspection?.status === "completed"
    )
  })

  return validCertificates[0] || null
}
