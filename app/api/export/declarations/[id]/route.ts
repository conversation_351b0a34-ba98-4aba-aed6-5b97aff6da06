import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { declarations } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { z } from "zod"

const patchSchema = z.object({
  status: z.string().min(1),
})

export async function PATCH(req: Request, { params }: { params: { id: string } }) {
  try {
    const body = await req.json()
    const data = patchSchema.parse(body)

    const updated = await db
      .update(declarations)
      .set({ status: data.status })
      .where(eq(declarations.id, params.id))
      .returning()

    return jsonOk(updated[0])
  } catch (e) {
    return jsonError(e)
  }
}

export async function DELETE(_: Request, { params }: { params: { id: string } }) {
  try {
    await db.delete(declarations).where(eq(declarations.id, params.id))
    return new Response(null, { status: 204 })
  } catch (e) {
    return jsonError(e)
  }
}
