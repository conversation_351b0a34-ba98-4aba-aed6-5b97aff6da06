import { jsonError, jsonOk } from "@/lib/api-helpers"
import { db, uid } from "@/lib/db"
import { documents } from "@/lib/schema-postgres"
import { put, del } from "@vercel/blob"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

export async function POST(req: Request, { params }: { params: { id: string } }) {
  const declarationId = params.id
  if (!req.body) {
    return jsonError("No file in request", 400)
  }

  const filename = req.headers.get("x-vercel-filename") || "document.bin"

  try {
    const blob = await put(filename, req.body, {
      access: "public",
    })

    const docId = uid("doc")
    await db.insert(documents).values({
      id: docId,
      filename: filename,
      url: blob.url,
      filetype: blob.contentType,
      declarationId: declarationId,
    })

    const newDoc = await db.query.documents.findFirst({
      where: eq(documents.id, docId),
    })

    return jsonOk(newDoc, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
}

const deleteSchema = z.object({
  documentId: z.string(),
  url: z.string().url(),
})

export async function DELETE(req: Request) {
  try {
    const body = await req.json()
    const data = deleteSchema.parse(body)

    // Delete from Vercel Blob storage
    await del(data.url)

    // Delete from our database
    await db.delete(documents).where(eq(documents.id, data.documentId))

    return new Response(null, { status: 204 })
  } catch (e) {
    return jsonError(e)
  }
}
