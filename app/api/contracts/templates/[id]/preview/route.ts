import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { contractTemplates } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { processTemplateContent, generateSalesContractVariables, generatePurchaseContractVariables } from "@/lib/template-processor"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

export const POST = withTenantAuth(async function POST(req: NextRequest, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    // Get template with tenant isolation
    const template = await db.query.contractTemplates.findFirst({
      where: and(
        eq(contractTemplates.id, id),
        eq(contractTemplates.company_id, context.companyId)
      ),
    })

    if (!template) {
      return createErrorResponse("Template not found", 404)
    }

    // Get contract data from request body
    let body;
    try {
      const text = await req.text();
      body = text ? JSON.parse(text) : {};
    } catch (error) {
      console.error('Failed to parse request body:', error);
      body = {};
    }

    console.log('Preview request body:', body);

    let variables

    if (template.type === "sales") {
      variables = await generateSalesContractVariables({
        number: body.number || "SC-PREVIEW-001",
        customer_id: body.customer_id || body.customerId || "",
        date: body.date || new Date().toISOString().split('T')[0],
        status: body.status || "draft",
        currency: body.currency || template.currency,
        payment_terms: body.payment_terms || template.payment_terms,
        delivery_terms: body.delivery_terms || template.delivery_terms,
        items: body.items || [],
        company_id: context.companyId // 🔥 NEW: Include company data
      })
    } else {
      variables = await generatePurchaseContractVariables({
        number: body.number || "PC-PREVIEW-001",
        supplier_id: body.supplier_id || body.supplierId || "",
        date: body.date || new Date().toISOString().split('T')[0],
        status: body.status || "draft",
        currency: body.currency || template.currency,
        payment_terms: body.payment_terms || template.payment_terms,
        delivery_terms: body.delivery_terms || template.delivery_terms,
        items: body.items || [],
        company_id: context.companyId // 🔥 NEW: Include company data
      })
    }

    // Process template content
    const processedContent = await processTemplateContent(template.content, variables)

    return createSuccessResponse({
      template: {
        id: template.id,
        name: template.name,
        type: template.type,
        original_content: template.content,
        processed_content: processedContent,
      },
      variables,
    })
  } catch (error) {
    return createErrorResponse(error)
  }
})
