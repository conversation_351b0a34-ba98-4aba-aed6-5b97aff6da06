import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { contractTemplates } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { contractTemplateSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth, createForbiddenResponse } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(_: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const template = await db.query.contractTemplates.findFirst({
      where: and(
        eq(contractTemplates.id, id),
        eq(contractTemplates.company_id, context.companyId)
      ),
    })

    if (!template) {
      return createErrorResponse("Template not found", 404)
    }

    return createSuccessResponse(template)
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant PUT endpoint with proper isolation
export const PUT = withTenantAuth(async function PUT(req: NextRequest, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    // 🛡️ CRITICAL: Check if template exists and belongs to current company
    const existingTemplate = await db.query.contractTemplates.findFirst({
      where: and(
        eq(contractTemplates.id, id),
        eq(contractTemplates.company_id, context.companyId)
      ),
    })

    if (!existingTemplate) {
      return createForbiddenResponse()
    }

    // Validate request body
    const validation = await validateRequestBody(req, contractTemplateSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data

    // Update template with tenant isolation
    await db.update(contractTemplates)
      .set({
        name: body.name,
        type: body.type,
        content: body.content,
        currency: body.currency,
        payment_terms: body.payment_terms,
        delivery_terms: body.delivery_terms,
        language: body.language || "en",
        version: body.version || existingTemplate.version,
        is_active: body.is_active !== false,
        updated_at: new Date(),
      })
      .where(and(
        eq(contractTemplates.id, id),
        eq(contractTemplates.company_id, context.companyId) // 🛡️ Double-check tenant isolation
      ))

    // Return updated template with tenant isolation
    const updatedTemplate = await db.query.contractTemplates.findFirst({
      where: and(
        eq(contractTemplates.id, id),
        eq(contractTemplates.company_id, context.companyId)
      ),
    })

    return createSuccessResponse(updatedTemplate, "Template updated successfully")
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant DELETE endpoint with proper isolation
export const DELETE = withTenantAuth(async function DELETE(_: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    // 🛡️ CRITICAL: Check if template exists and belongs to current company
    const existingTemplate = await db.query.contractTemplates.findFirst({
      where: and(
        eq(contractTemplates.id, id),
        eq(contractTemplates.company_id, context.companyId)
      ),
    })

    if (!existingTemplate) {
      return createForbiddenResponse()
    }

    // Delete template with tenant isolation
    await db.delete(contractTemplates).where(and(
      eq(contractTemplates.id, id),
      eq(contractTemplates.company_id, context.companyId) // 🛡️ Double-check tenant isolation
    ))

    return new Response(null, { status: 204 })
  } catch (error) {
    return createErrorResponse(error)
  }
})
