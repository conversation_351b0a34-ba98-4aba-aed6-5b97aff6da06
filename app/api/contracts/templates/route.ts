import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { contractTemplates } from "@/lib/schema-postgres"
import { desc, eq, and, like } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { contractTemplateSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(req: NextRequest, context) {
  try {
    const { searchParams } = new URL(req.url)
    const type = searchParams.get("type")
    const search = searchParams.get("search")
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const offset = (page - 1) * limit

    // Build where conditions with tenant isolation
    const conditions = [eq(contractTemplates.company_id, context.companyId)] // 🛡️ CRITICAL: Tenant filtering
    if (type && (type === "sales" || type === "purchase")) {
      conditions.push(eq(contractTemplates.type, type))
    }
    if (search) {
      conditions.push(like(contractTemplates.name, `%${search}%`))
    }

    const whereClause = and(...conditions)

    // Get templates with pagination
    const templates = await db.query.contractTemplates.findMany({
      where: whereClause,
      orderBy: [desc(contractTemplates.created_at)],
      limit,
      offset,
    })

    // Get total count for pagination
    const totalTemplates = await db.select({ count: contractTemplates.id }).from(contractTemplates).where(whereClause)
    const total = totalTemplates.length

    return createSuccessResponse({
      templates,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, contractTemplateSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("ct")

    const newTemplate = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      name: body.name,
      type: body.type,
      content: body.content,
      currency: body.currency,
      payment_terms: body.payment_terms,
      delivery_terms: body.delivery_terms,
      language: body.language || "en",
      version: body.version || 1,
      is_active: body.is_active !== false, // Default to true
    }

    // Insert template
    await db.insert(contractTemplates).values(newTemplate)

    // Return created template with tenant isolation
    const createdTemplate = await db.query.contractTemplates.findFirst({
      where: and(
        eq(contractTemplates.id, id),
        eq(contractTemplates.company_id, context.companyId)
      ),
    })

    return createSuccessResponse(createdTemplate, "Contract template created successfully", 201)
  } catch (error) {
    return createErrorResponse(error)
  }
})
