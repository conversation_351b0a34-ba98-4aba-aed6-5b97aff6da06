import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { purchaseContracts, purchaseContractItems } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { purchaseContractSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.purchaseContracts.findMany({
      where: eq(purchaseContracts.company_id, context.companyId),
      orderBy: [desc(purchaseContracts.created_at)],
      with: {
        supplier: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })
    return createSuccessResponse(rows)
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, purchaseContractSchema)

    if (!validation.success) {
      console.error("Validation failed:", validation.error.issues) // Debug log
      return createValidationErrorResponse(validation.error.issues)
    }

    console.log("Validated purchase contract data:", validation.data) // Debug log

    const body = validation.data
    console.log("Validated purchase contract data:", body)
    const id = uid("pc")

    const newContract = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      number: body.number,
      supplier_id: body.supplierId || body.supplier_id, // Support both field names
      template_id: (body.template_id || body.templateId) && (body.template_id || body.templateId) !== "none" ? (body.template_id || body.templateId) : null,
      date: body.date || new Date().toISOString().split('T')[0],
      status: body.status || "draft",
      currency: body.currency,
    }
    const items = (body.items || []).map((item) => ({
      id: uid("pci"),
      contract_id: id,
      product_id: item.productId || item.product_id, // Support both field names
      qty: item.qty.toString(),
      price: item.price.toString(),
    }))

    // Insert contract
    await db.insert(purchaseContracts).values(newContract)

    // Insert contract items if any
    if (items.length > 0) {
      await db.insert(purchaseContractItems).values(items)
    }

    // 🛡️ SECURE: Only return contract if it belongs to current company
    const row = await db.query.purchaseContracts.findFirst({
      where: and(
        eq(purchaseContracts.id, id),
        eq(purchaseContracts.company_id, context.companyId)
      ),
      with: {
        supplier: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })
    return createSuccessResponse(row, "Purchase contract created successfully", 201)
  } catch (error) {
    return createErrorResponse(error)
  }
})
