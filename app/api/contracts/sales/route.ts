import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { salesContracts, salesContractItems } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { salesContractSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.salesContracts.findMany({
      where: eq(salesContracts.company_id, context.companyId),
      orderBy: [desc(salesContracts.created_at)],
      with: {
        customer: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })
    return createSuccessResponse(rows)
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, salesContractSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    console.log("Validated sales contract data:", body)
    const id = uid("sc")

    const newContract = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      number: body.number,
      customer_id: body.customer_id || body.customerId, // Handle both field names
      template_id: (body.template_id || body.templateId) && (body.template_id || body.templateId) !== "none" ? (body.template_id || body.templateId) : null,
      date: body.date || new Date().toISOString().split('T')[0],
      status: body.status || "draft",
      currency: body.currency,
    }
    const items = (body.items || []).map((item) => ({
      id: uid("sci"),
      contract_id: id,
      product_id: item.product_id || item.productId, // Handle both field names
      qty: item.qty.toString(),
      price: item.price.toString(),
    }))

    // Insert contract
    await db.insert(salesContracts).values(newContract)

    // Insert contract items if any
    if (items.length > 0) {
      await db.insert(salesContractItems).values(items)
    }

    // 🛡️ SECURE: Only return contract if it belongs to current company
    const row = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, id),
        eq(salesContracts.company_id, context.companyId)
      ),
      with: {
        customer: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })
    return createSuccessResponse(row, "Sales contract created successfully", 201)
  } catch (error) {
    return createErrorResponse(error)
  }
})
