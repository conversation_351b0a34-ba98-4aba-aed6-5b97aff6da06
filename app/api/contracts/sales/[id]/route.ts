import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { salesContracts, salesContractItems } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { salesContractSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth, createForbiddenResponse } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(_: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const contract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, id),
        eq(salesContracts.company_id, context.companyId)
      ),
      with: {
        customer: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })

    if (!contract) {
      return createErrorResponse("Contract not found", 404)
    }

    return createSuccessResponse(contract)
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant PUT endpoint with proper isolation
export const PUT = withTenantAuth(async function PUT(req: NextRequest, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    // 🛡️ CRITICAL: Verify contract belongs to current company before updating
    const existingContract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, id),
        eq(salesContracts.company_id, context.companyId)
      ),
    })

    if (!existingContract) {
      return createForbiddenResponse()
    }

    // Validate request body
    const validation = await validateRequestBody(req, salesContractSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data

    // Update contract with tenant isolation
    await db.update(salesContracts)
      .set({
        number: body.number,
        customer_id: body.customer_id || body.customerId,
        date: body.date || new Date().toISOString().split('T')[0],
        status: body.status || "draft",
        currency: body.currency,
      })
      .where(and(
        eq(salesContracts.id, id),
        eq(salesContracts.company_id, context.companyId) // 🛡️ Double-check tenant isolation
      ))

    // Delete existing items and insert new ones
    await db.delete(salesContractItems).where(eq(salesContractItems.contract_id, id))

    const items = (body.items || []).map((item) => ({
      id: uid("sci"),
      contract_id: id,
      product_id: item.product_id || item.productId,
      qty: item.qty.toString(),
      price: item.price.toString(),
    }))

    if (items.length > 0) {
      await db.insert(salesContractItems).values(items)
    }

    // Return updated contract with tenant isolation
    const updatedContract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, id),
        eq(salesContracts.company_id, context.companyId)
      ),
      with: {
        customer: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })

    return createSuccessResponse(updatedContract, "Sales contract updated successfully")
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant DELETE endpoint with proper isolation
export const DELETE = withTenantAuth(async function DELETE(_: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    // 🛡️ CRITICAL: Verify contract belongs to current company before deleting
    const existingContract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, id),
        eq(salesContracts.company_id, context.companyId)
      ),
    })

    if (!existingContract) {
      return createForbiddenResponse()
    }

    // First delete contract items, then the contract with tenant isolation
    await db.delete(salesContractItems).where(eq(salesContractItems.contract_id, id))
    await db.delete(salesContracts).where(and(
      eq(salesContracts.id, id),
      eq(salesContracts.company_id, context.companyId) // 🛡️ Double-check tenant isolation
    ))

    return new Response(null, { status: 204 })
  } catch (error) {
    return createErrorResponse(error)
  }
})
