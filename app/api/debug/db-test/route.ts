import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { companies } from '@/lib/schema-postgres'

/**
 * Debug Database Connection Test
 * 
 * This endpoint tests the database connection and provides detailed error information
 * for debugging production issues.
 */
export async function GET(req: NextRequest) {
  try {
    console.log('🔍 Testing database connection...')
    
    // Test 1: Basic database connection
    console.log('📊 Test 1: Basic connection test')
    const basicTest = await db.execute('SELECT 1 as test')
    console.log('✅ Basic connection successful:', basicTest)
    
    // Test 2: Companies table query
    console.log('📊 Test 2: Companies table query')
    const companiesCount = await db.select().from(companies).limit(1)
    console.log('✅ Companies table accessible, sample:', companiesCount)
    
    // Test 3: Environment variables check
    console.log('📊 Test 3: Environment variables')
    const envCheck = {
      DATABASE_URL: !!process.env.DATABASE_URL ? 'SET' : 'MISSING',
      DATABASE_URL_POSTGRESQL: !!process.env.DATABASE_URL_POSTGRESQL ? 'SET' : 'MISSING',
      NODE_ENV: process.env.NODE_ENV,
      USE_POSTGRESQL: process.env.USE_POSTGRESQL,
    }
    console.log('🔧 Environment check:', envCheck)
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      tests: {
        basicConnection: 'PASSED',
        companiesTable: 'PASSED',
        companiesCount: companiesCount.length,
        environment: envCheck
      },
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('❌ Database test failed:', error)
    
    // Detailed error information
    const errorInfo = {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
      cause: error instanceof Error ? error.cause : undefined,
    }
    
    // Environment info for debugging
    const envInfo = {
      DATABASE_URL: process.env.DATABASE_URL ? 'SET (length: ' + process.env.DATABASE_URL.length + ')' : 'MISSING',
      DATABASE_URL_POSTGRESQL: process.env.DATABASE_URL_POSTGRESQL ? 'SET (length: ' + process.env.DATABASE_URL_POSTGRESQL.length + ')' : 'MISSING',
      NODE_ENV: process.env.NODE_ENV,
      USE_POSTGRESQL: process.env.USE_POSTGRESQL,
    }
    
    return NextResponse.json({
      success: false,
      error: errorInfo,
      environment: envInfo,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
