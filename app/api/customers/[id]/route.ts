import { db } from "@/lib/db"
import { json<PERSON><PERSON>r, json<PERSON>k } from "@/lib/api-helpers"
import { customers } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"
import { withTenantAuth, createForbiddenResponse } from "@/lib/tenant-utils"

const patchSchema = z.object({
  name: z.string().min(2).optional(),
  contact_name: z.string().optional(),
  contact_phone: z.string().optional(),
  contact_email: z.string().email().optional().or(z.literal("")),
  address: z.string().optional(),
  incoterm: z.string().optional(),
  payment_term: z.string().optional(),
  status: z.string().optional(),
})

// 🛡️ SECURE: Multi-tenant PATCH endpoint with proper isolation
export const PATCH = withTenantAuth(async function PATCH(req: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await req.json()
    const data = patchSchema.parse(body)

    // 🛡️ CRITICAL: Verify customer belongs to current company before updating
    const existingCustomer = await db.query.customers.findFirst({
      where: and(
        eq(customers.id, id),
        eq(customers.company_id, context.companyId)
      ),
    })

    if (!existingCustomer) {
      return createForbiddenResponse()
    }

    const updated = await db.update(customers)
      .set(data)
      .where(and(
        eq(customers.id, id),
        eq(customers.company_id, context.companyId) // 🛡️ Double-check tenant isolation
      ))
      .returning()

    return jsonOk(updated[0])
  } catch (e) {
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant DELETE endpoint with proper isolation
export const DELETE = withTenantAuth(async function DELETE(_: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    // 🛡️ CRITICAL: Verify customer belongs to current company before deleting
    const existingCustomer = await db.query.customers.findFirst({
      where: and(
        eq(customers.id, id),
        eq(customers.company_id, context.companyId)
      ),
    })

    if (!existingCustomer) {
      return createForbiddenResponse()
    }

    await db.delete(customers).where(and(
      eq(customers.id, id),
      eq(customers.company_id, context.companyId) // 🛡️ Double-check tenant isolation
    ))

    return new Response(null, { status: 204 })
  } catch (e) {
    return jsonError(e)
  }
})
