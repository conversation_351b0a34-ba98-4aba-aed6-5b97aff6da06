import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { customers } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { customerSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const rows = await db.query.customers.findMany({
      where: eq(customers.company_id, context.companyId),
      orderBy: [desc(customers.created_at)],
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, customerSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("cust")

    // Map validated data to database schema with company_id for tenant isolation
    const newCustomer = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      name: body.name,
      contact_name: body.contact_name,
      contact_phone: body.contact_phone,
      contact_email: body.contact_email,
      address: body.address,
      tax_id: body.tax_id,
      bank: body.bank,
      incoterm: body.incoterm,
      payment_term: body.payment_term,
      status: body.status || "active",
    }

    await db.insert(customers).values(newCustomer)

    // 🛡️ SECURE: Only return customer if it belongs to current company
    const row = await db.query.customers.findFirst({
      where: and(
        eq(customers.id, id),
        eq(customers.company_id, context.companyId)
      ),
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
})
