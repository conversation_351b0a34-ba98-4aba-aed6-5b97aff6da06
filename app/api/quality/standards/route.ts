import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { qualityStandards } from "@/lib/schema-postgres"
import { desc, eq } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { qualityStandardSchema } from "@/lib/validations"
import { NextRequest } from "next/server"

export async function GET(req: Request) {
  try {
    const url = new URL(req.url)
    const productId = url.searchParams.get("product_id")

    // Filter by product if specified
    const whereClause = productId ? eq(qualityStandards.product_id, productId) : undefined

    const rows = await db.query.qualityStandards.findMany({
      where: whereClause,
      orderBy: [desc(qualityStandards.created_at)],
      with: {
        product: true,
        results: {
          with: {
            inspection: {
              with: {
                workOrder: true,
              },
            },
          },
        },
      },
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
}

export async function POST(req: NextRequest) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, qualityStandardSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("qs")

    const newStandard = {
      id,
      product_id: body.product_id,
      standard_name: body.standard_name,
      specification: body.specification,
      tolerance: body.tolerance,
      test_method: body.test_method,
      acceptance_criteria: body.acceptance_criteria,
    }

    await db.insert(qualityStandards).values(newStandard)

    const row = await db.query.qualityStandards.findFirst({
      where: eq(qualityStandards.id, id),
      with: {
        product: true,
        results: {
          with: {
            inspection: {
              with: {
                workOrder: true,
              },
            },
          },
        },
      },
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
}
