import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { qualityInspections } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { z } from "zod"
import {
  createInspectionNotFoundError,
  createInvalidInspectionStatusError,
  createCannotDeleteCompletedInspectionError
} from "@/lib/errors"
import { qualityWorkflowService } from "@/lib/services/quality-workflow"

const patchSchema = z.object({
  inspection_type: z.enum(["incoming", "in-process", "final", "pre-shipment"]).optional(),
  inspector: z.string().min(1).optional(),
  scheduled_date: z.string().optional(),
  completed_date: z.string().optional(),
  status: z.enum(["scheduled", "in-progress", "completed", "failed"]).optional(),
  notes: z.string().optional(),
})

export async function GET(_: Request, { params }: { params: { id: string } }) {
  try {
    const row = await db.query.qualityInspections.findFirst({
      where: eq(qualityInspections.id, params.id),
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        defects: {
          with: {
            product: true,
          },
        },
        certificates: true,
        results: {
          with: {
            standard: true,
          },
        },
      },
    })

    if (!row) {
      throw createInspectionNotFoundError(params.id)
    }

    return createSuccessResponse(row)
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function PATCH(req: Request, { params }: { params: { id: string } }) {
  try {
    const body = await req.json()
    const data = patchSchema.parse(body)

    // Check if inspection exists and get current status
    const existing = await db.query.qualityInspections.findFirst({
      where: eq(qualityInspections.id, params.id)
    })

    if (!existing) {
      throw createInspectionNotFoundError(params.id)
    }

    // Validate status transitions if status is being changed
    if (data.status && data.status !== existing.status) {
      const validTransitions: Record<string, string[]> = {
        "scheduled": ["in-progress", "completed", "failed"],
        "in-progress": ["completed", "failed"],
        "completed": [], // Cannot change from completed
        "failed": ["scheduled", "in-progress"] // Can retry failed inspections
      }

      if (!validTransitions[existing.status]?.includes(data.status)) {
        throw createInvalidInspectionStatusError(existing.status, data.status)
      }
    }

    const updated = await db
      .update(qualityInspections)
      .set(data)
      .where(eq(qualityInspections.id, params.id))
      .returning()

    if (updated.length === 0) {
      throw createInspectionNotFoundError(params.id)
    }

    // Trigger quality workflow if inspection was completed
    if (data.status === "completed" && existing.status !== "completed") {
      try {
        // Determine inspection result (simplified - in real implementation would check inspection_results)
        const result = "pass" // This would be determined by actual inspection results
        await qualityWorkflowService.processInspectionCompletion(params.id, result)
      } catch (workflowError) {
        console.warn("Quality workflow completion trigger failed:", workflowError)
        // Don't fail the entire operation if workflow trigger fails
      }
    }

    // Fetch the updated record with relations
    const row = await db.query.qualityInspections.findFirst({
      where: eq(qualityInspections.id, params.id),
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        defects: {
          with: {
            product: true,
          },
        },
        certificates: true,
        results: {
          with: {
            standard: true,
          },
        },
      },
    })

    return createSuccessResponse(row, "Inspection updated successfully")
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function DELETE(_: Request, { params }: { params: { id: string } }) {
  try {
    // Check if inspection exists and its status
    const existing = await db.query.qualityInspections.findFirst({
      where: eq(qualityInspections.id, params.id)
    })

    if (!existing) {
      throw createInspectionNotFoundError(params.id)
    }

    // Prevent deletion of completed inspections
    if (existing.status === "completed") {
      throw createCannotDeleteCompletedInspectionError(params.id)
    }

    const deleted = await db
      .delete(qualityInspections)
      .where(eq(qualityInspections.id, params.id))
      .returning()

    if (deleted.length === 0) {
      throw createInspectionNotFoundError(params.id)
    }

    return new Response(null, { status: 204 })
  } catch (error) {
    return createErrorResponse(error)
  }
}
