import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { qualityInspections } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { qualityInspectionSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { createReferenceNotFoundError } from "@/lib/errors"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.qualityInspections.findMany({
      where: eq(qualityInspections.company_id, context.companyId),
      orderBy: [desc(qualityInspections.created_at)],
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        defects: true,
        certificates: true,
        results: {
          with: {
            standard: true,
          },
        },
      },
    })
    return createSuccessResponse(rows)
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, qualityInspectionSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("qi")

    // 🛡️ CRITICAL: Validate that work order exists and belongs to current company
    if (body.work_order_id) {
      const workOrder = await db.query.workOrders.findFirst({
        where: (workOrders, { eq, and }) => and(
          eq(workOrders.id, body.work_order_id),
          eq(workOrders.company_id, context.companyId)
        )
      })

      if (!workOrder) {
        throw createReferenceNotFoundError("Work Order", body.work_order_id)
      }
    }

    const newInspection = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      work_order_id: body.work_order_id,
      inspection_type: body.inspection_type,
      inspector: body.inspector,
      scheduled_date: body.scheduled_date,
      completed_date: body.completed_date,
      status: body.status || "scheduled",
      notes: body.notes,
    }

    await db.insert(qualityInspections).values(newInspection)

    // 🛡️ SECURE: Only return inspection if it belongs to current company
    const row = await db.query.qualityInspections.findFirst({
      where: and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      ),
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        defects: true,
        certificates: true,
        results: {
          with: {
            standard: true,
          },
        },
      },
    })
    return createSuccessResponse(row, "Inspection created successfully", 201)
  } catch (error) {
    return createErrorResponse(error)
  }
})
