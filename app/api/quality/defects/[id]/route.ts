import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { qualityDefects } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { z } from "zod"

const patchSchema = z.object({
  defect_type: z.enum(["visual", "dimensional", "functional", "material"]).optional(),
  severity: z.enum(["minor", "major", "critical"]).optional(),
  quantity: z.string().optional(),
  description: z.string().min(1).optional(),
  corrective_action: z.string().optional(),
  status: z.enum(["open", "in-progress", "resolved", "closed"]).optional(),
})

export async function GET(_: Request, { params }: { params: { id: string } }) {
  try {
    const row = await db.query.qualityDefects.findFirst({
      where: eq(qualityDefects.id, params.id),
      with: {
        inspection: {
          with: {
            workOrder: {
              with: {
                product: true,
              },
            },
          },
        },
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        product: true,
      },
    })

    if (!row) {
      return jsonError(new Error("Defect not found"), 404)
    }

    return jsonOk(row)
  } catch (e) {
    return jsonError(e)
  }
}

export async function PATCH(req: Request, { params }: { params: { id: string } }) {
  try {
    const body = await req.json()
    const data = patchSchema.parse(body)

    const updated = await db
      .update(qualityDefects)
      .set(data)
      .where(eq(qualityDefects.id, params.id))
      .returning()

    if (updated.length === 0) {
      return jsonError(new Error("Defect not found"), 404)
    }

    // Fetch the updated record with relations
    const row = await db.query.qualityDefects.findFirst({
      where: eq(qualityDefects.id, params.id),
      with: {
        inspection: {
          with: {
            workOrder: {
              with: {
                product: true,
              },
            },
          },
        },
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        product: true,
      },
    })

    return jsonOk(row)
  } catch (e) {
    return jsonError(e)
  }
}

export async function DELETE(_: Request, { params }: { params: { id: string } }) {
  try {
    const deleted = await db
      .delete(qualityDefects)
      .where(eq(qualityDefects.id, params.id))
      .returning()

    if (deleted.length === 0) {
      return jsonError(new Error("Defect not found"), 404)
    }

    return new Response(null, { status: 204 })
  } catch (e) {
    return jsonError(e)
  }
}
