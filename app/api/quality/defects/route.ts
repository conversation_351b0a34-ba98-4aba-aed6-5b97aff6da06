import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { qualityDefects } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { qualityDefectSchema } from "@/lib/validations"
import { NextRequest } from "next/server"

export async function GET(req: Request) {
  try {
    const url = new URL(req.url)
    const inspectionId = url.searchParams.get("inspection_id")
    const workOrderId = url.searchParams.get("work_order_id")
    const productId = url.searchParams.get("product_id")
    const severity = url.searchParams.get("severity")
    const status = url.searchParams.get("status")

    // Build where conditions based on query parameters
    const whereConditions = []
    if (inspectionId) {
      whereConditions.push(eq(qualityDefects.inspection_id, inspectionId))
    }
    if (workOrderId) {
      whereConditions.push(eq(qualityDefects.work_order_id, workOrderId))
    }
    if (productId) {
      whereConditions.push(eq(qualityDefects.product_id, productId))
    }
    if (severity) {
      whereConditions.push(eq(qualityDefects.severity, severity))
    }
    if (status) {
      whereConditions.push(eq(qualityDefects.status, status))
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined

    const rows = await db.query.qualityDefects.findMany({
      where: whereClause,
      orderBy: [desc(qualityDefects.created_at)],
      with: {
        inspection: {
          with: {
            workOrder: {
              with: {
                product: true,
              },
            },
          },
        },
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        product: true,
      },
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
}

export async function POST(req: NextRequest) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, qualityDefectSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("qd")

    const newDefect = {
      id,
      inspection_id: body.inspection_id,
      work_order_id: body.work_order_id,
      product_id: body.product_id,
      defect_type: body.defect_type,
      severity: body.severity,
      quantity: body.quantity,
      description: body.description,
      corrective_action: body.corrective_action,
      status: body.status || "open",
    }

    await db.insert(qualityDefects).values(newDefect)

    const row = await db.query.qualityDefects.findFirst({
      where: eq(qualityDefects.id, id),
      with: {
        inspection: {
          with: {
            workOrder: {
              with: {
                product: true,
              },
            },
          },
        },
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        product: true,
      },
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
}
