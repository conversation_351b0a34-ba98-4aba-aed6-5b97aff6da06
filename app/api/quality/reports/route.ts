import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { 
  qualityInspections, 
  qualityDefects, 
  qualityCertificates,
  stockLots,
  workOrders,
  products,
  customers 
} from "@/lib/schema-postgres"
import { eq, and, gte, lte, desc } from "drizzle-orm"

export async function GET(req: Request) {
  try {
    const url = new URL(req.url)
    const reportType = url.searchParams.get("type") || "summary"
    const startDate = url.searchParams.get("start_date")
    const endDate = url.searchParams.get("end_date")
    const productId = url.searchParams.get("product_id")
    const customerId = url.searchParams.get("customer_id")
    const format = url.searchParams.get("format") || "json" // json, csv, pdf

    // Validate date range
    if (!startDate || !endDate) {
      throw new Error("start_date and end_date are required")
    }

    const startDateTime = new Date(startDate)
    const endDateTime = new Date(endDate)

    let reportData
    switch (reportType) {
      case "summary":
        reportData = await generateSummaryReport(startDateTime, endDateTime, productId, customerId)
        break
      case "compliance":
        reportData = await generateComplianceReport(startDateTime, endDateTime, productId, customerId)
        break
      case "audit_trail":
        reportData = await generateAuditTrailReport(startDateTime, endDateTime, productId, customerId)
        break
      case "defect_analysis":
        reportData = await generateDefectAnalysisReport(startDateTime, endDateTime, productId)
        break
      case "certificate_status":
        reportData = await generateCertificateStatusReport(startDateTime, endDateTime, customerId)
        break
      default:
        throw new Error(`Unknown report type: ${reportType}`)
    }

    const report = {
      reportType,
      period: {
        startDate,
        endDate,
      },
      filters: {
        productId,
        customerId,
      },
      data: reportData,
      generatedAt: new Date().toISOString(),
      format,
    }

    return createSuccessResponse(report)
  } catch (error) {
    return createErrorResponse(error)
  }
}

async function generateSummaryReport(startDate: Date, endDate: Date, productId?: string, customerId?: string) {
  // Get all inspections in the period
  const inspections = await db.query.qualityInspections.findMany({
    where: and(
      gte(qualityInspections.created_at, startDate),
      lte(qualityInspections.created_at, endDate)
    ),
    with: {
      workOrder: {
        with: {
          product: true,
          salesContract: {
            with: {
              customer: true,
            },
          },
        },
      },
      defects: true,
      certificates: true,
    },
    orderBy: [desc(qualityInspections.created_at)],
  })

  // Filter by product/customer if specified
  const filteredInspections = inspections.filter(inspection => {
    if (productId && inspection.workOrder?.product?.id !== productId) return false
    if (customerId && inspection.workOrder?.salesContract?.customer?.id !== customerId) return false
    return true
  })

  // Calculate summary statistics
  const totalInspections = filteredInspections.length
  const completedInspections = filteredInspections.filter(i => i.status === "completed").length
  const failedInspections = filteredInspections.filter(i => i.status === "failed").length
  const totalDefects = filteredInspections.reduce((sum, i) => sum + (i.defects?.length || 0), 0)
  const totalCertificates = filteredInspections.reduce((sum, i) => sum + (i.certificates?.length || 0), 0)

  return {
    summary: {
      totalInspections,
      completedInspections,
      failedInspections,
      totalDefects,
      totalCertificates,
      passRate: totalInspections > 0 ? ((completedInspections / totalInspections) * 100).toFixed(2) : "0.00",
    },
    inspectionsByType: groupBy(filteredInspections, 'inspection_type'),
    inspectionsByStatus: groupBy(filteredInspections, 'status'),
    recentInspections: filteredInspections.slice(0, 10).map(formatInspectionSummary),
  }
}

async function generateComplianceReport(startDate: Date, endDate: Date, productId?: string, customerId?: string) {
  // Get all certificates in the period
  const certificates = await db.query.qualityCertificates.findMany({
    where: and(
      gte(qualityCertificates.created_at, startDate),
      lte(qualityCertificates.created_at, endDate)
    ),
    with: {
      inspection: {
        with: {
          workOrder: {
            with: {
              product: true,
              salesContract: {
                with: {
                  customer: true,
                },
              },
            },
          },
          defects: true,
        },
      },
    },
    orderBy: [desc(qualityCertificates.created_at)],
  })

  // Filter by product/customer if specified
  const filteredCertificates = certificates.filter(cert => {
    if (productId && cert.inspection?.workOrder?.product?.id !== productId) return false
    if (customerId && cert.inspection?.workOrder?.salesContract?.customer?.id !== customerId) return false
    return true
  })

  // Check compliance status
  const today = new Date().toISOString().split('T')[0]
  const activeCertificates = filteredCertificates.filter(c => !c.valid_until || c.valid_until >= today)
  const expiredCertificates = filteredCertificates.filter(c => c.valid_until && c.valid_until < today)
  const expiringSoon = filteredCertificates.filter(c => {
    if (!c.valid_until) return false
    const daysUntilExpiry = Math.ceil((new Date(c.valid_until).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0
  })

  return {
    summary: {
      totalCertificates: filteredCertificates.length,
      activeCertificates: activeCertificates.length,
      expiredCertificates: expiredCertificates.length,
      expiringSoon: expiringSoon.length,
    },
    certificatesByType: groupBy(filteredCertificates, 'certificate_type'),
    complianceStatus: {
      compliant: activeCertificates.length,
      nonCompliant: expiredCertificates.length,
      requiresAttention: expiringSoon.length,
    },
    certificates: filteredCertificates.map(formatCertificateSummary),
  }
}

async function generateAuditTrailReport(startDate: Date, endDate: Date, productId?: string, customerId?: string) {
  // Get all quality-related activities in chronological order
  const inspections = await db.query.qualityInspections.findMany({
    where: and(
      gte(qualityInspections.created_at, startDate),
      lte(qualityInspections.created_at, endDate)
    ),
    with: {
      workOrder: {
        with: {
          product: true,
          salesContract: {
            with: {
              customer: true,
            },
          },
        },
      },
      defects: true,
      certificates: true,
    },
    orderBy: [desc(qualityInspections.created_at)],
  })

  // Create audit trail entries
  const auditTrail = []

  for (const inspection of inspections) {
    // Filter by product/customer if specified
    if (productId && inspection.workOrder?.product?.id !== productId) continue
    if (customerId && inspection.workOrder?.salesContract?.customer?.id !== customerId) continue

    // Inspection created
    auditTrail.push({
      timestamp: inspection.created_at,
      action: "inspection_created",
      entity: "quality_inspection",
      entityId: inspection.id,
      details: {
        inspectionType: inspection.inspection_type,
        inspector: inspection.inspector,
        workOrderId: inspection.work_order_id,
        productName: inspection.workOrder?.product?.name,
        customerName: inspection.workOrder?.salesContract?.customer?.name,
      },
    })

    // Inspection completed
    if (inspection.completed_date) {
      auditTrail.push({
        timestamp: new Date(inspection.completed_date),
        action: "inspection_completed",
        entity: "quality_inspection",
        entityId: inspection.id,
        details: {
          status: inspection.status,
          defectCount: inspection.defects?.length || 0,
          certificateCount: inspection.certificates?.length || 0,
        },
      })
    }

    // Defects recorded
    for (const defect of inspection.defects || []) {
      auditTrail.push({
        timestamp: defect.created_at,
        action: "defect_recorded",
        entity: "quality_defect",
        entityId: defect.id,
        details: {
          defectType: defect.defect_type,
          severity: defect.severity,
          description: defect.description,
          inspectionId: inspection.id,
        },
      })
    }

    // Certificates generated
    for (const certificate of inspection.certificates || []) {
      auditTrail.push({
        timestamp: certificate.created_at,
        action: "certificate_generated",
        entity: "quality_certificate",
        entityId: certificate.id,
        details: {
          certificateNumber: certificate.certificate_number,
          certificateType: certificate.certificate_type,
          validUntil: certificate.valid_until,
          inspectionId: inspection.id,
        },
      })
    }
  }

  // Sort by timestamp
  auditTrail.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

  return {
    summary: {
      totalEvents: auditTrail.length,
      dateRange: { startDate, endDate },
    },
    auditTrail: auditTrail.slice(0, 100), // Limit to 100 most recent events
  }
}

async function generateDefectAnalysisReport(startDate: Date, endDate: Date, productId?: string) {
  const defects = await db.query.qualityDefects.findMany({
    where: and(
      gte(qualityDefects.created_at, startDate),
      lte(qualityDefects.created_at, endDate),
      productId ? eq(qualityDefects.product_id, productId) : undefined
    ),
    with: {
      inspection: {
        with: {
          workOrder: {
            with: {
              product: true,
            },
          },
        },
      },
      product: true,
    },
    orderBy: [desc(qualityDefects.created_at)],
  })

  return {
    summary: {
      totalDefects: defects.length,
      bySeverity: groupBy(defects, 'severity'),
      byType: groupBy(defects, 'defect_type'),
      byStatus: groupBy(defects, 'status'),
    },
    defects: defects.map(formatDefectSummary),
  }
}

async function generateCertificateStatusReport(startDate: Date, endDate: Date, customerId?: string) {
  const certificates = await db.query.qualityCertificates.findMany({
    where: and(
      gte(qualityCertificates.created_at, startDate),
      lte(qualityCertificates.created_at, endDate)
    ),
    with: {
      inspection: {
        with: {
          workOrder: {
            with: {
              product: true,
              salesContract: {
                with: {
                  customer: true,
                },
              },
            },
          },
        },
      },
    },
    orderBy: [desc(qualityCertificates.created_at)],
  })

  // Filter by customer if specified
  const filteredCertificates = customerId 
    ? certificates.filter(c => c.inspection?.workOrder?.salesContract?.customer?.id === customerId)
    : certificates

  return {
    summary: {
      totalCertificates: filteredCertificates.length,
      byType: groupBy(filteredCertificates, 'certificate_type'),
    },
    certificates: filteredCertificates.map(formatCertificateSummary),
  }
}

// Helper functions
function groupBy(array: any[], key: string) {
  return array.reduce((groups, item) => {
    const value = item[key] || 'unknown'
    groups[value] = (groups[value] || 0) + 1
    return groups
  }, {})
}

function formatInspectionSummary(inspection: any) {
  return {
    id: inspection.id,
    type: inspection.inspection_type,
    status: inspection.status,
    inspector: inspection.inspector,
    scheduledDate: inspection.scheduled_date,
    completedDate: inspection.completed_date,
    productName: inspection.workOrder?.product?.name,
    customerName: inspection.workOrder?.salesContract?.customer?.name,
    defectCount: inspection.defects?.length || 0,
    certificateCount: inspection.certificates?.length || 0,
  }
}

function formatCertificateSummary(certificate: any) {
  return {
    id: certificate.id,
    certificateNumber: certificate.certificate_number,
    type: certificate.certificate_type,
    issuedDate: certificate.issued_date,
    validUntil: certificate.valid_until,
    productName: certificate.inspection?.workOrder?.product?.name,
    customerName: certificate.inspection?.workOrder?.salesContract?.customer?.name,
  }
}

function formatDefectSummary(defect: any) {
  return {
    id: defect.id,
    type: defect.defect_type,
    severity: defect.severity,
    description: defect.description,
    status: defect.status,
    correctiveAction: defect.corrective_action,
    productName: defect.product?.name,
    createdAt: defect.created_at,
  }
}
