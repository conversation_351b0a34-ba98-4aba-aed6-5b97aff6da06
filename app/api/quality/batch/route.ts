import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { 
  qualityInspections, 
  qualityDefects, 
  qualityCertificates,
  stockLots,
  workOrders 
} from "@/lib/schema-postgres"
import { eq, and, inArray } from "drizzle-orm"
import { z } from "zod"
import { certificateGenerationService } from "@/lib/services/certificate-generation"
import { qualityWorkflowService } from "@/lib/services/quality-workflow"

const batchOperationSchema = z.object({
  operation: z.enum([
    "update_inspection_status",
    "update_defect_status", 
    "generate_certificates",
    "assign_inspectors",
    "bulk_approve_quality",
    "schedule_inspections",
    "export_quality_data"
  ]),
  items: z.array(z.record(z.any())).min(1, "At least one item is required"),
  parameters: z.record(z.any()).optional(),
})

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const data = batchOperationSchema.parse(body)

    let result

    switch (data.operation) {
      case "update_inspection_status":
        result = await batchUpdateInspectionStatus(data.items, data.parameters)
        break

      case "update_defect_status":
        result = await batchUpdateDefectStatus(data.items, data.parameters)
        break

      case "generate_certificates":
        result = await batchGenerateCertificates(data.items, data.parameters)
        break

      case "assign_inspectors":
        result = await batchAssignInspectors(data.items, data.parameters)
        break

      case "bulk_approve_quality":
        result = await bulkApproveQuality(data.items, data.parameters)
        break

      case "schedule_inspections":
        result = await batchScheduleInspections(data.items, data.parameters)
        break

      case "export_quality_data":
        result = await exportQualityData(data.items, data.parameters)
        break

      default:
        throw new Error(`Unknown batch operation: ${data.operation}`)
    }

    return createSuccessResponse({
      operation: data.operation,
      result,
      processedItems: data.items.length,
      executedAt: new Date().toISOString(),
    }, `Batch operation ${data.operation} completed successfully`)

  } catch (error) {
    return createErrorResponse(error)
  }
}

async function batchUpdateInspectionStatus(
  items: Array<{ id: string, status: string, notes?: string }>,
  parameters?: any
) {
  const results = {
    successful: [],
    failed: [],
    summary: {
      total: items.length,
      updated: 0,
      errors: 0,
    }
  }

  await db.transaction(async (tx) => {
    for (const item of items) {
      try {
        // Validate inspection exists
        const inspection = await tx.query.qualityInspections.findFirst({
          where: eq(qualityInspections.id, item.id)
        })

        if (!inspection) {
          throw new Error(`Inspection ${item.id} not found`)
        }

        // Update inspection
        const updateData: any = { status: item.status }
        if (item.notes) updateData.notes = item.notes
        if (item.status === "completed") {
          updateData.completed_date = new Date().toISOString().split('T')[0]
        }

        await tx
          .update(qualityInspections)
          .set(updateData)
          .where(eq(qualityInspections.id, item.id))

        // Trigger workflow if completed
        if (item.status === "completed") {
          try {
            await qualityWorkflowService.processInspectionCompletion(item.id, "pass")
          } catch (workflowError) {
            // Don't fail the batch operation for workflow errors
            console.warn(`Workflow trigger failed for ${item.id}:`, workflowError)
          }
        }

        results.successful.push({
          id: item.id,
          status: item.status,
          message: "Updated successfully"
        })
        results.summary.updated++

      } catch (error) {
        results.failed.push({
          id: item.id,
          error: error.message
        })
        results.summary.errors++
      }
    }
  })

  return results
}

async function batchUpdateDefectStatus(
  items: Array<{ id: string, status: string, corrective_action?: string }>,
  parameters?: any
) {
  const results = {
    successful: [],
    failed: [],
    summary: {
      total: items.length,
      updated: 0,
      errors: 0,
    }
  }

  await db.transaction(async (tx) => {
    for (const item of items) {
      try {
        const updateData: any = { status: item.status }
        if (item.corrective_action) {
          updateData.corrective_action = item.corrective_action
        }

        const updated = await tx
          .update(qualityDefects)
          .set(updateData)
          .where(eq(qualityDefects.id, item.id))
          .returning()

        if (updated.length === 0) {
          throw new Error(`Defect ${item.id} not found`)
        }

        results.successful.push({
          id: item.id,
          status: item.status,
          message: "Updated successfully"
        })
        results.summary.updated++

      } catch (error) {
        results.failed.push({
          id: item.id,
          error: error.message
        })
        results.summary.errors++
      }
    }
  })

  return results
}

async function batchGenerateCertificates(
  items: Array<{ inspectionId: string, certificateType: string }>,
  parameters?: any
) {
  const results = {
    successful: [],
    failed: [],
    summary: {
      total: items.length,
      generated: 0,
      errors: 0,
    }
  }

  for (const item of items) {
    try {
      const certificateId = await certificateGenerationService.generateCertificate({
        inspectionId: item.inspectionId,
        certificateType: item.certificateType as any,
        validityDays: parameters?.validityDays || 365,
      })

      results.successful.push({
        inspectionId: item.inspectionId,
        certificateId,
        certificateType: item.certificateType,
        message: "Certificate generated successfully"
      })
      results.summary.generated++

    } catch (error) {
      results.failed.push({
        inspectionId: item.inspectionId,
        error: error.message
      })
      results.summary.errors++
    }
  }

  return results
}

async function batchAssignInspectors(
  items: Array<{ inspectionId: string, inspector: string }>,
  parameters?: any
) {
  const results = {
    successful: [],
    failed: [],
    summary: {
      total: items.length,
      assigned: 0,
      errors: 0,
    }
  }

  await db.transaction(async (tx) => {
    for (const item of items) {
      try {
        const updated = await tx
          .update(qualityInspections)
          .set({ inspector: item.inspector })
          .where(eq(qualityInspections.id, item.inspectionId))
          .returning()

        if (updated.length === 0) {
          throw new Error(`Inspection ${item.inspectionId} not found`)
        }

        results.successful.push({
          inspectionId: item.inspectionId,
          inspector: item.inspector,
          message: "Inspector assigned successfully"
        })
        results.summary.assigned++

      } catch (error) {
        results.failed.push({
          inspectionId: item.inspectionId,
          error: error.message
        })
        results.summary.errors++
      }
    }
  })

  return results
}

async function bulkApproveQuality(
  items: Array<{ type: "stock_lot" | "work_order", id: string }>,
  parameters?: any
) {
  const results = {
    successful: [],
    failed: [],
    summary: {
      total: items.length,
      approved: 0,
      errors: 0,
    }
  }

  await db.transaction(async (tx) => {
    for (const item of items) {
      try {
        if (item.type === "stock_lot") {
          await tx
            .update(stockLots)
            .set({ 
              quality_status: "approved",
              quality_notes: "Bulk approved via batch operation"
            })
            .where(eq(stockLots.id, item.id))

        } else if (item.type === "work_order") {
          await tx
            .update(workOrders)
            .set({ quality_status: "approved" })
            .where(eq(workOrders.id, item.id))
        }

        results.successful.push({
          type: item.type,
          id: item.id,
          message: "Quality approved successfully"
        })
        results.summary.approved++

      } catch (error) {
        results.failed.push({
          type: item.type,
          id: item.id,
          error: error.message
        })
        results.summary.errors++
      }
    }
  })

  return results
}

async function batchScheduleInspections(
  items: Array<{ 
    workOrderId?: string, 
    productId: string, 
    inspectionType: string,
    scheduledDate: string,
    inspector?: string 
  }>,
  parameters?: any
) {
  const results = {
    successful: [],
    failed: [],
    summary: {
      total: items.length,
      scheduled: 0,
      errors: 0,
    }
  }

  await db.transaction(async (tx) => {
    for (const item of items) {
      try {
        const inspectionId = uid("qi")
        
        const newInspection = {
          id: inspectionId,
          work_order_id: item.workOrderId || null,
          inspection_type: item.inspectionType,
          inspector: item.inspector || "TBD",
          scheduled_date: item.scheduledDate,
          status: "scheduled" as const,
          notes: "Batch scheduled inspection",
        }

        await tx.insert(qualityInspections).values(newInspection)

        results.successful.push({
          inspectionId,
          workOrderId: item.workOrderId,
          inspectionType: item.inspectionType,
          scheduledDate: item.scheduledDate,
          message: "Inspection scheduled successfully"
        })
        results.summary.scheduled++

      } catch (error) {
        results.failed.push({
          workOrderId: item.workOrderId,
          error: error.message
        })
        results.summary.errors++
      }
    }
  })

  return results
}

async function exportQualityData(
  items: Array<{ type: string, filters?: any }>,
  parameters?: any
) {
  const results = {
    exports: [],
    summary: {
      total: items.length,
      exported: 0,
      totalRecords: 0,
    }
  }

  for (const item of items) {
    try {
      let data = []
      let recordCount = 0

      switch (item.type) {
        case "inspections":
          data = await db.query.qualityInspections.findMany({
            limit: 1000, // Limit for performance
            with: {
              workOrder: {
                with: {
                  product: true,
                },
              },
              defects: true,
              certificates: true,
            },
          })
          recordCount = data.length
          break

        case "defects":
          data = await db.query.qualityDefects.findMany({
            limit: 1000,
            with: {
              product: true,
              inspection: true,
            },
          })
          recordCount = data.length
          break

        case "certificates":
          data = await db.query.qualityCertificates.findMany({
            limit: 1000,
            with: {
              inspection: {
                with: {
                  workOrder: {
                    with: {
                      product: true,
                    },
                  },
                },
              },
            },
          })
          recordCount = data.length
          break

        default:
          throw new Error(`Unknown export type: ${item.type}`)
      }

      results.exports.push({
        type: item.type,
        recordCount,
        data: parameters?.includeData ? data : null,
        exportedAt: new Date().toISOString(),
      })

      results.summary.exported++
      results.summary.totalRecords += recordCount

    } catch (error) {
      results.exports.push({
        type: item.type,
        error: error.message,
      })
    }
  }

  return results
}
