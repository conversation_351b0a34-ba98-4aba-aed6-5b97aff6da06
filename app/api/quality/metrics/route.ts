import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { 
  qualityInspections, 
  qualityDefects, 
  qualityCertificates,
  stockLots,
  workOrders 
} from "@/lib/schema-postgres"
import { eq, and, gte, lte, count, sql } from "drizzle-orm"

export async function GET(req: Request) {
  try {
    const url = new URL(req.url)
    const period = url.searchParams.get("period") || "30" // days
    const productId = url.searchParams.get("product_id")
    const inspectionType = url.searchParams.get("inspection_type")

    // Calculate date range
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - parseInt(period) * 24 * 60 * 60 * 1000)
    const startDateStr = startDate.toISOString().split('T')[0]
    const endDateStr = endDate.toISOString().split('T')[0]

    // Build base filters
    const baseFilters = [
      gte(qualityInspections.created_at, startDate),
      lte(qualityInspections.created_at, endDate)
    ]

    if (productId) {
      // Would need to join with work orders to filter by product
    }

    if (inspectionType) {
      baseFilters.push(eq(qualityInspections.inspection_type, inspectionType))
    }

    // Get inspection metrics
    const inspectionMetrics = await getInspectionMetrics(baseFilters, startDateStr, endDateStr)
    
    // Get defect metrics
    const defectMetrics = await getDefectMetrics(baseFilters, startDateStr, endDateStr)
    
    // Get certificate metrics
    const certificateMetrics = await getCertificateMetrics(startDateStr, endDateStr)
    
    // Get quality status metrics
    const qualityStatusMetrics = await getQualityStatusMetrics()

    // Calculate derived metrics
    const derivedMetrics = calculateDerivedMetrics(inspectionMetrics, defectMetrics)

    const metrics = {
      period: {
        days: parseInt(period),
        startDate: startDateStr,
        endDate: endDateStr,
      },
      inspections: inspectionMetrics,
      defects: defectMetrics,
      certificates: certificateMetrics,
      qualityStatus: qualityStatusMetrics,
      derived: derivedMetrics,
      generatedAt: new Date().toISOString(),
    }

    return createSuccessResponse(metrics)
  } catch (error) {
    return createErrorResponse(error)
  }
}

async function getInspectionMetrics(baseFilters: any[], startDate: string, endDate: string) {
  // Total inspections
  const totalInspections = await db
    .select({ count: count() })
    .from(qualityInspections)
    .where(and(...baseFilters))

  // Inspections by status
  const inspectionsByStatus = await db
    .select({ 
      status: qualityInspections.status, 
      count: count() 
    })
    .from(qualityInspections)
    .where(and(...baseFilters))
    .groupBy(qualityInspections.status)

  // Inspections by type
  const inspectionsByType = await db
    .select({ 
      type: qualityInspections.inspection_type, 
      count: count() 
    })
    .from(qualityInspections)
    .where(and(...baseFilters))
    .groupBy(qualityInspections.inspection_type)

  // Completed inspections (for pass rate calculation)
  const completedInspections = await db
    .select({ count: count() })
    .from(qualityInspections)
    .where(and(...baseFilters, eq(qualityInspections.status, "completed")))

  return {
    total: totalInspections[0]?.count || 0,
    completed: completedInspections[0]?.count || 0,
    byStatus: inspectionsByStatus.reduce((acc, item) => {
      acc[item.status] = item.count
      return acc
    }, {} as Record<string, number>),
    byType: inspectionsByType.reduce((acc, item) => {
      acc[item.type] = item.count
      return acc
    }, {} as Record<string, number>),
  }
}

async function getDefectMetrics(baseFilters: any[], startDate: string, endDate: string) {
  // Total defects
  const totalDefects = await db
    .select({ count: count() })
    .from(qualityDefects)
    .innerJoin(qualityInspections, eq(qualityDefects.inspection_id, qualityInspections.id))
    .where(and(...baseFilters))

  // Defects by severity
  const defectsBySeverity = await db
    .select({ 
      severity: qualityDefects.severity, 
      count: count() 
    })
    .from(qualityDefects)
    .innerJoin(qualityInspections, eq(qualityDefects.inspection_id, qualityInspections.id))
    .where(and(...baseFilters))
    .groupBy(qualityDefects.severity)

  // Defects by type
  const defectsByType = await db
    .select({ 
      type: qualityDefects.defect_type, 
      count: count() 
    })
    .from(qualityDefects)
    .innerJoin(qualityInspections, eq(qualityDefects.inspection_id, qualityInspections.id))
    .where(and(...baseFilters))
    .groupBy(qualityDefects.defect_type)

  // Defects by status
  const defectsByStatus = await db
    .select({ 
      status: qualityDefects.status, 
      count: count() 
    })
    .from(qualityDefects)
    .innerJoin(qualityInspections, eq(qualityDefects.inspection_id, qualityInspections.id))
    .where(and(...baseFilters))
    .groupBy(qualityDefects.status)

  return {
    total: totalDefects[0]?.count || 0,
    bySeverity: defectsBySeverity.reduce((acc, item) => {
      acc[item.severity] = item.count
      return acc
    }, {} as Record<string, number>),
    byType: defectsByType.reduce((acc, item) => {
      acc[item.type] = item.count
      return acc
    }, {} as Record<string, number>),
    byStatus: defectsByStatus.reduce((acc, item) => {
      acc[item.status] = item.count
      return acc
    }, {} as Record<string, number>),
  }
}

async function getCertificateMetrics(startDate: string, endDate: string) {
  const startDateTime = new Date(startDate)
  const endDateTime = new Date(endDate)

  // Total certificates
  const totalCertificates = await db
    .select({ count: count() })
    .from(qualityCertificates)
    .where(and(
      gte(qualityCertificates.created_at, startDateTime),
      lte(qualityCertificates.created_at, endDateTime)
    ))

  // Certificates by type
  const certificatesByType = await db
    .select({ 
      type: qualityCertificates.certificate_type, 
      count: count() 
    })
    .from(qualityCertificates)
    .where(and(
      gte(qualityCertificates.created_at, startDateTime),
      lte(qualityCertificates.created_at, endDateTime)
    ))
    .groupBy(qualityCertificates.certificate_type)

  // Expiring certificates (next 30 days)
  const expiringDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  const expiringCertificates = await db
    .select({ count: count() })
    .from(qualityCertificates)
    .where(and(
      lte(qualityCertificates.valid_until, expiringDate),
      gte(qualityCertificates.valid_until, new Date().toISOString().split('T')[0])
    ))

  return {
    total: totalCertificates[0]?.count || 0,
    expiringSoon: expiringCertificates[0]?.count || 0,
    byType: certificatesByType.reduce((acc, item) => {
      acc[item.type] = item.count
      return acc
    }, {} as Record<string, number>),
  }
}

async function getQualityStatusMetrics() {
  // Stock lots by quality status
  const stockByQualityStatus = await db
    .select({ 
      status: stockLots.quality_status, 
      count: count() 
    })
    .from(stockLots)
    .groupBy(stockLots.quality_status)

  // Work orders by quality status
  const workOrdersByQualityStatus = await db
    .select({ 
      status: workOrders.quality_status, 
      count: count() 
    })
    .from(workOrders)
    .groupBy(workOrders.quality_status)

  return {
    stockLots: stockByQualityStatus.reduce((acc, item) => {
      acc[item.status || 'unknown'] = item.count
      return acc
    }, {} as Record<string, number>),
    workOrders: workOrdersByQualityStatus.reduce((acc, item) => {
      acc[item.status || 'unknown'] = item.count
      return acc
    }, {} as Record<string, number>),
  }
}

function calculateDerivedMetrics(inspectionMetrics: any, defectMetrics: any) {
  const totalInspections = inspectionMetrics.total
  const completedInspections = inspectionMetrics.completed
  const totalDefects = defectMetrics.total

  // Pass rate (inspections without critical defects)
  const criticalDefects = defectMetrics.bySeverity.critical || 0
  const passRate = completedInspections > 0 
    ? ((completedInspections - criticalDefects) / completedInspections * 100).toFixed(2)
    : "0.00"

  // Defect rate (defects per inspection)
  const defectRate = totalInspections > 0 
    ? (totalDefects / totalInspections).toFixed(2)
    : "0.00"

  // Completion rate
  const completionRate = totalInspections > 0 
    ? (completedInspections / totalInspections * 100).toFixed(2)
    : "0.00"

  return {
    passRate: parseFloat(passRate),
    defectRate: parseFloat(defectRate),
    completionRate: parseFloat(completionRate),
    averageDefectsPerInspection: parseFloat(defectRate),
  }
}
