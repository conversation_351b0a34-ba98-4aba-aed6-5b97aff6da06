import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { qualityCertificates } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { qualityCertificateSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { createReferenceNotFoundError } from "@/lib/errors"

export async function GET(req: Request) {
  try {
    const url = new URL(req.url)
    const inspectionId = url.searchParams.get("inspection_id")
    const certificateType = url.searchParams.get("certificate_type")
    const status = url.searchParams.get("status") // active, expired, revoked

    // Build where conditions based on query parameters
    const whereConditions = []
    if (inspectionId) {
      whereConditions.push(eq(qualityCertificates.inspection_id, inspectionId))
    }
    if (certificateType) {
      whereConditions.push(eq(qualityCertificates.certificate_type, certificateType))
    }

    // Filter by status (active/expired/revoked)
    if (status === "expired") {
      const today = new Date().toISOString().split('T')[0]
      whereConditions.push(
        and(
          eq(qualityCertificates.valid_until, today), // Simplified - would use proper date comparison
        )
      )
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined

    const rows = await db.query.qualityCertificates.findMany({
      where: whereClause,
      orderBy: [desc(qualityCertificates.created_at)],
      with: {
        inspection: {
          with: {
            workOrder: {
              with: {
                product: true,
                salesContract: {
                  with: {
                    customer: true,
                  },
                },
              },
            },
            defects: true,
            results: {
              with: {
                standard: true,
              },
            },
          },
        },
      },
    })
    return createSuccessResponse(rows)
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function POST(req: NextRequest) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, qualityCertificateSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("qc")

    // Validate that inspection exists
    const inspection = await db.query.qualityInspections.findFirst({
      where: (inspections, { eq }) => eq(inspections.id, body.inspection_id),
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        results: true,
      },
    })

    if (!inspection) {
      throw createReferenceNotFoundError("Quality Inspection", body.inspection_id)
    }

    // Validate that inspection is completed and passed
    if (inspection.status !== "completed") {
      throw createReferenceNotFoundError(
        "Completed Inspection", 
        "Cannot generate certificate for incomplete inspection"
      )
    }

    // Generate certificate number if not provided
    const certificateNumber = body.certificate_number || 
      await generateCertificateNumber(body.certificate_type, inspection)

    const newCertificate = {
      id,
      inspection_id: body.inspection_id,
      certificate_number: certificateNumber,
      certificate_type: body.certificate_type,
      issued_date: body.issued_date,
      valid_until: body.valid_until,
      file_path: body.file_path,
    }

    await db.insert(qualityCertificates).values(newCertificate)

    const row = await db.query.qualityCertificates.findFirst({
      where: eq(qualityCertificates.id, id),
      with: {
        inspection: {
          with: {
            workOrder: {
              with: {
                product: true,
                salesContract: {
                  with: {
                    customer: true,
                  },
                },
              },
            },
            defects: true,
            results: {
              with: {
                standard: true,
              },
            },
          },
        },
      },
    })
    return createSuccessResponse(row, "Quality certificate created successfully", 201)
  } catch (error) {
    return createErrorResponse(error)
  }
}

// Helper function to generate certificate numbers
async function generateCertificateNumber(certificateType: string, inspection: any): Promise<string> {
  const year = new Date().getFullYear()
  const month = String(new Date().getMonth() + 1).padStart(2, '0')
  
  // Get count of certificates of this type this month
  const existingCount = await db.query.qualityCertificates.findMany({
    where: and(
      eq(qualityCertificates.certificate_type, certificateType),
      // In a real implementation, would filter by month/year properly
    ),
  })

  const sequence = String(existingCount.length + 1).padStart(3, '0')
  
  const prefix = {
    'COA': 'COA',
    'COC': 'COC', 
    'test_report': 'TR',
    'compliance': 'COMP'
  }[certificateType] || 'CERT'

  return `${prefix}-${year}${month}-${sequence}`
}
