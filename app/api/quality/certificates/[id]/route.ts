import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { qualityCertificates } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { z } from "zod"
import { 
  createCertificateNotFoundError,
  createInvalidQualityResultError 
} from "@/lib/errors"

const patchSchema = z.object({
  certificate_type: z.enum(["COA", "COC", "test_report", "compliance"]).optional(),
  issued_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  valid_until: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  file_path: z.string().max(500).optional(),
  status: z.enum(["active", "revoked", "expired"]).optional(),
})

export async function GET(_: Request, { params }: { params: { id: string } }) {
  try {
    const certificate = await db.query.qualityCertificates.findFirst({
      where: eq(qualityCertificates.id, params.id),
      with: {
        inspection: {
          with: {
            workOrder: {
              with: {
                product: true,
                salesContract: {
                  with: {
                    customer: true,
                  },
                },
              },
            },
            defects: {
              with: {
                product: true,
              },
            },
            results: {
              with: {
                standard: true,
              },
            },
          },
        },
      },
    })

    if (!certificate) {
      throw createCertificateNotFoundError(params.id)
    }

    // Add computed fields for certificate status
    const today = new Date().toISOString().split('T')[0]
    const isExpired = certificate.valid_until && certificate.valid_until < today
    
    const enrichedCertificate = {
      ...certificate,
      computed_status: isExpired ? "expired" : "active",
      days_until_expiry: certificate.valid_until 
        ? Math.ceil((new Date(certificate.valid_until).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        : null,
    }

    return createSuccessResponse(enrichedCertificate)
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function PATCH(req: Request, { params }: { params: { id: string } }) {
  try {
    const body = await req.json()
    const data = patchSchema.parse(body)

    // Check if certificate exists
    const existing = await db.query.qualityCertificates.findFirst({
      where: eq(qualityCertificates.id, params.id)
    })

    if (!existing) {
      throw createCertificateNotFoundError(params.id)
    }

    // Validate business rules
    if (data.status === "revoked") {
      // Log revocation reason (would be in a real audit system)
      console.log(`Certificate ${params.id} revoked`)
    }

    if (data.valid_until && data.issued_date) {
      if (data.valid_until <= data.issued_date) {
        throw createInvalidQualityResultError("Valid until date must be after issued date")
      }
    }

    const updated = await db
      .update(qualityCertificates)
      .set(data)
      .where(eq(qualityCertificates.id, params.id))
      .returning()

    if (updated.length === 0) {
      throw createCertificateNotFoundError(params.id)
    }

    // Fetch the updated record with relations
    const certificate = await db.query.qualityCertificates.findFirst({
      where: eq(qualityCertificates.id, params.id),
      with: {
        inspection: {
          with: {
            workOrder: {
              with: {
                product: true,
                salesContract: {
                  with: {
                    customer: true,
                  },
                },
              },
            },
            defects: {
              with: {
                product: true,
              },
            },
            results: {
              with: {
                standard: true,
              },
            },
          },
        },
      },
    })

    return createSuccessResponse(certificate, "Certificate updated successfully")
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function DELETE(_: Request, { params }: { params: { id: string } }) {
  try {
    // Check if certificate exists
    const existing = await db.query.qualityCertificates.findFirst({
      where: eq(qualityCertificates.id, params.id)
    })

    if (!existing) {
      throw createCertificateNotFoundError(params.id)
    }

    // In a real system, might want to soft delete or require special permissions
    const deleted = await db
      .delete(qualityCertificates)
      .where(eq(qualityCertificates.id, params.id))
      .returning()

    if (deleted.length === 0) {
      throw createCertificateNotFoundError(params.id)
    }

    return new Response(null, { status: 204 })
  } catch (error) {
    return createErrorResponse(error)
  }
}
