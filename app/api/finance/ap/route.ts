import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { apInvoices } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.apInvoices.findMany({
      where: eq(apInvoices.company_id, context.companyId),
      orderBy: [desc(apInvoices.date)],
      with: {
        supplier: true,
      },
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: Request, context) {
  try {
    const body = await req.json()
    const id = uid("api")
    const newInvoice = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      ...body,
    }

    await db.insert(apInvoices).values(newInvoice)

    // 🛡️ SECURE: Only return invoice if it belongs to current company
    const row = await db.query.apInvoices.findFirst({
      where: and(
        eq(apInvoices.id, id),
        eq(apInvoices.company_id, context.companyId)
      ),
      with: {
        supplier: true,
      },
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
})
