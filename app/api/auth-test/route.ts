import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Test if we can import Auth0 functions
    const { handleAuth } = await import('@auth0/nextjs-auth0')
    
    // Test environment variables
    const envCheck = {
      AUTH0_SECRET: !!process.env.AUTH0_SECRET,
      AUTH0_BASE_URL: process.env.AUTH0_BASE_URL,
      AUTH0_ISSUER_BASE_URL: process.env.AUTH0_ISSUER_BASE_URL,
      AUTH0_CLIENT_ID: !!process.env.AUTH0_CLIENT_ID,
      AUTH0_CLIENT_SECRET: !!process.env.AUTH0_CLIENT_SECRET,
    }
    
    return NextResponse.json({
      success: true,
      message: 'Auth0 test successful',
      hasHandleAuth: typeof handleAuth === 'function',
      envCheck
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Auth0 test failed'
    }, { status: 500 })
  }
}
