import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { qualityInspections, qualityDefects } from "@/lib/schema-postgres"
import { desc, eq, and, or } from "drizzle-orm"
import { z } from "zod"

const mobileInspectionUpdateSchema = z.object({
  status: z.enum(["in-progress", "completed", "failed"]).optional(),
  notes: z.string().max(500).optional(),
  inspector: z.string().max(100).optional(),
  quickResult: z.enum(["pass", "fail", "conditional"]).optional(),
  defects: z.array(z.object({
    defect_type: z.enum(["visual", "dimensional", "functional", "material"]),
    severity: z.enum(["minor", "major", "critical"]),
    description: z.string().max(200),
    quantity: z.string(),
  })).optional(),
})

export async function GET(req: Request) {
  try {
    const url = new URL(req.url)
    const inspector = url.searchParams.get("inspector")
    const status = url.searchParams.get("status")
    const limit = parseInt(url.searchParams.get("limit") || "20")
    const offset = parseInt(url.searchParams.get("offset") || "0")

    // Build where conditions for mobile-optimized queries
    const whereConditions = []
    
    if (inspector) {
      whereConditions.push(eq(qualityInspections.inspector, inspector))
    }
    
    if (status) {
      whereConditions.push(eq(qualityInspections.status, status))
    } else {
      // Default: show active inspections (scheduled, in-progress)
      whereConditions.push(
        or(
          eq(qualityInspections.status, "scheduled"),
          eq(qualityInspections.status, "in-progress")
        )
      )
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined

    // Get inspections with minimal data for mobile performance
    const inspections = await db.query.qualityInspections.findMany({
      where: whereClause,
      orderBy: [desc(qualityInspections.created_at)],
      limit: limit,
      offset: offset,
      with: {
        workOrder: {
          columns: {
            id: true,
            number: true,
            qty: true,
          },
          with: {
            product: {
              columns: {
                id: true,
                sku: true,
                name: true,
                unit: true,
              },
            },
            salesContract: {
              columns: {
                id: true,
                number: true,
              },
              with: {
                customer: {
                  columns: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
        defects: {
          columns: {
            id: true,
            defect_type: true,
            severity: true,
            description: true,
            status: true,
          },
        },
      },
    })

    // Transform data for mobile consumption
    const mobileInspections = inspections.map(inspection => ({
      id: inspection.id,
      type: inspection.inspection_type,
      status: inspection.status,
      inspector: inspection.inspector,
      scheduledDate: inspection.scheduled_date,
      completedDate: inspection.completed_date,
      notes: inspection.notes,
      priority: getPriority(inspection),
      workOrder: {
        id: inspection.workOrder?.id,
        number: inspection.workOrder?.number,
        quantity: inspection.workOrder?.qty,
        product: {
          id: inspection.workOrder?.product?.id,
          sku: inspection.workOrder?.product?.sku,
          name: inspection.workOrder?.product?.name,
          unit: inspection.workOrder?.product?.unit,
        },
        customer: {
          id: inspection.workOrder?.salesContract?.customer?.id,
          name: inspection.workOrder?.salesContract?.customer?.name,
        },
      },
      defects: {
        total: inspection.defects?.length || 0,
        critical: inspection.defects?.filter(d => d.severity === "critical").length || 0,
        major: inspection.defects?.filter(d => d.severity === "major").length || 0,
        minor: inspection.defects?.filter(d => d.severity === "minor").length || 0,
      },
      quickActions: getQuickActions(inspection),
    }))

    return createSuccessResponse({
      inspections: mobileInspections,
      pagination: {
        limit,
        offset,
        total: mobileInspections.length,
        hasMore: mobileInspections.length === limit,
      },
      summary: {
        scheduled: mobileInspections.filter(i => i.status === "scheduled").length,
        inProgress: mobileInspections.filter(i => i.status === "in-progress").length,
        completed: mobileInspections.filter(i => i.status === "completed").length,
      },
    })
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const data = mobileInspectionUpdateSchema.parse(body)
    const inspectionId = body.inspectionId

    if (!inspectionId) {
      throw new Error("inspectionId is required")
    }

    // Get current inspection
    const inspection = await db.query.qualityInspections.findFirst({
      where: eq(qualityInspections.id, inspectionId),
    })

    if (!inspection) {
      throw new Error("Inspection not found")
    }

    // Update inspection
    const updateData: any = {}
    if (data.status) updateData.status = data.status
    if (data.notes) updateData.notes = data.notes
    if (data.inspector) updateData.inspector = data.inspector
    if (data.status === "completed") updateData.completed_date = new Date().toISOString().split('T')[0]

    await db
      .update(qualityInspections)
      .set(updateData)
      .where(eq(qualityInspections.id, inspectionId))

    // Add defects if provided
    if (data.defects && data.defects.length > 0) {
      const defectInserts = data.defects.map(defect => ({
        id: `qd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        inspection_id: inspectionId,
        work_order_id: inspection.work_order_id,
        product_id: null, // Would need to get from work order
        defect_type: defect.defect_type,
        severity: defect.severity,
        quantity: defect.quantity,
        description: defect.description,
        status: "open",
      }))

      await db.insert(qualityDefects).values(defectInserts)
    }

    // Get updated inspection for response
    const updatedInspection = await db.query.qualityInspections.findFirst({
      where: eq(qualityInspections.id, inspectionId),
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        defects: true,
      },
    })

    return createSuccessResponse({
      inspection: updatedInspection,
      message: "Inspection updated successfully",
      quickResult: data.quickResult,
    })
  } catch (error) {
    return createErrorResponse(error)
  }
}

function getPriority(inspection: any): "high" | "medium" | "low" {
  // Determine priority based on inspection type and timing
  if (inspection.inspection_type === "pre-shipment") return "high"
  if (inspection.inspection_type === "final") return "high"
  if (inspection.inspection_type === "incoming") return "medium"
  return "low"
}

function getQuickActions(inspection: any): string[] {
  const actions = []
  
  if (inspection.status === "scheduled") {
    actions.push("start_inspection")
  }
  
  if (inspection.status === "in-progress") {
    actions.push("add_defect", "complete_inspection", "mark_failed")
  }
  
  if (inspection.status === "completed") {
    actions.push("view_certificate", "generate_report")
  }
  
  return actions
}
