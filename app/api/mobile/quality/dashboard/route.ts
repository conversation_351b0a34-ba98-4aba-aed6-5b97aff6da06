import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { 
  qualityInspections, 
  qualityDefects, 
  qualityCertificates,
  stockLots,
  workOrders 
} from "@/lib/schema-postgres"
import { eq, and, gte, lte, count, desc } from "drizzle-orm"

export async function GET(req: Request) {
  try {
    const url = new URL(req.url)
    const inspector = url.searchParams.get("inspector")
    const period = parseInt(url.searchParams.get("period") || "7") // days

    // Calculate date range
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - period * 24 * 60 * 60 * 1000)

    // Get mobile dashboard data
    const dashboardData = await getMobileDashboardData(startDate, endDate, inspector)

    return createSuccessResponse({
      period: `Last ${period} days`,
      inspector: inspector || "All Inspectors",
      data: dashboardData,
      lastUpdated: new Date().toISOString(),
    })
  } catch (error) {
    return createErrorResponse(error)
  }
}

async function getMobileDashboardData(startDate: Date, endDate: Date, inspector?: string) {
  // Base filters
  const baseFilters = [
    gte(qualityInspections.created_at, startDate),
    lte(qualityInspections.created_at, endDate)
  ]

  if (inspector) {
    baseFilters.push(eq(qualityInspections.inspector, inspector))
  }

  // Get inspection summary
  const inspectionSummary = await getInspectionSummary(baseFilters)
  
  // Get today's tasks
  const todaysTasks = await getTodaysTasks(inspector)
  
  // Get quality alerts
  const qualityAlerts = await getQualityAlerts()
  
  // Get recent activity
  const recentActivity = await getRecentActivity(inspector)
  
  // Get performance metrics
  const performanceMetrics = await getPerformanceMetrics(baseFilters)

  return {
    summary: inspectionSummary,
    todaysTasks,
    alerts: qualityAlerts,
    recentActivity,
    performance: performanceMetrics,
    quickStats: {
      pendingInspections: todaysTasks.filter(t => t.status === "scheduled").length,
      inProgressInspections: todaysTasks.filter(t => t.status === "in-progress").length,
      completedToday: todaysTasks.filter(t => t.status === "completed").length,
      criticalDefects: qualityAlerts.filter(a => a.severity === "critical").length,
    },
  }
}

async function getInspectionSummary(baseFilters: any[]) {
  // Total inspections
  const totalInspections = await db
    .select({ count: count() })
    .from(qualityInspections)
    .where(and(...baseFilters))

  // Inspections by status
  const inspectionsByStatus = await db
    .select({ 
      status: qualityInspections.status, 
      count: count() 
    })
    .from(qualityInspections)
    .where(and(...baseFilters))
    .groupBy(qualityInspections.status)

  // Pass rate calculation
  const completedInspections = inspectionsByStatus.find(s => s.status === "completed")?.count || 0
  const failedInspections = inspectionsByStatus.find(s => s.status === "failed")?.count || 0
  const passRate = completedInspections + failedInspections > 0 
    ? (completedInspections / (completedInspections + failedInspections) * 100).toFixed(1)
    : "0.0"

  return {
    total: totalInspections[0]?.count || 0,
    completed: completedInspections,
    failed: failedInspections,
    inProgress: inspectionsByStatus.find(s => s.status === "in-progress")?.count || 0,
    scheduled: inspectionsByStatus.find(s => s.status === "scheduled")?.count || 0,
    passRate: parseFloat(passRate),
  }
}

async function getTodaysTasks(inspector?: string) {
  const today = new Date().toISOString().split('T')[0]
  
  const whereConditions = [
    eq(qualityInspections.scheduled_date, today)
  ]
  
  if (inspector) {
    whereConditions.push(eq(qualityInspections.inspector, inspector))
  }

  const tasks = await db.query.qualityInspections.findMany({
    where: and(...whereConditions),
    orderBy: [desc(qualityInspections.created_at)],
    limit: 10,
    with: {
      workOrder: {
        columns: {
          id: true,
          number: true,
        },
        with: {
          product: {
            columns: {
              id: true,
              name: true,
              sku: true,
            },
          },
        },
      },
    },
  })

  return tasks.map(task => ({
    id: task.id,
    type: task.inspection_type,
    status: task.status,
    workOrderNumber: task.workOrder?.number,
    productName: task.workOrder?.product?.name,
    productSku: task.workOrder?.product?.sku,
    scheduledTime: task.scheduled_date,
    priority: getPriority(task.inspection_type),
    estimatedDuration: getEstimatedDuration(task.inspection_type),
  }))
}

async function getQualityAlerts() {
  const alerts = []

  // Critical defects in last 24 hours
  const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000)
  const criticalDefects = await db.query.qualityDefects.findMany({
    where: and(
      eq(qualityDefects.severity, "critical"),
      gte(qualityDefects.created_at, yesterday),
      eq(qualityDefects.status, "open")
    ),
    limit: 5,
    with: {
      product: {
        columns: {
          name: true,
          sku: true,
        },
      },
    },
  })

  for (const defect of criticalDefects) {
    alerts.push({
      id: defect.id,
      type: "critical_defect",
      severity: "critical",
      title: "Critical Defect Found",
      message: `${defect.defect_type} defect in ${defect.product?.name}`,
      timestamp: defect.created_at,
      actionRequired: true,
    })
  }

  // Expiring certificates
  const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  const today = new Date().toISOString().split('T')[0]
  
  const expiringCertificates = await db.query.qualityCertificates.findMany({
    where: and(
      gte(qualityCertificates.valid_until, today),
      lte(qualityCertificates.valid_until, nextWeek)
    ),
    limit: 3,
  })

  for (const cert of expiringCertificates) {
    alerts.push({
      id: cert.id,
      type: "certificate_expiring",
      severity: "warning",
      title: "Certificate Expiring Soon",
      message: `Certificate ${cert.certificate_number} expires ${cert.valid_until}`,
      timestamp: cert.created_at,
      actionRequired: false,
    })
  }

  // Overdue inspections
  const overdueInspections = await db.query.qualityInspections.findMany({
    where: and(
      lte(qualityInspections.scheduled_date, today),
      eq(qualityInspections.status, "scheduled")
    ),
    limit: 3,
    with: {
      workOrder: {
        columns: {
          number: true,
        },
      },
    },
  })

  for (const inspection of overdueInspections) {
    alerts.push({
      id: inspection.id,
      type: "overdue_inspection",
      severity: "high",
      title: "Overdue Inspection",
      message: `${inspection.inspection_type} inspection for WO ${inspection.workOrder?.number}`,
      timestamp: inspection.created_at,
      actionRequired: true,
    })
  }

  return alerts.sort((a, b) => {
    const severityOrder = { critical: 0, high: 1, warning: 2 }
    return severityOrder[a.severity] - severityOrder[b.severity]
  })
}

async function getRecentActivity(inspector?: string) {
  const whereConditions = []
  
  if (inspector) {
    whereConditions.push(eq(qualityInspections.inspector, inspector))
  }

  const recentInspections = await db.query.qualityInspections.findMany({
    where: whereConditions.length > 0 ? and(...whereConditions) : undefined,
    orderBy: [desc(qualityInspections.created_at)],
    limit: 5,
    with: {
      workOrder: {
        columns: {
          number: true,
        },
        with: {
          product: {
            columns: {
              name: true,
            },
          },
        },
      },
    },
  })

  return recentInspections.map(inspection => ({
    id: inspection.id,
    action: getActionDescription(inspection.status),
    target: `${inspection.inspection_type} inspection`,
    details: `WO ${inspection.workOrder?.number} - ${inspection.workOrder?.product?.name}`,
    timestamp: inspection.created_at,
    status: inspection.status,
  }))
}

async function getPerformanceMetrics(baseFilters: any[]) {
  // Average inspection time (simulated)
  const avgInspectionTime = "2.5 hours"
  
  // Defect rate
  const totalInspections = await db
    .select({ count: count() })
    .from(qualityInspections)
    .where(and(...baseFilters))

  const totalDefects = await db
    .select({ count: count() })
    .from(qualityDefects)
    .innerJoin(qualityInspections, eq(qualityDefects.inspection_id, qualityInspections.id))
    .where(and(...baseFilters))

  const defectRate = totalInspections[0]?.count > 0 
    ? (totalDefects[0]?.count / totalInspections[0]?.count).toFixed(2)
    : "0.00"

  return {
    averageInspectionTime: avgInspectionTime,
    defectRate: parseFloat(defectRate),
    inspectionsPerDay: (totalInspections[0]?.count || 0) / 7, // Assuming 7-day period
    efficiency: "94%", // Simulated
  }
}

function getPriority(inspectionType: string): "high" | "medium" | "low" {
  const priorityMap = {
    "pre-shipment": "high",
    "final": "high",
    "in-process": "medium",
    "incoming": "low",
  }
  return priorityMap[inspectionType] || "medium"
}

function getEstimatedDuration(inspectionType: string): string {
  const durationMap = {
    "pre-shipment": "1-2 hours",
    "final": "2-3 hours", 
    "in-process": "1 hour",
    "incoming": "30 minutes",
  }
  return durationMap[inspectionType] || "1 hour"
}

function getActionDescription(status: string): string {
  const actionMap = {
    "scheduled": "Scheduled",
    "in-progress": "Started",
    "completed": "Completed",
    "failed": "Failed",
  }
  return actionMap[status] || "Updated"
}
