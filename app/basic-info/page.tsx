"use client"

import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import SimpleTable from "./SimpleTable"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"

export default function BasicInfoPage() {
  return (
    <AppShell>
      <div className="grid gap-6">
        <SamplesCard />
        <CustomersCard />
        <SuppliersCard />
        <ProductsCard />
      </div>
    </AppShell>
  )
}

function SamplesCard() {
  const { t } = useI18n()
  const [rows, setRows] = useState<any[]>([])
  const [form, setForm] = useState({ code: "", name: "", specification: "", moq: 0, available_from_stock: false })

  async function load() {
    setRows(await safeJson("/api/samples", []))
  }
  useEffect(() => {
    load()
  }, [])

  async function add() {
    if (!form.code || !form.name) return
    await fetch("/api/samples", { method: "POST", body: JSON.stringify(form) })
    setForm({ code: "", name: "", specification: "", moq: 0, available_from_stock: false })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/samples/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("basic.samples.title")}</CardTitle>
        <CardDescription>{t("basic.samples.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-5">
          <div className="grid gap-1">
            <Label>{t("field.code")}</Label>
            <Input value={form.code} onChange={(e) => setForm({ ...form, code: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.name")}</Label>
            <Input value={form.name} onChange={(e) => setForm({ ...form, name: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.spec")}</Label>
            <Input value={form.specification} onChange={(e) => setForm({ ...form, specification: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.moq")}</Label>
            <Input type="number" value={form.moq} onChange={(e) => setForm({ ...form, moq: Number(e.target.value) })} />
          </div>
          <div className="flex items-end gap-2">
            <Switch
              checked={form.available_from_stock}
              onCheckedChange={(v) => setForm({ ...form, available_from_stock: v })}
            />
            <Label>{t("field.inStock")}</Label>
          </div>
        </div>
        <div className="flex justify-end">
          <Button onClick={add}>{t("action.add")}</Button>
        </div>
        <SimpleTable
          headers={[t("field.code"), t("field.name"), t("field.spec"), t("field.moq"), t("field.inStock"), ""]}
          rows={rows.map((s) => [
            s.code,
            s.name,
            s.specification || "",
            s.moq ?? 0,
            s.available_from_stock ? "Yes" : "No",
            <Button key="d" size="sm" variant="ghost" onClick={() => remove(s.id)}>
              {t("action.delete")}
            </Button>,
          ])}
          emptyText={t("table.noData")}
        />
      </CardContent>
    </Card>
  )
}

function CustomersCard() {
  const { t } = useI18n()
  const [rows, setRows] = useState<any[]>([])
  const [form, setForm] = useState({ name: "", contact_name: "", incoterm: "", payment_term: "", address: "" })

  async function load() {
    setRows(await safeJson("/api/customers", []))
  }
  useEffect(() => {
    load()
  }, [])

  async function add() {
    if (!form.name) return
    await fetch("/api/customers", { method: "POST", body: JSON.stringify(form) })
    setForm({ name: "", contact_name: "", incoterm: "", payment_term: "", address: "" })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/customers/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("basic.customers.title")}</CardTitle>
        <CardDescription>{t("basic.customers.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-5">
          <div className="grid gap-1">
            <Label>{t("field.name")}</Label>
            <Input value={form.name} onChange={(e) => setForm({ ...form, name: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.contact")}</Label>
            <Input value={form.contact_name} onChange={(e) => setForm({ ...form, contact_name: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.incoterm")}</Label>
            <Input value={form.incoterm} onChange={(e) => setForm({ ...form, incoterm: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.paymentTerm")}</Label>
            <Input value={form.payment_term} onChange={(e) => setForm({ ...form, payment_term: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.address")}</Label>
            <Input value={form.address} onChange={(e) => setForm({ ...form, address: e.target.value })} />
          </div>
        </div>
        <div className="flex justify-end">
          <Button onClick={add}>{t("action.add")}</Button>
        </div>
        <SimpleTable
          headers={[t("field.name"), t("field.contact"), t("field.incoterm"), t("field.paymentTerm"), ""]}
          rows={rows.map((c) => [
            c.name,
            c.contact_name || "",
            c.incoterm || "",
            c.payment_term || "",
            <Button key="d" size="sm" variant="ghost" onClick={() => remove(c.id)}>
              {t("action.delete")}
            </Button>,
          ])}
          emptyText={t("table.noData")}
        />
      </CardContent>
    </Card>
  )
}

function SuppliersCard() {
  const { t } = useI18n()
  const [rows, setRows] = useState<any[]>([])
  const [form, setForm] = useState({ name: "", contact_name: "", address: "" })

  async function load() {
    setRows(await safeJson("/api/suppliers", []))
  }
  useEffect(() => {
    load()
  }, [])

  async function add() {
    if (!form.name) return
    await fetch("/api/suppliers", { method: "POST", body: JSON.stringify(form) })
    setForm({ name: "", contact_name: "", address: "" })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/suppliers/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("basic.suppliers.title")}</CardTitle>
        <CardDescription>{t("basic.suppliers.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-4">
          <div className="grid gap-1">
            <Label>{t("field.name")}</Label>
            <Input value={form.name} onChange={(e) => setForm({ ...form, name: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.contact")}</Label>
            <Input value={form.contact_name} onChange={(e) => setForm({ ...form, contact_name: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.address")}</Label>
            <Input value={form.address} onChange={(e) => setForm({ ...form, address: e.target.value })} />
          </div>
        </div>
        <div className="flex justify-end">
          <Button onClick={add}>{t("action.add")}</Button>
        </div>
        <SimpleTable
          headers={[t("field.name"), t("field.contact"), t("field.address"), ""]}
          rows={rows.map((s) => [
            s.name,
            s.contact_name || "",
            s.address || "",
            <Button key="d" size="sm" variant="ghost" onClick={() => remove(s.id)}>
              {t("action.delete")}
            </Button>,
          ])}
          emptyText={t("table.noData")}
        />
      </CardContent>
    </Card>
  )
}

function ProductsCard() {
  const { t } = useI18n()
  const [rows, setRows] = useState<any[]>([])
  const [form, setForm] = useState({ sku: "", name: "", unit: "pcs", hs_code: "", origin: "", package: "" })

  async function load() {
    setRows(await safeJson("/api/products", []))
  }
  useEffect(() => {
    load()
  }, [])

  async function add() {
    if (!form.sku || !form.name || !form.unit) return
    await fetch("/api/products", { method: "POST", body: JSON.stringify(form) })
    setForm({ sku: "", name: "", unit: "pcs", hs_code: "", origin: "", package: "" })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/products/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("basic.products.title")}</CardTitle>
        <CardDescription>{t("basic.products.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-6">
          <div className="grid gap-1">
            <Label>{t("field.sku")}</Label>
            <Input value={form.sku} onChange={(e) => setForm({ ...form, sku: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.name")}</Label>
            <Input value={form.name} onChange={(e) => setForm({ ...form, name: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.unit")}</Label>
            <Input value={form.unit} onChange={(e) => setForm({ ...form, unit: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.hsCode")}</Label>
            <Input value={form.hs_code} onChange={(e) => setForm({ ...form, hs_code: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.origin")}</Label>
            <Input value={form.origin} onChange={(e) => setForm({ ...form, origin: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.packaging")}</Label>
            <Input value={form.package} onChange={(e) => setForm({ ...form, package: e.target.value })} />
          </div>
        </div>
        <div className="flex justify-end">
          <Button onClick={add}>{t("action.add")}</Button>
        </div>
        <SimpleTable
          headers={[t("field.sku"), t("field.name"), t("field.unit"), t("field.hsCode"), t("field.origin"), ""]}
          rows={rows.map((p) => [
            p.sku,
            p.name,
            p.unit,
            p.hs_code || "",
            p.origin || "",
            <Button key="d" size="sm" variant="ghost" onClick={() => remove(p.id)}>
              {t("action.delete")}
            </Button>,
          ])}
          emptyText={t("table.noData")}
        />
      </CardContent>
    </Card>
  )
}
