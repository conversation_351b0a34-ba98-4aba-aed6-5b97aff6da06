"use client"

import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"

export default function ContractsPage() {
  return (
    <AppShell>
      <div className="grid gap-6">
        <SalesContractsCard />
        <PurchaseContractsCard />
      </div>
    </AppShell>
  )
}

function SalesContractsCard() {
  const { t } = useI18n()
  const [customers, setCustomers] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [rows, setRows] = useState<any[]>([])
  const [items, setItems] = useState<Array<{ product_id?: string; qty: number; price: number }>>([])
  const [form, setForm] = useState({ number: "", customer_id: "", currency: "USD" })

  async function load() {
    const [cs, ps, rs] = await Promise.all([
      safeJson("/api/customers", []),
      safeJson("/api/products", []),
      safeJson("/api/contracts/sales", []),
    ])
    // Ensure we always have arrays
    setCustomers(Array.isArray(cs) ? cs : [])
    setProducts(Array.isArray(ps) ? ps : [])
    setRows(Array.isArray(rs) ? rs : [])
  }
  useEffect(() => {
    load()
  }, [])

  async function create() {
    if (!form.number || !form.customer_id || items.length === 0) return
    await fetch("/api/contracts/sales", {
      method: "POST",
      body: JSON.stringify({ ...form, items }),
    })
    setForm({ number: "", customer_id: "", currency: "USD" })
    setItems([])
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/contracts/sales/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("contracts.sales.title")}</CardTitle>
        <CardDescription>{t("contracts.sales.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-4">
          <div className="grid gap-1">
            <Label>{t("field.number")}</Label>
            <Input value={form.number} onChange={(e) => setForm({ ...form, number: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.customer")}</Label>
            <Select value={form.customer_id} onValueChange={(v) => setForm({ ...form, customer_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {customers.map((c) => (
                  <SelectItem key={c.id} value={c.id}>
                    {c.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.currency")}</Label>
            <Input value={form.currency} onChange={(e) => setForm({ ...form, currency: e.target.value })} />
          </div>
          <div className="flex items-end">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setItems([...items, { product_id: products[0]?.id, qty: 1, price: 1 }])}
            >
              {t("action.addItem")}
            </Button>
          </div>
        </div>

        <div className="border rounded-md p-3 space-y-2">
          {items.length === 0 && <div className="text-sm text-muted-foreground">{t("table.noData")}</div>}
          {items.map((it, idx) => (
            <div key={idx} className="grid gap-2 md:grid-cols-4 items-end">
              <div>
                <Label>{t("field.product")}</Label>
                <Select
                  value={it.product_id || ""}
                  onValueChange={(v) => {
                    const copy = [...items]
                    copy[idx] = { ...copy[idx], product_id: v }
                    setItems(copy)
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((p) => (
                      <SelectItem key={p.id} value={p.id}>
                        {p.sku} - {p.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{t("field.qty")}</Label>
                <Input
                  type="number"
                  value={it.qty}
                  onChange={(e) => {
                    const copy = [...items]
                    copy[idx] = { ...copy[idx], qty: Number(e.target.value) }
                    setItems(copy)
                  }}
                />
              </div>
              <div>
                <Label>{t("field.price")}</Label>
                <Input
                  type="number"
                  value={it.price}
                  onChange={(e) => {
                    const copy = [...items]
                    copy[idx] = { ...copy[idx], price: Number(e.target.value) }
                    setItems(copy)
                  }}
                />
              </div>
              <div className="flex items-center gap-2">
                <div className="text-sm text-muted-foreground">
                  {t("field.total")}: {(it.qty * it.price).toFixed(2)}
                </div>
                <Button variant="ghost" size="sm" onClick={() => setItems(items.filter((_, i) => i !== idx))}>
                  {t("action.remove")}
                </Button>
              </div>
            </div>
          ))}
        </div>
        <div className="flex justify-end">
          <Button onClick={create}>{t("action.createContract")}</Button>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("field.number")}</TableHead>
              <TableHead>{t("field.customer")}</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Items</TableHead>
              <TableHead>{t("table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((r) => (
              <TableRow key={r.id}>
                <TableCell>{r.number}</TableCell>
                <TableCell>{r.customer_name}</TableCell>
                <TableCell>{r.status}</TableCell>
                <TableCell>{r.item_count}</TableCell>
                <TableCell className="text-right">
                  <Button size="sm" variant="ghost" onClick={() => remove(r.id)}>
                    {t("action.delete")}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {rows.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-muted-foreground">
                  {t("contracts.sales.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

function PurchaseContractsCard() {
  const { t } = useI18n()
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [rows, setRows] = useState<any[]>([])
  const [items, setItems] = useState<Array<{ product_id?: string; qty: number; price: number }>>([])
  const [form, setForm] = useState({ number: "", supplier_id: "", currency: "USD" })

  async function load() {
    const [ss, ps, rs] = await Promise.all([
      safeJson("/api/suppliers", []),
      safeJson("/api/products", []),
      safeJson("/api/contracts/purchase", []),
    ])
    // Ensure we always have arrays
    setSuppliers(Array.isArray(ss) ? ss : [])
    setProducts(Array.isArray(ps) ? ps : [])
    setRows(Array.isArray(rs) ? rs : [])
  }
  useEffect(() => {
    load()
  }, [])

  async function create() {
    if (!form.number || !form.supplier_id || items.length === 0) return
    await fetch("/api/contracts/purchase", { method: "POST", body: JSON.stringify({ ...form, items }) })
    setForm({ number: "", supplier_id: "", currency: "USD" })
    setItems([])
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/contracts/purchase/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("contracts.purchase.title")}</CardTitle>
        <CardDescription>{t("contracts.purchase.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-4">
          <div className="grid gap-1">
            <Label>{t("field.number")}</Label>
            <Input value={form.number} onChange={(e) => setForm({ ...form, number: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.supplier")}</Label>
            <Select value={form.supplier_id} onValueChange={(v) => setForm({ ...form, supplier_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map((s) => (
                  <SelectItem key={s.id} value={s.id}>
                    {s.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.currency")}</Label>
            <Input value={form.currency} onChange={(e) => setForm({ ...form, currency: e.target.value })} />
          </div>
          <div className="flex items-end">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setItems([...items, { product_id: products[0]?.id, qty: 1, price: 1 }])}
            >
              {t("action.addItem")}
            </Button>
          </div>
        </div>

        <div className="border rounded-md p-3 space-y-2">
          {items.length === 0 && <div className="text-sm text-muted-foreground">{t("table.noData")}</div>}
          {items.map((it, idx) => (
            <div key={idx} className="grid gap-2 md:grid-cols-4 items-end">
              <div>
                <Label>{t("field.product")}</Label>
                <Select
                  value={it.product_id || ""}
                  onValueChange={(v) => {
                    const copy = [...items]
                    copy[idx] = { ...copy[idx], product_id: v }
                    setItems(copy)
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((p) => (
                      <SelectItem key={p.id} value={p.id}>
                        {p.sku} - {p.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{t("field.qty")}</Label>
                <Input
                  type="number"
                  value={it.qty}
                  onChange={(e) => {
                    const copy = [...items]
                    copy[idx] = { ...copy[idx], qty: Number(e.target.value) }
                    setItems(copy)
                  }}
                />
              </div>
              <div>
                <Label>{t("field.price")}</Label>
                <Input
                  type="number"
                  value={it.price}
                  onChange={(e) => {
                    const copy = [...items]
                    copy[idx] = { ...copy[idx], price: Number(e.target.value) }
                    setItems(copy)
                  }}
                />
              </div>
              <div className="flex items-center gap-2">
                <div className="text-sm text-muted-foreground">
                  {t("field.total")}: {(it.qty * it.price).toFixed(2)}
                </div>
                <Button variant="ghost" size="sm" onClick={() => setItems(items.filter((_, i) => i !== idx))}>
                  {t("action.remove")}
                </Button>
              </div>
            </div>
          ))}
        </div>
        <div className="flex justify-end">
          <Button onClick={create}>{t("action.createPO")}</Button>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("field.number")}</TableHead>
              <TableHead>{t("field.supplier")}</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Items</TableHead>
              <TableHead>{t("table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((r) => (
              <TableRow key={r.id}>
                <TableCell>{r.number}</TableCell>
                <TableCell>{r.supplier_name}</TableCell>
                <TableCell>{r.status}</TableCell>
                <TableCell>{r.item_count}</TableCell>
                <TableCell className="text-right">
                  <Button size="sm" variant="ghost" onClick={() => remove(r.id)}>
                    {t("action.delete")}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {rows.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-muted-foreground">
                  {t("contracts.purchase.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
