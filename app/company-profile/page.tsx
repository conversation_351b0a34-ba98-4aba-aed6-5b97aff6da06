"use client"

import { AppShell } from "@/components/app-shell"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { useUser } from '@auth0/nextjs-auth0/client'
import { AlertCircle, Building, CheckCircle, Edit, Save, X } from "lucide-react"
import { useEffect, useState } from "react"
import { toast } from "sonner"

interface CompanyProfile {
  id?: string
  name: string
  legal_name: string
  email: string
  phone: string
  website: string
  address_line1: string
  address_line2: string
  city: string
  state_province: string
  postal_code: string
  country: string
  industry: string
  business_type: string
  employee_count: string
  annual_revenue: string
  registration_number: string
  tax_id: string
  vat_number: string
  bank_name: string
  bank_account: string
  bank_swift: string
  bank_address: string
  export_license: string
  customs_code: string
  preferred_incoterms: string
  preferred_payment_terms: string
  onboarding_completed: string
  created_at?: string
  updated_at?: string
}

export default function CompanyProfilePage() {
  const { user, isLoading } = useUser()
  const [profile, setProfile] = useState<CompanyProfile | null>(null)
  const [editMode, setEditMode] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (user) {
      fetchProfile()
    }
  }, [user])

  // Helper function to ensure all profile values are strings (not null/undefined)
  const normalizeProfile = (profileData: any): CompanyProfile => {
    return {
      ...profileData,
      name: profileData.name || '',
      legal_name: profileData.legal_name || '',
      email: profileData.email || '',
      phone: profileData.phone || '',
      website: profileData.website || '',
      country: profileData.country || '',
      address_line1: profileData.address_line1 || '',
      address_line2: profileData.address_line2 || '',
      city: profileData.city || '',
      state_province: profileData.state_province || '',
      postal_code: profileData.postal_code || '',
      industry: profileData.industry || '',
      business_type: profileData.business_type || '',
      employee_count: profileData.employee_count || '',
      annual_revenue: profileData.annual_revenue || '',
      registration_number: profileData.registration_number || '',
      tax_id: profileData.tax_id || '',
      vat_number: profileData.vat_number || '',
      bank_name: profileData.bank_name || '',
      bank_account: profileData.bank_account || '',
      bank_swift: profileData.bank_swift || '',
      bank_address: profileData.bank_address || '',
      export_license: profileData.export_license || '',
      customs_code: profileData.customs_code || '',
      preferred_incoterms: profileData.preferred_incoterms || '',
      preferred_payment_terms: profileData.preferred_payment_terms || '',
      onboarding_completed: profileData.onboarding_completed || 'false',
      created_at: profileData.created_at,
      updated_at: profileData.updated_at,
    }
  }

  const fetchProfile = async () => {
    try {
      const response = await fetch('/api/companies')
      if (response.ok) {
        const result = await response.json()
        console.log('API Response:', result) // Debug log
        setProfile(normalizeProfile(result.data.company))
      } else {
        console.error('Failed to fetch company profile')
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!profile) return

    setSaving(true)
    try {
      const response = await fetch('/api/companies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profile),
      })

      if (response.ok) {
        const result = await response.json()
        setProfile(result.data.company)
        setEditMode(false)
        toast.success('Company profile updated successfully!')
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to update profile')
      }
    } catch (error) {
      console.error('Error saving profile:', error)
      toast.error('Failed to update profile')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setEditMode(false)
    fetchProfile() // Reset to original data
  }

  const updateProfile = (field: keyof CompanyProfile, value: string) => {
    if (profile) {
      setProfile({ ...profile, [field]: value })
    }
  }

  const getCompletionPercentage = () => {
    if (!profile) return 0

    const requiredFields = [
      'name', 'email', 'address_line1', 'city', 'country',
      'industry', 'business_type', 'employee_count'
    ]

    const optionalFields = [
      'legal_name', 'phone', 'website', 'registration_number', 'tax_id',
      'bank_name', 'export_license', 'preferred_incoterms'
    ]

    const allFields = [...requiredFields, ...optionalFields]
    const filledFields = allFields.filter(field => profile[field as keyof CompanyProfile])

    return Math.round((filledFields.length / allFields.length) * 100)
  }

  if (isLoading || loading) {
    return (
      <AppShell>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading company profile...</p>
          </div>
        </div>
      </AppShell>
    )
  }

  if (!profile) {
    return (
      <AppShell>
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardHeader className="text-center">
              <Building className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <CardTitle>No Company Profile Found</CardTitle>
              <CardDescription>
                It looks like you haven't completed your company profile setup yet.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Button asChild>
                <a href="/onboarding">Complete Company Setup</a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppShell>
    )
  }

  const completionPercentage = getCompletionPercentage()

  return (
    <AppShell>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Company Profile</h1>
            <p className="text-muted-foreground">Manage your company information and settings</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                {completionPercentage === 100 ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                )}
                <span className="text-sm font-medium">{completionPercentage}% Complete</span>
              </div>
              <Badge variant={completionPercentage === 100 ? "default" : "secondary"}>
                {completionPercentage === 100 ? "Complete" : "Incomplete"}
              </Badge>
            </div>
            {editMode ? (
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleCancel} disabled={saving}>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button onClick={handleSave} disabled={saving}>
                  <Save className="h-4 w-4 mr-2" />
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            ) : (
              <Button onClick={() => setEditMode(true)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            )}
          </div>
        </div>

        {/* Profile Content */}
        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Information</TabsTrigger>
            <TabsTrigger value="business">Business Details</TabsTrigger>
            <TabsTrigger value="banking">Banking</TabsTrigger>
            <TabsTrigger value="export">Export & Trade</TabsTrigger>
          </TabsList>

          <TabsContent value="basic">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Your company's basic contact and address information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Company Name *</Label>
                    {editMode ? (
                      <Input
                        id="name"
                        value={profile.name}
                        onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                        placeholder="Enter company name"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.name}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="legal_name">Legal Company Name</Label>
                    {editMode ? (
                      <Input
                        id="legal_name"
                        value={profile.legal_name}
                        onChange={(e) => setProfile({ ...profile, legal_name: e.target.value })}
                        placeholder="Full legal name"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.legal_name}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    {editMode ? (
                      <Input
                        id="email"
                        type="email"
                        value={profile.email}
                        onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                        placeholder="<EMAIL>"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.email}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    {editMode ? (
                      <Input
                        id="phone"
                        value={profile.phone}
                        onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                        placeholder="+****************"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.phone}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    {editMode ? (
                      <Input
                        id="website"
                        value={profile.website}
                        onChange={(e) => setProfile({ ...profile, website: e.target.value })}
                        placeholder="https://www.company.com"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.website}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="country">Country *</Label>
                    {editMode ? (
                      <Select value={profile.country} onValueChange={(value) => setProfile({ ...profile, country: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="US">United States</SelectItem>
                          <SelectItem value="CN">China</SelectItem>
                          <SelectItem value="GB">United Kingdom</SelectItem>
                          <SelectItem value="DE">Germany</SelectItem>
                          <SelectItem value="FR">France</SelectItem>
                          <SelectItem value="JP">Japan</SelectItem>
                          <SelectItem value="IN">India</SelectItem>
                          <SelectItem value="BR">Brazil</SelectItem>
                          <SelectItem value="CA">Canada</SelectItem>
                          <SelectItem value="AU">Australia</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.country}</div>
                    )}
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="address_line1">Street Address *</Label>
                    {editMode ? (
                      <Input
                        id="address_line1"
                        value={profile.address_line1}
                        onChange={(e) => setProfile({ ...profile, address_line1: e.target.value })}
                        placeholder="Street address"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.address_line1}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address_line2">Address Line 2</Label>
                    {editMode ? (
                      <Input
                        id="address_line2"
                        value={profile.address_line2 || ''}
                        onChange={(e) => setProfile({ ...profile, address_line2: e.target.value })}
                        placeholder="Apartment, suite, etc."
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.address_line2 || 'Not provided'}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="city">City *</Label>
                    {editMode ? (
                      <Input
                        id="city"
                        value={profile.city}
                        onChange={(e) => setProfile({ ...profile, city: e.target.value })}
                        placeholder="City"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.city}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="state_province">State/Province</Label>
                    {editMode ? (
                      <Input
                        id="state_province"
                        value={profile.state_province}
                        onChange={(e) => setProfile({ ...profile, state_province: e.target.value })}
                        placeholder="State or Province"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.state_province}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="postal_code">Postal Code</Label>
                    {editMode ? (
                      <Input
                        id="postal_code"
                        value={profile.postal_code}
                        onChange={(e) => setProfile({ ...profile, postal_code: e.target.value })}
                        placeholder="12345"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.postal_code}</div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="business">
            <Card>
              <CardHeader>
                <CardTitle>Business Details</CardTitle>
                <CardDescription>
                  Business registration and operational information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="industry">Industry *</Label>
                    {editMode ? (
                      <Select value={profile.industry} onValueChange={(value) => setProfile({ ...profile, industry: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select industry" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="textile">Textile & Apparel</SelectItem>
                          <SelectItem value="electronics">Electronics</SelectItem>
                          <SelectItem value="automotive">Automotive</SelectItem>
                          <SelectItem value="food">Food & Beverage</SelectItem>
                          <SelectItem value="chemicals">Chemicals</SelectItem>
                          <SelectItem value="machinery">Machinery</SelectItem>
                          <SelectItem value="furniture">Furniture</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="p-3 bg-muted rounded-md capitalize">{profile.industry}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="business_type">Business Type</Label>
                    {editMode ? (
                      <Select value={profile.business_type} onValueChange={(value) => setProfile({ ...profile, business_type: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select business type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="llc">LLC</SelectItem>
                          <SelectItem value="corporation">Corporation</SelectItem>
                          <SelectItem value="partnership">Partnership</SelectItem>
                          <SelectItem value="sole_proprietorship">Sole Proprietorship</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="p-3 bg-muted rounded-md uppercase">{profile.business_type}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="employee_count">Employee Count</Label>
                    {editMode ? (
                      <Select value={profile.employee_count} onValueChange={(value) => setProfile({ ...profile, employee_count: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select employee count" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1-10">1-10 employees</SelectItem>
                          <SelectItem value="11-50">11-50 employees</SelectItem>
                          <SelectItem value="51-200">51-200 employees</SelectItem>
                          <SelectItem value="201-500">201-500 employees</SelectItem>
                          <SelectItem value="500+">500+ employees</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.employee_count} employees</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="annual_revenue">Annual Revenue</Label>
                    {editMode ? (
                      <Select value={profile.annual_revenue} onValueChange={(value) => setProfile({ ...profile, annual_revenue: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select annual revenue" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="under-1m">Under $1M</SelectItem>
                          <SelectItem value="1m-5m">$1M - $5M</SelectItem>
                          <SelectItem value="5m-10m">$5M - $10M</SelectItem>
                          <SelectItem value="10m-50m">$10M - $50M</SelectItem>
                          <SelectItem value="50m+">$50M+</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.annual_revenue}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="registration_number">Registration Number</Label>
                    {editMode ? (
                      <Input
                        id="registration_number"
                        value={profile.registration_number}
                        onChange={(e) => setProfile({ ...profile, registration_number: e.target.value })}
                        placeholder="Business registration number"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.registration_number}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tax_id">Tax ID</Label>
                    {editMode ? (
                      <Input
                        id="tax_id"
                        value={profile.tax_id}
                        onChange={(e) => setProfile({ ...profile, tax_id: e.target.value })}
                        placeholder="Tax identification number"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.tax_id}</div>
                    )}
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="vat_number">VAT Number</Label>
                    {editMode ? (
                      <Input
                        id="vat_number"
                        value={profile.vat_number}
                        onChange={(e) => setProfile({ ...profile, vat_number: e.target.value })}
                        placeholder="VAT registration number"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.vat_number}</div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="banking">
            <Card>
              <CardHeader>
                <CardTitle>Banking Information</CardTitle>
                <CardDescription>
                  Bank account details for transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="bank_name">Bank Name</Label>
                    {editMode ? (
                      <Input
                        id="bank_name"
                        value={profile.bank_name}
                        onChange={(e) => setProfile({ ...profile, bank_name: e.target.value })}
                        placeholder="Bank name"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.bank_name}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bank_account">Account Number</Label>
                    {editMode ? (
                      <Input
                        id="bank_account"
                        value={profile.bank_account}
                        onChange={(e) => setProfile({ ...profile, bank_account: e.target.value })}
                        placeholder="Account number"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.bank_account}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bank_swift">SWIFT/BIC Code</Label>
                    {editMode ? (
                      <Input
                        id="bank_swift"
                        value={profile.bank_swift}
                        onChange={(e) => setProfile({ ...profile, bank_swift: e.target.value })}
                        placeholder="SWIFT/BIC code"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.bank_swift}</div>
                    )}
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="bank_address">Bank Address</Label>
                    {editMode ? (
                      <Textarea
                        id="bank_address"
                        value={profile.bank_address}
                        onChange={(e) => setProfile({ ...profile, bank_address: e.target.value })}
                        placeholder="Bank address"
                        rows={3}
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md min-h-[80px]">{profile.bank_address}</div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export">
            <Card>
              <CardHeader>
                <CardTitle>Export & Trade</CardTitle>
                <CardDescription>
                  Export licenses and trade preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="export_license">Export License</Label>
                    {editMode ? (
                      <Input
                        id="export_license"
                        value={profile.export_license}
                        onChange={(e) => setProfile({ ...profile, export_license: e.target.value })}
                        placeholder="Export license number"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.export_license}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="customs_code">Customs Code</Label>
                    {editMode ? (
                      <Input
                        id="customs_code"
                        value={profile.customs_code}
                        onChange={(e) => setProfile({ ...profile, customs_code: e.target.value })}
                        placeholder="Customs registration code"
                      />
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.customs_code}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="preferred_incoterms">Preferred Incoterms</Label>
                    {editMode ? (
                      <Select value={profile.preferred_incoterms} onValueChange={(value) => setProfile({ ...profile, preferred_incoterms: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select Incoterms" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="EXW">EXW - Ex Works</SelectItem>
                          <SelectItem value="FCA">FCA - Free Carrier</SelectItem>
                          <SelectItem value="CPT">CPT - Carriage Paid To</SelectItem>
                          <SelectItem value="CIP">CIP - Carriage and Insurance Paid To</SelectItem>
                          <SelectItem value="DAP">DAP - Delivered at Place</SelectItem>
                          <SelectItem value="DPU">DPU - Delivered at Place Unloaded</SelectItem>
                          <SelectItem value="DDP">DDP - Delivered Duty Paid</SelectItem>
                          <SelectItem value="FAS">FAS - Free Alongside Ship</SelectItem>
                          <SelectItem value="FOB">FOB - Free on Board</SelectItem>
                          <SelectItem value="CFR">CFR - Cost and Freight</SelectItem>
                          <SelectItem value="CIF">CIF - Cost, Insurance and Freight</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.preferred_incoterms}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="preferred_payment_terms">Preferred Payment Terms</Label>
                    {editMode ? (
                      <Select value={profile.preferred_payment_terms} onValueChange={(value) => setProfile({ ...profile, preferred_payment_terms: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment terms" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="prepaid">Prepaid</SelectItem>
                          <SelectItem value="net-15">Net 15 days</SelectItem>
                          <SelectItem value="net-30">Net 30 days</SelectItem>
                          <SelectItem value="net-60">Net 60 days</SelectItem>
                          <SelectItem value="net-90">Net 90 days</SelectItem>
                          <SelectItem value="cod">Cash on Delivery</SelectItem>
                          <SelectItem value="lc">Letter of Credit</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="p-3 bg-muted rounded-md">{profile.preferred_payment_terms}</div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppShell>
  )
}
