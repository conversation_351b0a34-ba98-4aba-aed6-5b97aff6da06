"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Package, AlertTriangle, TrendingUp, Search, Filter } from "lucide-react"

export default function RawMaterialsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterCategory, setFilterCategory] = useState("all")

  // Mock data for raw materials
  const rawMaterials = [
    {
      id: "RM001",
      name: "Cotton Yarn 30s",
      category: "Yarn",
      supplier: "ABC Textiles",
      currentStock: 2500,
      unit: "kg",
      reorderLevel: 500,
      lastPurchasePrice: 4.50,
      currency: "USD",
      quality: "Combed",
      composition: "100% Cotton",
      status: "In Stock"
    },
    {
      id: "RM002",
      name: "Polyester Fabric",
      category: "Fabric",
      supplier: "XYZ Mills",
      currentStock: 150,
      unit: "meters",
      reorderLevel: 200,
      lastPurchasePrice: 3.20,
      currency: "USD",
      quality: "Premium",
      composition: "100% Polyester",
      status: "Low Stock"
    },
    {
      id: "RM003",
      name: "Reactive Dye - Red",
      category: "Dyes",
      supplier: "Color Chem Ltd",
      currentStock: 75,
      unit: "kg",
      reorderLevel: 50,
      lastPurchasePrice: 12.80,
      currency: "USD",
      quality: "High Fastness",
      composition: "Reactive Dye",
      status: "In Stock"
    }
  ]

  const materialCategories = [
    { name: "Yarn", count: 45, value: 125000 },
    { name: "Fabric", count: 32, value: 89000 },
    { name: "Dyes", count: 28, value: 34000 },
    { name: "Chemicals", count: 15, value: 22000 },
    { name: "Accessories", count: 67, value: 18000 }
  ]

  const recentPurchases = [
    { id: "PO001", supplier: "ABC Textiles", material: "Cotton Yarn 30s", quantity: 1000, amount: 4500, date: "2024-01-15" },
    { id: "PO002", supplier: "XYZ Mills", material: "Polyester Fabric", quantity: 500, amount: 1600, date: "2024-01-14" },
    { id: "PO003", supplier: "Color Chem Ltd", material: "Reactive Dye - Blue", quantity: 50, amount: 640, date: "2024-01-13" }
  ]

  const filteredMaterials = rawMaterials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.supplier.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterCategory === "all" || material.category.toLowerCase() === filterCategory.toLowerCase()
    return matchesSearch && matchesFilter
  })

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Raw Materials</h1>
          <p className="text-muted-foreground">Manage your textile raw materials, inventory, and supplier relationships</p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Material
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Materials</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">187</div>
            <p className="text-xs text-muted-foreground">+12 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$288,000</div>
            <p className="text-xs text-muted-foreground">+5.2% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">Requires attention</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Suppliers</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">Across 5 countries</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="inventory" className="space-y-4">
        <TabsList>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="purchases">Recent Purchases</TabsTrigger>
          <TabsTrigger value="specifications">Specifications</TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Material Inventory</CardTitle>
              <CardDescription>Track and manage your raw material stock levels</CardDescription>
              <div className="flex gap-4">
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search materials..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64"
                  />
                </div>
                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger className="w-40">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="yarn">Yarn</SelectItem>
                    <SelectItem value="fabric">Fabric</SelectItem>
                    <SelectItem value="dyes">Dyes</SelectItem>
                    <SelectItem value="chemicals">Chemicals</SelectItem>
                    <SelectItem value="accessories">Accessories</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Material ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Supplier</TableHead>
                    <TableHead>Current Stock</TableHead>
                    <TableHead>Reorder Level</TableHead>
                    <TableHead>Last Price</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMaterials.map((material) => (
                    <TableRow key={material.id}>
                      <TableCell className="font-medium">{material.id}</TableCell>
                      <TableCell>{material.name}</TableCell>
                      <TableCell>{material.category}</TableCell>
                      <TableCell>{material.supplier}</TableCell>
                      <TableCell>{material.currentStock} {material.unit}</TableCell>
                      <TableCell>{material.reorderLevel} {material.unit}</TableCell>
                      <TableCell>{material.currency} {material.lastPurchasePrice}</TableCell>
                      <TableCell>
                        <Badge variant={material.status === "Low Stock" ? "destructive" : "default"}>
                          {material.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Material Categories</CardTitle>
              <CardDescription>Overview of material categories and their values</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {materialCategories.map((category) => (
                  <Card key={category.name}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">{category.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{category.count}</div>
                      <p className="text-sm text-muted-foreground">items</p>
                      <div className="text-lg font-semibold text-green-600">${category.value.toLocaleString()}</div>
                      <p className="text-xs text-muted-foreground">total value</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="purchases" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Purchases</CardTitle>
              <CardDescription>Track recent material purchases and supplier performance</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>PO Number</TableHead>
                    <TableHead>Supplier</TableHead>
                    <TableHead>Material</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentPurchases.map((purchase) => (
                    <TableRow key={purchase.id}>
                      <TableCell className="font-medium">{purchase.id}</TableCell>
                      <TableCell>{purchase.supplier}</TableCell>
                      <TableCell>{purchase.material}</TableCell>
                      <TableCell>{purchase.quantity}</TableCell>
                      <TableCell>${purchase.amount}</TableCell>
                      <TableCell>{purchase.date}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="specifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Material Specifications</CardTitle>
              <CardDescription>Detailed specifications and quality parameters</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {rawMaterials.map((material) => (
                  <Card key={material.id}>
                    <CardHeader>
                      <CardTitle className="text-lg">{material.name}</CardTitle>
                      <CardDescription>{material.category} - {material.id}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4 md:grid-cols-3">
                        <div>
                          <Label className="text-sm font-medium">Composition</Label>
                          <p className="text-sm text-muted-foreground">{material.composition}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium">Quality Grade</Label>
                          <p className="text-sm text-muted-foreground">{material.quality}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium">Supplier</Label>
                          <p className="text-sm text-muted-foreground">{material.supplier}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}