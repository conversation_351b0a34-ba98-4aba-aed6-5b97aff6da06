"use client"

import React, { useState, useEffect } from "react"
import { useUser } from '@auth0/nextjs-auth0/client'
import { useRouter } from 'next/navigation'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Building, CreditCard, Globe, ArrowRight, ArrowLeft } from "lucide-react"
import { toast } from "sonner"

interface CompanyData {
  // Basic Information
  name: string
  legal_name: string
  email: string
  phone: string
  website: string
  
  // Address
  address_line1: string
  address_line2: string
  city: string
  state_province: string
  postal_code: string
  country: string
  
  // Business Details
  industry: string
  business_type: string
  employee_count: string
  annual_revenue: string
  registration_number: string
  tax_id: string
  vat_number: string
  
  // Banking Information
  bank_name: string
  bank_account: string
  bank_swift: string
  bank_address: string
  
  // Export Information
  export_license: string
  customs_code: string
  preferred_incoterms: string
  preferred_payment_terms: string
}

const STEPS = [
  { id: 'basic_info', title: 'Basic Information', icon: Building },
  { id: 'business_details', title: 'Business Details', icon: CreditCard },
  { id: 'banking', title: 'Banking Information', icon: CreditCard },
  { id: 'export_info', title: 'Export Information', icon: Globe },
]

export default function OnboardingPage() {
  const { user, isLoading } = useUser()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<CompanyData>({
    name: '',
    legal_name: '',
    email: user?.email || '',
    phone: '',
    website: '',
    address_line1: '',
    address_line2: '',
    city: '',
    state_province: '',
    postal_code: '',
    country: '',
    industry: '',
    business_type: '',
    employee_count: '',
    annual_revenue: '',
    registration_number: '',
    tax_id: '',
    vat_number: '',
    bank_name: '',
    bank_account: '',
    bank_swift: '',
    bank_address: '',
    export_license: '',
    customs_code: '',
    preferred_incoterms: 'FOB',
    preferred_payment_terms: '30 days',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/api/auth/login')
    }
  }, [user, isLoading, router])

  // Update email when user data is available
  useEffect(() => {
    if (user?.email && !formData.email) {
      setFormData(prev => ({ ...prev, email: user.email }))
    }
  }, [user, formData.email])

  const updateFormData = (field: keyof CompanyData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 0: // Basic Information
        return !!(formData.name && formData.email && formData.address_line1 && formData.city && formData.country)
      case 1: // Business Details
        return !!(formData.industry && formData.business_type && formData.employee_count)
      case 2: // Banking Information
        return !!(formData.bank_name)
      case 3: // Export Information
        return !!(formData.preferred_incoterms && formData.preferred_payment_terms)
      default:
        return true
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length - 1))
    } else {
      toast.error('Please fill in all required fields')
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0))
  }

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      toast.error('Please fill in all required fields')
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch('/api/companies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          onboarding_completed: 'true',
          onboarding_step: 'complete'
        }),
      })

      if (response.ok) {
        toast.success('Company profile created successfully!')
        router.push('/dashboard')
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to create company profile')
      }
    } catch (error) {
      console.error('Error creating company profile:', error)
      toast.error('Failed to create company profile')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const progress = ((currentStep + 1) / STEPS.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Welcome to Manufacturing ERP</h1>
          <p className="text-muted-foreground">Let's set up your company profile to get started</p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            {STEPS.map((step, index) => {
              const Icon = step.icon
              const isCompleted = index < currentStep
              const isCurrent = index === currentStep
              
              return (
                <div key={step.id} className="flex flex-col items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 ${
                    isCompleted ? 'bg-green-500 text-white' :
                    isCurrent ? 'bg-blue-500 text-white' :
                    'bg-gray-200 text-gray-500'
                  }`}>
                    {isCompleted ? <CheckCircle className="h-5 w-5" /> : <Icon className="h-5 w-5" />}
                  </div>
                  <span className={`text-xs text-center ${
                    isCurrent ? 'text-blue-600 font-medium' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </span>
                </div>
              )
            })}
          </div>
          <Progress value={progress} className="h-2" />
          <div className="text-center mt-2">
            <Badge variant="secondary">
              Step {currentStep + 1} of {STEPS.length}
            </Badge>
          </div>
        </div>

        {/* Form Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {React.createElement(STEPS[currentStep].icon, { className: "h-5 w-5" })}
              {STEPS[currentStep].title}
            </CardTitle>
            <CardDescription>
              {currentStep === 0 && "Enter your company's basic information"}
              {currentStep === 1 && "Tell us about your business"}
              {currentStep === 2 && "Add your banking information for transactions"}
              {currentStep === 3 && "Configure your export and trade preferences"}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Step Content will be added in the next part */}
            {currentStep === 0 && <BasicInformationStep formData={formData} updateFormData={updateFormData} />}
            {currentStep === 1 && <BusinessDetailsStep formData={formData} updateFormData={updateFormData} />}
            {currentStep === 2 && <BankingInformationStep formData={formData} updateFormData={updateFormData} />}
            {currentStep === 3 && <ExportInformationStep formData={formData} updateFormData={updateFormData} />}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>
          
          {currentStep === STEPS.length - 1 ? (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !validateStep(currentStep)}
              className="flex items-center gap-2"
            >
              {isSubmitting ? 'Creating...' : 'Complete Setup'}
              <CheckCircle className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={nextStep}
              disabled={!validateStep(currentStep)}
              className="flex items-center gap-2"
            >
              Next
              <ArrowRight className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

// Step Components
function BasicInformationStep({ formData, updateFormData }: {
  formData: CompanyData
  updateFormData: (field: keyof CompanyData, value: string) => void
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="name">Company Name *</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => updateFormData('name', e.target.value)}
          placeholder="Enter company name"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="legal_name">Legal Company Name</Label>
        <Input
          id="legal_name"
          value={formData.legal_name}
          onChange={(e) => updateFormData('legal_name', e.target.value)}
          placeholder="Full legal name"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">Email Address *</Label>
        <Input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => updateFormData('email', e.target.value)}
          placeholder="<EMAIL>"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone">Phone Number</Label>
        <Input
          id="phone"
          value={formData.phone}
          onChange={(e) => updateFormData('phone', e.target.value)}
          placeholder="+****************"
        />
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="website">Website</Label>
        <Input
          id="website"
          value={formData.website}
          onChange={(e) => updateFormData('website', e.target.value)}
          placeholder="https://www.company.com"
        />
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="address_line1">Address Line 1 *</Label>
        <Input
          id="address_line1"
          value={formData.address_line1}
          onChange={(e) => updateFormData('address_line1', e.target.value)}
          placeholder="Street address"
        />
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="address_line2">Address Line 2</Label>
        <Input
          id="address_line2"
          value={formData.address_line2}
          onChange={(e) => updateFormData('address_line2', e.target.value)}
          placeholder="Apartment, suite, etc."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="city">City *</Label>
        <Input
          id="city"
          value={formData.city}
          onChange={(e) => updateFormData('city', e.target.value)}
          placeholder="City"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="state_province">State/Province</Label>
        <Input
          id="state_province"
          value={formData.state_province}
          onChange={(e) => updateFormData('state_province', e.target.value)}
          placeholder="State or Province"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="postal_code">Postal Code</Label>
        <Input
          id="postal_code"
          value={formData.postal_code}
          onChange={(e) => updateFormData('postal_code', e.target.value)}
          placeholder="12345"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="country">Country *</Label>
        <Select value={formData.country} onValueChange={(value) => updateFormData('country', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select country" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="US">United States</SelectItem>
            <SelectItem value="CA">Canada</SelectItem>
            <SelectItem value="CN">China</SelectItem>
            <SelectItem value="IN">India</SelectItem>
            <SelectItem value="BD">Bangladesh</SelectItem>
            <SelectItem value="VN">Vietnam</SelectItem>
            <SelectItem value="TR">Turkey</SelectItem>
            <SelectItem value="IT">Italy</SelectItem>
            <SelectItem value="DE">Germany</SelectItem>
            <SelectItem value="UK">United Kingdom</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}

function BusinessDetailsStep({ formData, updateFormData }: {
  formData: CompanyData
  updateFormData: (field: keyof CompanyData, value: string) => void
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="industry">Industry *</Label>
        <Select value={formData.industry} onValueChange={(value) => updateFormData('industry', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select industry" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="textile">Textile Manufacturing</SelectItem>
            <SelectItem value="apparel">Apparel & Fashion</SelectItem>
            <SelectItem value="home-textile">Home Textiles</SelectItem>
            <SelectItem value="technical-textile">Technical Textiles</SelectItem>
            <SelectItem value="leather">Leather Goods</SelectItem>
            <SelectItem value="footwear">Footwear</SelectItem>
            <SelectItem value="other">Other Manufacturing</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="business_type">Business Type *</Label>
        <Select value={formData.business_type} onValueChange={(value) => updateFormData('business_type', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select business type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="corporation">Corporation</SelectItem>
            <SelectItem value="llc">Limited Liability Company (LLC)</SelectItem>
            <SelectItem value="partnership">Partnership</SelectItem>
            <SelectItem value="sole-proprietorship">Sole Proprietorship</SelectItem>
            <SelectItem value="cooperative">Cooperative</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="employee_count">Number of Employees *</Label>
        <Select value={formData.employee_count} onValueChange={(value) => updateFormData('employee_count', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select employee count" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1-10">1-10 employees</SelectItem>
            <SelectItem value="11-50">11-50 employees</SelectItem>
            <SelectItem value="51-200">51-200 employees</SelectItem>
            <SelectItem value="201-500">201-500 employees</SelectItem>
            <SelectItem value="501-1000">501-1000 employees</SelectItem>
            <SelectItem value="1000+">1000+ employees</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="annual_revenue">Annual Revenue</Label>
        <Select value={formData.annual_revenue} onValueChange={(value) => updateFormData('annual_revenue', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select revenue range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="under-1m">Under $1M</SelectItem>
            <SelectItem value="1m-5m">$1M - $5M</SelectItem>
            <SelectItem value="5m-10m">$5M - $10M</SelectItem>
            <SelectItem value="10m-50m">$10M - $50M</SelectItem>
            <SelectItem value="50m-100m">$50M - $100M</SelectItem>
            <SelectItem value="100m+">$100M+</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="registration_number">Business Registration Number</Label>
        <Input
          id="registration_number"
          value={formData.registration_number}
          onChange={(e) => updateFormData('registration_number', e.target.value)}
          placeholder="Registration number"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tax_id">Tax ID / EIN</Label>
        <Input
          id="tax_id"
          value={formData.tax_id}
          onChange={(e) => updateFormData('tax_id', e.target.value)}
          placeholder="Tax identification number"
        />
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="vat_number">VAT Number</Label>
        <Input
          id="vat_number"
          value={formData.vat_number}
          onChange={(e) => updateFormData('vat_number', e.target.value)}
          placeholder="VAT registration number"
        />
      </div>
    </div>
  )
}

function BankingInformationStep({ formData, updateFormData }: {
  formData: CompanyData
  updateFormData: (field: keyof CompanyData, value: string) => void
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="bank_name">Bank Name *</Label>
        <Input
          id="bank_name"
          value={formData.bank_name}
          onChange={(e) => updateFormData('bank_name', e.target.value)}
          placeholder="Name of your bank"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="bank_account">Bank Account Number</Label>
        <Input
          id="bank_account"
          value={formData.bank_account}
          onChange={(e) => updateFormData('bank_account', e.target.value)}
          placeholder="Account number"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="bank_swift">SWIFT/BIC Code</Label>
        <Input
          id="bank_swift"
          value={formData.bank_swift}
          onChange={(e) => updateFormData('bank_swift', e.target.value)}
          placeholder="SWIFT code"
        />
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="bank_address">Bank Address</Label>
        <Textarea
          id="bank_address"
          value={formData.bank_address}
          onChange={(e) => updateFormData('bank_address', e.target.value)}
          placeholder="Bank's full address"
          rows={3}
        />
      </div>
    </div>
  )
}

function ExportInformationStep({ formData, updateFormData }: {
  formData: CompanyData
  updateFormData: (field: keyof CompanyData, value: string) => void
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="export_license">Export License Number</Label>
        <Input
          id="export_license"
          value={formData.export_license}
          onChange={(e) => updateFormData('export_license', e.target.value)}
          placeholder="Export license number"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="customs_code">Customs Code</Label>
        <Input
          id="customs_code"
          value={formData.customs_code}
          onChange={(e) => updateFormData('customs_code', e.target.value)}
          placeholder="Customs registration code"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="preferred_incoterms">Preferred Incoterms *</Label>
        <Select value={formData.preferred_incoterms} onValueChange={(value) => updateFormData('preferred_incoterms', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select Incoterms" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="FOB">FOB (Free on Board)</SelectItem>
            <SelectItem value="CIF">CIF (Cost, Insurance & Freight)</SelectItem>
            <SelectItem value="EXW">EXW (Ex Works)</SelectItem>
            <SelectItem value="FCA">FCA (Free Carrier)</SelectItem>
            <SelectItem value="CPT">CPT (Carriage Paid To)</SelectItem>
            <SelectItem value="CIP">CIP (Carriage & Insurance Paid To)</SelectItem>
            <SelectItem value="DAP">DAP (Delivered at Place)</SelectItem>
            <SelectItem value="DPU">DPU (Delivered at Place Unloaded)</SelectItem>
            <SelectItem value="DDP">DDP (Delivered Duty Paid)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="preferred_payment_terms">Preferred Payment Terms *</Label>
        <Select value={formData.preferred_payment_terms} onValueChange={(value) => updateFormData('preferred_payment_terms', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select payment terms" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="30 days">Net 30 days</SelectItem>
            <SelectItem value="60 days">Net 60 days</SelectItem>
            <SelectItem value="90 days">Net 90 days</SelectItem>
            <SelectItem value="L/C at sight">L/C at sight</SelectItem>
            <SelectItem value="L/C 30 days">L/C 30 days</SelectItem>
            <SelectItem value="L/C 60 days">L/C 60 days</SelectItem>
            <SelectItem value="T/T advance">T/T in advance</SelectItem>
            <SelectItem value="T/T 30% advance">T/T 30% advance, 70% before shipment</SelectItem>
            <SelectItem value="T/T 50% advance">T/T 50% advance, 50% before shipment</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
