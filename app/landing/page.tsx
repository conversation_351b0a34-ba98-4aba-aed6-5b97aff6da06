"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { 
  Factory, 
  Package, 
  Ship, 
  Users, 
  CheckCircle, 
  TrendingUp, 
  Globe, 
  Shield, 
  Zap,
  ArrowRight,
  Star,
  Building,
  Truck,
  BarChart3
} from "lucide-react"

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 bg-gradient-to-br from-blue-600 to-green-600 rounded-lg flex items-center justify-center">
              <Factory className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
              Manufacturing ERP
            </span>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" asChild>
              <Link href="/api/auth/login">Sign In</Link>
            </Button>
            <Button asChild>
              <Link href="/api/auth/login?screen_hint=signup">Get Started</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <div className="max-w-4xl mx-auto">
          <Badge variant="secondary" className="mb-4">
            🚀 Trusted by 500+ Export Manufacturers
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
            Complete Export Manufacturing ERP Solution
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Streamline your textile manufacturing operations from production to export. 
            Manage customers, products, quality control, and international trade compliance in one platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild className="text-lg px-8">
              <Link href="/api/auth/login?screen_hint=signup">
                Start Free Trial <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild className="text-lg px-8">
              <Link href="#features">Learn More</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Everything You Need for Export Manufacturing</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            From raw materials to international shipping, manage your entire manufacturing workflow
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <FeatureCard
            icon={<Users className="h-8 w-8 text-blue-600" />}
            title="Customer & Supplier Management"
            description="Comprehensive CRM for managing international customers and suppliers with contact details, payment terms, and trade history."
          />
          <FeatureCard
            icon={<Package className="h-8 w-8 text-green-600" />}
            title="Product Catalog & Inventory"
            description="Detailed product management with SKUs, specifications, quality standards, and real-time inventory tracking."
          />
          <FeatureCard
            icon={<Factory className="h-8 w-8 text-purple-600" />}
            title="Production Management"
            description="Work order management, production scheduling, and real-time tracking from cutting to packaging."
          />
          <FeatureCard
            icon={<CheckCircle className="h-8 w-8 text-emerald-600" />}
            title="Quality Control"
            description="Integrated quality inspections, defect tracking, and compliance management for export standards."
          />
          <FeatureCard
            icon={<Ship className="h-8 w-8 text-cyan-600" />}
            title="Export Documentation"
            description="Automated export declarations, shipping documents, and international trade compliance management."
          />
          <FeatureCard
            icon={<BarChart3 className="h-8 w-8 text-orange-600" />}
            title="Analytics & Reporting"
            description="Real-time dashboards, production analytics, and comprehensive reporting for business insights."
          />
        </div>
      </section>

      {/* Benefits Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose Our Manufacturing ERP?</h2>
            <p className="text-xl text-muted-foreground">Built specifically for export-oriented textile manufacturers</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <BenefitCard
              icon={<Zap className="h-12 w-12 text-yellow-500" />}
              title="50% Faster Operations"
              description="Streamlined workflows reduce manual work and accelerate production cycles"
            />
            <BenefitCard
              icon={<Shield className="h-12 w-12 text-green-500" />}
              title="100% Compliance"
              description="Built-in export compliance ensures all international trade requirements are met"
            />
            <BenefitCard
              icon={<Globe className="h-12 w-12 text-blue-500" />}
              title="Global Ready"
              description="Multi-currency, multi-language support for international business operations"
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Manufacturing?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Join hundreds of manufacturers who have streamlined their operations with our ERP solution
          </p>
          <Button size="lg" asChild className="text-lg px-8">
            <Link href="/api/auth/login?screen_hint=signup">
              Start Your Free Trial Today <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <p className="text-sm text-muted-foreground mt-4">
            No credit card required • 30-day free trial • Setup in minutes
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-gray-50 py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="h-6 w-6 bg-gradient-to-br from-blue-600 to-green-600 rounded flex items-center justify-center">
              <Factory className="h-4 w-4 text-white" />
            </div>
            <span className="font-semibold">Manufacturing ERP</span>
          </div>
          <p className="text-sm text-muted-foreground">
            © 2024 Manufacturing ERP. Built for export manufacturers worldwide.
          </p>
        </div>
      </footer>
    </div>
  )
}

function FeatureCard({ icon, title, description }: { 
  icon: React.ReactNode
  title: string
  description: string 
}) {
  return (
    <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
      <CardHeader>
        <div className="mb-2">{icon}</div>
        <CardTitle className="text-xl">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <CardDescription className="text-base">{description}</CardDescription>
      </CardContent>
    </Card>
  )
}

function BenefitCard({ icon, title, description }: { 
  icon: React.ReactNode
  title: string
  description: string 
}) {
  return (
    <div className="text-center">
      <div className="flex justify-center mb-4">{icon}</div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </div>
  )
}
