"use client"

import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"
import Link from "next/link"

export default function ExportPage() {
  return (
    <AppShell>
      <div className="grid gap-6">
        <DeclarationsCard />
      </div>
    </AppShell>
  )
}

function DeclarationsCard() {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [rows, setRows] = useState<any[]>([])
  const [number, setNumber] = useState("")
  const [items, setItems] = useState<Array<{ product_id?: string; qty: number; hs_code?: string }>>([])

  async function load() {
    const [p, r] = await Promise.all([safeJson("/api/products", []), safeJson("/api/export/declarations", [])])
    setProducts(p)
    setRows(r)
  }
  useEffect(() => {
    load()
  }, [])

  function addItem() {
    setItems([...items, { product_id: products[0]?.id, qty: 1, hs_code: products[0]?.hs_code }])
  }

  async function create() {
    if (!number || items.length === 0) return
    const res = await fetch("/api/export/declarations", { method: "POST", body: JSON.stringify({ number, items }) })
    if (!res.ok) {
      alert(await res.text())
      return
    }
    setNumber("")
    setItems([])
    await load()
  }

  async function submit(id: string) {
    await fetch(`/api/export/declarations/${id}`, { method: "PATCH", body: JSON.stringify({ status: "submitted" }) })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/export/declarations/${id}`)
    await fetch(`/api/export/declarations/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("export.title")}</CardTitle>
        <CardDescription>{t("export.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-4">
          <div className="grid gap-1">
            <Label>{t("field.declarationNo")}</Label>
            <Input value={number} onChange={(e) => setNumber(e.target.value)} />
          </div>
          <div className="flex items-end">
            <Button onClick={addItem} variant="secondary">
              {t("action.addItem")}
            </Button>
          </div>
        </div>

        <div className="border rounded-md p-3 space-y-2">
          {items.length === 0 && <div className="text-sm text-muted-foreground">{t("table.noData")}</div>}
          {items.map((it, idx) => (
            <div key={idx} className="grid gap-2 md:grid-cols-4 items-end">
              <div>
                <Label>{t("field.product")}</Label>
                <Select
                  value={it.product_id || ""}
                  onValueChange={(v) => {
                    const p = products.find((pr: any) => pr.id === v)
                    const copy = [...items]
                    copy[idx] = { ...copy[idx], product_id: v, hs_code: p?.hs_code }
                    setItems(copy)
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((p) => (
                      <SelectItem key={p.id} value={p.id}>
                        {p.sku} - {p.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{t("field.qty")}</Label>
                <Input
                  type="number"
                  value={it.qty}
                  onChange={(e) => {
                    const copy = [...items]
                    copy[idx] = { ...copy[idx], qty: Number(e.target.value) }
                    setItems(copy)
                  }}
                />
              </div>
              <div>
                <Label>{t("field.hsCode")}</Label>
                <Input
                  value={it.hs_code || ""}
                  onChange={(e) => {
                    const copy = [...items]
                    copy[idx] = { ...copy[idx], hs_code: e.target.value }
                    setItems(copy)
                  }}
                />
              </div>
              <div className="flex items-center">
                <Button variant="ghost" size="sm" onClick={() => setItems(items.filter((_, i) => i !== idx))}>
                  {t("action.remove")}
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-end">
          <Button onClick={create}>{t("action.createDeclaration")}</Button>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("field.declarationNo")}</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Items</TableHead>
              <TableHead>{t("table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((r) => (
              <TableRow key={r.id}>
                <TableCell>
                  <Link href={`/export/${r.id}`} className="hover:underline">
                    {r.number}
                  </Link>
                </TableCell>
                <TableCell>{r.status}</TableCell>
                <TableCell>{r.items.length}</TableCell>
                <TableCell className="space-x-2">
                  <Button size="sm" variant="secondary" onClick={() => submit(r.id)} disabled={r.status !== "draft"}>
                    {t("action.submit")}
                  </Button>
                  <Button size="sm" variant="ghost" onClick={() => remove(r.id)}>
                    {t("action.delete")}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {rows.length === 0 && (
              <TableRow>
                <TableCell colSpan={4} className="text-muted-foreground">
                  {t("export.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
