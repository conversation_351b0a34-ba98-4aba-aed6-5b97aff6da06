"use client"

import { useState, useRef } from "react"
import type { PutB<PERSON>bResult } from "@vercel/blob"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import type { declarations, documents } from "@/lib/schema-postgres"
import { File, Trash2, UploadCloud } from "lucide-react"
import Link from "next/link"

type DeclarationWithDocs = typeof declarations.$inferSelect & {
  documents: Array<typeof documents.$inferSelect>
}

export function DocumentManager({ declaration }: { declaration: DeclarationWithDocs }) {
  const router = useRouter()
  const inputFileRef = useRef<HTMLInputElement>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [docToDelete, setDocToDelete] = useState<(typeof documents.$inferSelect) | null>(null)

  const handleUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    event.preventDefault()
    if (!inputFileRef.current?.files) {
      throw new Error("No file selected")
    }

    const file = inputFileRef.current.files[0]
    setIsUploading(true)

    try {
      const response = await fetch(`/api/export/declarations/${declaration.id}/documents`, {
        method: "POST",
        headers: { "x-vercel-filename": file.name },
        body: file,
      })

      const newBlob = (await response.json()) as PutBlobResult

      if (response.ok) {
        toast.success("File uploaded successfully.")
        router.refresh()
      } else {
        throw new Error("Upload failed.")
      }
    } catch (error) {
      toast.error("Failed to upload file.", {
        description: error instanceof Error ? error.message : "An unexpected error occurred.",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleDelete = async () => {
    if (!docToDelete) return

    try {
      const response = await fetch(`/api/export/declarations/${declaration.id}/documents`, {
        method: "DELETE",
        body: JSON.stringify({ documentId: docToDelete.id, url: docToDelete.url }),
      })

      if (response.ok) {
        toast.success("Document deleted successfully.")
        setDocToDelete(null)
        router.refresh()
      } else {
        throw new Error("Failed to delete document.")
      }
    } catch (error) {
      toast.error("Failed to delete document.", {
        description: error instanceof Error ? error.message : "An unexpected error occurred.",
      })
    }
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Documents</CardTitle>
          <CardDescription>Manage documents related to this export declaration.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border-2 border-dashed border-muted-foreground/50 rounded-lg p-6 flex flex-col items-center text-center">
            <UploadCloud className="h-12 w-12 text-muted-foreground" />
            <p className="mt-4 text-sm text-muted-foreground">
              Drag and drop files here, or click to select a file.
            </p>
            <Input
              ref={inputFileRef}
              type="file"
              onChange={handleUpload}
              className="hidden"
              id="file-upload"
              disabled={isUploading}
            />
            <Button asChild variant="outline" className="mt-4" disabled={isUploading}>
              <label htmlFor="file-upload">{isUploading ? "Uploading..." : "Choose File"}</label>
            </Button>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold">Uploaded Documents</h4>
            {declaration.documents.length === 0 ? (
              <p className="text-sm text-muted-foreground">No documents uploaded yet.</p>
            ) : (
              <ul className="divide-y divide-muted-foreground/20">
                {declaration.documents.map((doc) => (
                  <li key={doc.id} className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-3">
                      <File className="h-5 w-5 text-muted-foreground" />
                      <Link href={doc.url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                        {doc.filename}
                      </Link>
                    </div>
                    <Button variant="ghost" size="icon" onClick={() => setDocToDelete(doc)}>
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={!!docToDelete} onOpenChange={(open) => !open && setDocToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the document "{docToDelete?.filename}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
