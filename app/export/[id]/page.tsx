import { AppShell } from "@/components/app-shell"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { db } from "@/lib/db"
import { declarations } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { notFound, redirect } from "next/navigation"
import { DocumentManager } from "./document-manager"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { getTenantContext } from "@/lib/tenant-utils"

export default async function DeclarationDetailsPage({ params }: { params: { id: string } }) {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch declaration for the current company
  const declaration = await db.query.declarations.findFirst({
    where: and(
      eq(declarations.id, params.id),
      eq(declarations.company_id, context.companyId)
    ),
    with: {
      items: {
        with: {
          product: true,
        },
      },
      documents: true,
    },
  })

  if (!declaration) {
    notFound()
  }

  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <Button asChild variant="ghost">
            <Link href="/export">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Declarations
            </Link>
          </Button>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Declaration: {declaration.number}</CardTitle>
            <CardDescription>Status: {declaration.status}</CardDescription>
          </CardHeader>
          <CardContent>
            <h3 className="font-semibold mb-2">Items</h3>
            <ul className="list-disc pl-5">
              {declaration.items.map((item) => (
                <li key={item.id}>
                  {item.product.name} - {item.qty} {item.product.unit}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <DocumentManager declaration={declaration} />
      </div>
    </AppShell>
  )
}
