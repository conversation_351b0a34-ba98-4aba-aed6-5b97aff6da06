"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Palette, Layers, Star, Search, Filter, Eye, Download, Copy } from "lucide-react"

export default function DesignCatalogPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterSeason, setFilterSeason] = useState("all")
  const [filterCategory, setFilterCategory] = useState("all")

  // Mock data for design collections
  const designCollections = [
    {
      id: "DC001",
      name: "Spring Bloom 2024",
      season: "Spring 2024",
      category: "Casual Wear",
      designer: "<PERSON> <PERSON>",
      status: "Active",
      designs: 24,
      colors: 8,
      fabrics: 6,
      createdDate: "2024-01-10",
      description: "Fresh floral patterns with sustainable fabrics"
    },
    {
      id: "DC002",
      name: "Urban Professional",
      season: "All Season",
      category: "Formal Wear",
      designer: "Michael Torres",
      status: "In Development",
      designs: 18,
      colors: 5,
      fabrics: 4,
      createdDate: "2024-01-08",
      description: "Modern professional attire for urban lifestyle"
    },
    {
      id: "DC003",
      name: "Summer Breeze",
      season: "Summer 2024",
      category: "Resort Wear",
      designer: "Emma Rodriguez",
      status: "Completed",
      designs: 32,
      colors: 12,
      fabrics: 8,
      createdDate: "2023-12-15",
      description: "Lightweight fabrics with tropical inspirations"
    }
  ]

  // Mock data for individual designs
  const designs = [
    {
      id: "D001",
      name: "Floral Midi Dress",
      collection: "Spring Bloom 2024",
      category: "Dresses",
      fabric: "Organic Cotton Voile",
      colors: ["Rose Pink", "Lavender", "Mint Green"],
      sizes: ["XS", "S", "M", "L", "XL"],
      status: "Production Ready",
      designDate: "2024-01-12",
      estimatedCost: 45.50,
      targetPrice: 89.99
    },
    {
      id: "D002",
      name: "Linen Blazer",
      collection: "Urban Professional",
      category: "Outerwear",
      fabric: "Premium Linen Blend",
      colors: ["Navy", "Charcoal", "Beige"],
      sizes: ["S", "M", "L", "XL", "XXL"],
      status: "Design Review",
      designDate: "2024-01-10",
      estimatedCost: 78.20,
      targetPrice: 149.99
    },
    {
      id: "D003",
      name: "Tropical Print Shirt",
      collection: "Summer Breeze",
      category: "Tops",
      fabric: "Bamboo Rayon",
      colors: ["Ocean Blue", "Sunset Orange", "Palm Green"],
      sizes: ["XS", "S", "M", "L", "XL"],
      status: "Approved",
      designDate: "2023-12-20",
      estimatedCost: 32.75,
      targetPrice: 65.99
    }
  ]

  // Mock data for color palettes
  const colorPalettes = [
    {
      id: "CP001",
      name: "Spring Pastels",
      season: "Spring 2024",
      colors: [
        { name: "Rose Pink", hex: "#F8BBD9" },
        { name: "Lavender", hex: "#E4C1F9" },
        { name: "Mint Green", hex: "#A8E6CF" },
        { name: "Peach", hex: "#FFD3A5" },
        { name: "Sky Blue", hex: "#A8DADC" }
      ],
      usage: "Active"
    },
    {
      id: "CP002",
      name: "Professional Neutrals",
      season: "All Season",
      colors: [
        { name: "Navy", hex: "#2C3E50" },
        { name: "Charcoal", hex: "#34495E" },
        { name: "Beige", hex: "#F5F5DC" },
        { name: "Cream", hex: "#FFFDD0" },
        { name: "Stone Gray", hex: "#918E85" }
      ],
      usage: "Active"
    }
  ]

  const filteredCollections = designCollections.filter(collection => {
    const matchesSearch = collection.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         collection.designer.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSeason = filterSeason === "all" || collection.season.toLowerCase().includes(filterSeason.toLowerCase())
    const matchesCategory = filterCategory === "all" || collection.category.toLowerCase() === filterCategory.toLowerCase()
    return matchesSearch && matchesSeason && matchesCategory
  })

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active": return "default"
      case "completed": return "secondary"
      case "in development": return "outline"
      case "production ready": return "default"
      case "approved": return "default"
      case "design review": return "secondary"
      default: return "secondary"
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Design Catalog</h1>
          <p className="text-muted-foreground">Manage your design collections, patterns, and color palettes</p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          New Collection
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Collections</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">+2 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Designs</CardTitle>
            <Palette className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-xs text-muted-foreground">Across all collections</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Production Ready</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">42</div>
            <p className="text-xs text-muted-foreground">Ready for manufacturing</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Color Palettes</CardTitle>
            <Palette className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">Seasonal & evergreen</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="collections" className="space-y-4">
        <TabsList>
          <TabsTrigger value="collections">Collections</TabsTrigger>
          <TabsTrigger value="designs">Individual Designs</TabsTrigger>
          <TabsTrigger value="colors">Color Palettes</TabsTrigger>
          <TabsTrigger value="trends">Trend Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="collections" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Design Collections</CardTitle>
              <CardDescription>Manage your seasonal and thematic design collections</CardDescription>
              <div className="flex gap-4">
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search collections..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64"
                  />
                </div>
                <Select value={filterSeason} onValueChange={setFilterSeason}>
                  <SelectTrigger className="w-40">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Season" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Seasons</SelectItem>
                    <SelectItem value="spring">Spring 2024</SelectItem>
                    <SelectItem value="summer">Summer 2024</SelectItem>
                    <SelectItem value="fall">Fall 2024</SelectItem>
                    <SelectItem value="winter">Winter 2024</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger className="w-40">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="casual wear">Casual Wear</SelectItem>
                    <SelectItem value="formal wear">Formal Wear</SelectItem>
                    <SelectItem value="resort wear">Resort Wear</SelectItem>
                    <SelectItem value="activewear">Activewear</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredCollections.map((collection) => (
                  <Card key={collection.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{collection.name}</CardTitle>
                          <CardDescription>{collection.season} • {collection.category}</CardDescription>
                        </div>
                        <Badge variant={getStatusColor(collection.status)}>
                          {collection.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Designer:</span>
                          <span>{collection.designer}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Designs:</span>
                          <span>{collection.designs}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Colors:</span>
                          <span>{collection.colors}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Fabrics:</span>
                          <span>{collection.fabrics}</span>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">{collection.description}</p>
                        <div className="flex gap-2 mt-4">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="designs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Individual Designs</CardTitle>
              <CardDescription>Browse and manage individual design pieces</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Design ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Collection</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Fabric</TableHead>
                    <TableHead>Colors</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Cost/Price</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {designs.map((design) => (
                    <TableRow key={design.id}>
                      <TableCell className="font-medium">{design.id}</TableCell>
                      <TableCell>{design.name}</TableCell>
                      <TableCell>{design.collection}</TableCell>
                      <TableCell>{design.category}</TableCell>
                      <TableCell>{design.fabric}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          {design.colors.slice(0, 3).map((color, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {color}
                            </Badge>
                          ))}
                          {design.colors.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{design.colors.length - 3}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(design.status)}>
                          {design.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>${design.estimatedCost}</div>
                          <div className="text-muted-foreground">${design.targetPrice}</div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="colors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Color Palettes</CardTitle>
              <CardDescription>Manage seasonal and collection-specific color palettes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {colorPalettes.map((palette) => (
                  <Card key={palette.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{palette.name}</CardTitle>
                          <CardDescription>{palette.season}</CardDescription>
                        </div>
                        <Badge variant="default">{palette.usage}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {palette.colors.map((color, index) => (
                          <div key={index} className="flex items-center gap-3">
                            <div 
                              className="w-8 h-8 rounded-full border-2 border-gray-200"
                              style={{ backgroundColor: color.hex }}
                            ></div>
                            <div className="flex-1">
                              <div className="font-medium text-sm">{color.name}</div>
                              <div className="text-xs text-muted-foreground">{color.hex}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="flex gap-2 mt-4">
                        <Button size="sm" variant="outline">
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Palette
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Popular Categories</CardTitle>
                <CardDescription>Most requested design categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Casual Wear</span>
                    <span className="text-sm font-medium">45%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Formal Wear</span>
                    <span className="text-sm font-medium">28%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Resort Wear</span>
                    <span className="text-sm font-medium">18%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Activewear</span>
                    <span className="text-sm font-medium">9%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Seasonal Performance</CardTitle>
                <CardDescription>Design approval rates by season</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Spring 2024</span>
                    <span className="text-sm font-medium text-green-600">92%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Summer 2024</span>
                    <span className="text-sm font-medium text-green-600">88%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Fall 2023</span>
                    <span className="text-sm font-medium text-yellow-600">76%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Winter 2023</span>
                    <span className="text-sm font-medium text-yellow-600">82%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}