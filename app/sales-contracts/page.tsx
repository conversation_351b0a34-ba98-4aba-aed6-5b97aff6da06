import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { salesContracts } from "@/lib/schema-postgres";
import { desc, eq } from "drizzle-orm";
import { SalesContractsClientPage } from "./sales-contracts-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect } from "next/navigation";

export default async function SalesContractsPage() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch data for the current company
  const allSalesContracts = await db.query.salesContracts.findMany({
    where: eq(salesContracts.company_id, context.companyId),
    orderBy: [desc(salesContracts.created_at)],
    with: {
      customer: true,
      items: {
        with: {
          product: true,
        },
      },
    },
  });

  return (
    <AppShell>
      <SalesContractsClientPage
        initialContracts={allSalesContracts}
      />
    </AppShell>
  );
}
