import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { customers, products, contractTemplates, salesContracts } from "@/lib/schema-postgres";
import { eq, and } from "drizzle-orm";
import { EditSalesContractPage } from "./edit-sales-contract-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect, notFound } from "next/navigation";

export default async function EditSalesContractPageServer({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  const { id } = await params;

  // 🛡️ SECURE: Get the contract with tenant isolation
  const contract = await db.query.salesContracts.findFirst({
    where: and(
      eq(salesContracts.id, id),
      eq(salesContracts.company_id, context.companyId)
    ),
    with: {
      customer: true,
      items: {
        with: {
          product: true,
        },
      },
    },
  });

  if (!contract) {
    notFound();
  }

  // 🛡️ SECURE: Only fetch data for the current company
  const allCustomers = await db.query.customers.findMany({
    where: eq(customers.company_id, context.companyId),
    orderBy: (customers, { asc }) => [asc(customers.name)],
  });

  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
    orderBy: (products, { asc }) => [asc(products.name)],
  });

  const allTemplates = await db.query.contractTemplates.findMany({
    where: eq(contractTemplates.company_id, context.companyId),
    orderBy: (contractTemplates, { asc }) => [asc(contractTemplates.name)],
  });

  return (
    <AppShell>
      <EditSalesContractPage
        contract={contract}
        customers={allCustomers}
        products={allProducts}
        templates={allTemplates}
      />
    </AppShell>
  );
}
