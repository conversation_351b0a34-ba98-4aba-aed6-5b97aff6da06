"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CustomerSelect } from "@/components/forms/customer-select"
import { ProductSelect } from "@/components/forms/product-select"
import { TemplateSelect } from "@/components/forms/template-select"

import { zodResolver } from "@hookform/resolvers/zod"
import { ArrowLeft, Building, CreditCard, FileText, Package, Plus, Save, Trash2, X } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { useFieldA<PERSON>y, useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

// Types
interface Customer {
  id: string
  name: string
  contact_email?: string
  address?: string
}

interface Product {
  id: string
  name: string
  sku: string
  price?: string
  unit: string
  description?: string
}

interface ContractTemplate {
  id: string
  name: string
  type: string
  description?: string
}

// Form validation schema
const formSchema = z.object({
  number: z.string().min(1, "Contract number is required"),
  customer_id: z.string().min(1, "Customer is required"),
  template_id: z.string().optional(),
  currency: z.string().min(1, "Currency is required"),
  items: z.array(z.object({
    product_id: z.string().min(1, "Product is required"),
    qty: z.coerce.number().positive("Quantity must be positive"),
    price: z.coerce.number().positive("Price must be positive"),
  })).min(1, "At least one item is required"),
})

interface AddSalesContractPageProps {
  customers: Customer[]
  products: Product[]
  templates: ContractTemplate[]
}

export function AddSalesContractPage({ customers, products, templates }: AddSalesContractPageProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [customersList, setCustomersList] = useState<Customer[]>(customers)
  const [productsList, setProductsList] = useState<Product[]>(products)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      number: "",
      customer_id: "",
      template_id: "none",
      currency: "USD",
      items: [{ product_id: "", qty: 1, price: 0 }],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)
    try {
      // Convert "none" template selection to undefined
      const submitData = {
        ...values,
        template_id: values.template_id === "none" ? undefined : values.template_id
      }

      const response = await fetch("/api/contracts/sales", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(submitData),
      })

      if (response.ok) {
        toast.success("Sales contract created successfully!")
        router.push("/sales-contracts")
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || "Failed to create sales contract")
      }
    } catch (error) {
      console.error("Error creating contract:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const addItem = () => {
    append({ product_id: "", qty: 1, price: 0 })
  }

  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  const handleCustomerAdded = (newCustomer: Customer) => {
    setCustomersList(prev => [...prev, newCustomer])
  }

  const handleProductAdded = (newProduct: Product) => {
    setProductsList(prev => [...prev, newProduct])
  }

  // Auto-populate price when product is selected
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name && name.includes('.product_id')) {
        const match = name.match(/items\.(\d+)\.product_id/)
        if (match) {
          const index = parseInt(match[1])
          const productId = value.items?.[index]?.product_id
          if (productId) {
            const product = productsList.find(p => p.id === productId)
            if (product && product.price) {
              form.setValue(`items.${index}.price`, product.price)
            }
          }
        }
      }
    })
    return () => subscription.unsubscribe()
  }, [form, productsList])

  // Calculate total
  const watchedItems = form.watch("items")
  const total = watchedItems.reduce((sum, item) => {
    return sum + (item.qty * item.price)
  }, 0)

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 mb-6">
        <Link
          href="/sales-contracts"
          className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          Sales Contracts
        </Link>
        <span className="text-muted-foreground">/</span>
        <span className="font-medium">Add New Contract</span>
      </div>

      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 bg-blue-100 rounded-lg">
            <FileText className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create Sales Contract</h1>
            <p className="text-muted-foreground">Enter the details for your new sales contract</p>
          </div>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Contract Information Section */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Building className="h-5 w-5 text-blue-600" />
                <CardTitle>Contract Information</CardTitle>
              </div>
              <CardDescription>
                Basic contract details and customer information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contract Number *</FormLabel>
                      <FormControl>
                        <Input placeholder="SC-2025-001" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="USD">USD - US Dollar</SelectItem>
                          <SelectItem value="EUR">EUR - Euro</SelectItem>
                          <SelectItem value="GBP">GBP - British Pound</SelectItem>
                          <SelectItem value="CNY">CNY - Chinese Yuan</SelectItem>
                          <SelectItem value="JPY">JPY - Japanese Yen</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="customer_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer *</FormLabel>
                      <FormControl>
                        <CustomerSelect
                          customers={customersList}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Search and select customer..."
                          onCustomerAdded={handleCustomerAdded}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="template_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contract Template (Optional)</FormLabel>
                      <FormControl>
                        <TemplateSelect
                          templates={templates}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Search and select template..."
                          templateType="sales"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Products Section */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-green-600" />
                  <div>
                    <CardTitle>Contract Items</CardTitle>
                    <CardDescription>Add products and quantities for this contract</CardDescription>
                  </div>
                </div>
                <Button type="button" onClick={addItem} variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="p-4 border rounded-lg bg-gray-50/50">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-sm">Item #{index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeItem(index)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name={`items.${index}.product_id`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Product *</FormLabel>
                            <FormControl>
                              <ProductSelect
                                products={productsList}
                                value={field.value}
                                onValueChange={field.onChange}
                                placeholder="Search and select product..."
                                onProductAdded={handleProductAdded}
                                showPrice={true}
                                showAddNew={true}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.qty`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Quantity *</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                step="1"
                                placeholder="1"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.price`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Unit Price *</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Line Total */}
                    <div className="mt-3 pt-3 border-t bg-gray-50 -mx-4 px-4 py-2 rounded-b-lg">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-muted-foreground">Line Total:</span>
                        <span className="font-semibold text-green-600 text-base">
                          {form.watch("currency")} {(watchedItems[index]?.qty * watchedItems[index]?.price || 0).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Add Another Item Button */}
              <div className="mt-6 pt-4 border-t border-dashed">
                <Button
                  type="button"
                  onClick={addItem}
                  variant="outline"
                  className="w-full border-dashed border-2 hover:border-solid hover:bg-green-50 hover:border-green-300 transition-all"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Another Item
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Contract Summary */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-purple-600" />
                <CardTitle>Contract Summary</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Total Items:</span>
                  <Badge variant="secondary">{fields.length}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Currency:</span>
                  <span className="font-medium">{form.watch("currency")}</span>
                </div>
                <div className="border-t" />
                <div className="flex justify-between items-center text-lg font-semibold">
                  <span>Total Contract Value:</span>
                  <span className="text-green-600">
                    {form.watch("currency")} {total.toFixed(2)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-between pt-6 border-t">
            <Link href="/sales-contracts">
              <Button type="button" variant="outline">
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </Link>

            <Button type="submit" disabled={isSubmitting} className="min-w-[140px]">
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Contract
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
