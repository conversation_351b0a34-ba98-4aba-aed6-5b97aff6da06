import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { customers, products, contractTemplates } from "@/lib/schema-postgres";
import { eq } from "drizzle-orm";
import { AddSalesContractPage } from "./add-sales-contract-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect } from "next/navigation";

export default async function AddSalesContractPageServer() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch data for the current company
  const allCustomers = await db.query.customers.findMany({
    where: eq(customers.company_id, context.companyId),
    orderBy: (customers, { asc }) => [asc(customers.name)],
  });

  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
    orderBy: (products, { asc }) => [asc(products.name)],
  });

  const allTemplates = await db.query.contractTemplates.findMany({
    where: eq(contractTemplates.company_id, context.companyId),
    orderBy: (contractTemplates, { asc }) => [asc(contractTemplates.name)],
  });

  return (
    <AppShell>
      <AddSalesContractPage
        customers={allCustomers}
        products={allProducts}
        templates={allTemplates}
      />
    </AppShell>
  );
}
