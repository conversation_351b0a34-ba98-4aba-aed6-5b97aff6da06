import { notFound } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { eq, and } from "drizzle-orm"
import { salesContracts } from "@/lib/schema-postgres"
import { SalesContractViewClient } from "./sales-contract-view-client"

interface SalesContractViewPageProps {
  params: Promise<{ id: string }>
}

export default async function SalesContractViewPage({ params }: SalesContractViewPageProps) {
  const { id } = await params
  const context = await getTenantContext()
  
  if (!context) {
    notFound()
  }

  // Fetch the sales contract with related data
  const contract = await db.query.salesContracts.findFirst({
    where: and(
      eq(salesContracts.id, id),
      eq(salesContracts.company_id, context.companyId)
    ),
    with: {
      customer: true,
      template: true,
      items: {
        with: {
          product: true
        }
      }
    }
  })

  if (!contract) {
    notFound()
  }

  return (
    <SalesContractViewClient 
      contract={contract}
      companyId={context.companyId}
    />
  )
}
