import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { db } from "@/lib/db"
import { customers } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { notFound, redirect } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, File, Mail, Phone, Users } from "lucide-react"
import { getTenantContext } from "@/lib/tenant-utils"

export default async function CustomerDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params

  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch customer for the current company
  const customer = await db.query.customers.findFirst({
    where: and(
      eq(customers.id, id),
      eq(customers.company_id, context.companyId)
    ),
    with: {
      salesContracts: {
        with: {
          items: true,
        },
      },
      arInvoices: true,
    },
  })

  if (!customer) {
    notFound()
  }

  // Remove the problematic line that references non-existent declarations
  // const declarationsWithDocs = customer.salesContracts.flatMap((sc) => sc.declarations).filter((d) => d.documents.length > 0)

  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <Button asChild variant="ghost">
            <Link href="/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Link>
          </Button>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>{customer.name}</CardTitle>
            <CardDescription>
              Status: <span className="capitalize">{customer.status}</span>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-3">
                <h3 className="font-semibold">Contact Information</h3>
                <div className="space-y-2">
                  {customer.contact_name && (
                    <div className="flex items-center gap-2 text-sm">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      {customer.contact_name}
                    </div>
                  )}
                  {customer.contact_email && (
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <a href={`mailto:${customer.contact_email}`} className="hover:underline">
                        {customer.contact_email}
                      </a>
                    </div>
                  )}
                  {customer.contact_phone && (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      {customer.contact_phone}
                    </div>
                  )}
                </div>
              </div>
              <div className="space-y-3">
                <h3 className="font-semibold">Details</h3>
                <div className="space-y-2 text-sm">
                  <div>
                    <strong>Address:</strong> {customer.address || "N/A"}
                  </div>
                  <div>
                    <strong>Incoterm:</strong> {customer.incoterm || "N/A"}
                  </div>
                  <div>
                    <strong>Payment Term:</strong> {customer.payment_term || "N/A"}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Sales Contracts</CardTitle>
            <CardDescription>Sales contracts for this customer.</CardDescription>
          </CardHeader>
          <CardContent>
            {customer.salesContracts.length === 0 ? (
              <p className="text-sm text-muted-foreground">No sales contracts found for this customer.</p>
            ) : (
              <div className="space-y-4">
                {customer.salesContracts.map((contract) => (
                  <div key={contract.id} className="border rounded-lg p-4">
                    <h4 className="font-semibold">Contract: {contract.number}</h4>
                    <p className="text-sm text-muted-foreground">Date: {contract.date}</p>
                    <p className="text-sm text-muted-foreground">Status: {contract.status}</p>
                    {contract.items && contract.items.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium">Items: {contract.items.length}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
