"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { FilePen, Plus, Trash2, Users, TrendingUp, DollarSign, Calendar, Search, Phone, Mail, MapPin, Star } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { AddCustomerForm } from "./add-customer-form"
import { EditCustomerForm } from "./edit-customer-form"
import { Toaster } from "sonner"
import { useRouter } from "next/navigation"
import { safeJson } from "@/lib/safe-fetch"
import Link from "next/link"

// This type should match the data returned from the API /api/customers
export type CustomerFromApi = {
  id: string
  name: string
  contact_name: string | null
  contact_phone: string | null
  contact_email: string | null
  address: string | null
  incoterm: string | null
  payment_term: string | null
  status: string
  created_at: string
}

// The main page is now a server component that fetches data
export default async function CRMPage() {
  // Use relative URL for server-side fetching to avoid port issues
  const customers = await safeJson("/api/customers", [] as CustomerFromApi[])

  return <CrmClientPage initialCustomers={customers} />
}

// All client-side logic and UI is in this component
function CrmClientPage({ initialCustomers }: { initialCustomers: CustomerFromApi[] }) {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [isAddCustomerOpen, setAddCustomerOpen] = useState(false)
  const [customerToDelete, setCustomerToDelete] = useState<CustomerFromApi | null>(null)
  const [customerToEdit, setCustomerToEdit] = useState<CustomerFromApi | null>(null)

  const handleDeleteCustomer = async () => {
    if (!customerToDelete) return

    const response = await fetch(`/api/customers/${customerToDelete.id}`, {
      method: "DELETE",
    })

    if (response.ok) {
      toast.success(`Customer "${customerToDelete.name}" deleted successfully.`)
      setCustomerToDelete(null)
      router.refresh()
    } else {
      const errorData = await response.json().catch(() => ({}))
      toast.error("Failed to delete customer.", {
        description: errorData.error || "An unexpected error occurred.",
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "default"
      case "prospect":
        return "secondary"
      case "inactive":
        return "destructive"
      default:
        return "secondary"
    }
  }

  const filteredCustomers = initialCustomers.filter((customer) => {
    const matchesSearch =
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (customer.contact_name || "").toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === "all" || customer.status.toLowerCase() === filterStatus
    return matchesSearch && matchesStatus
  })

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Customer Relationship Management</h1>
          <p className="text-muted-foreground">Manage customer relationships, opportunities, and interactions</p>
        </div>
        <Dialog open={isAddCustomerOpen} onOpenChange={setAddCustomerOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Customer
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Customer</DialogTitle>
              <DialogDescription>Enter the details of the new customer below.</DialogDescription>
            </DialogHeader>
            <AddCustomerForm setOpen={setAddCustomerOpen} />
          </DialogContent>
        </Dialog>
      </div>
      <Toaster richColors />

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{initialCustomers.length}</div>
            <p className="text-xs text-muted-foreground">From database</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="customers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="customers">Customers</TabsTrigger>
        </TabsList>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Database</CardTitle>
              <CardDescription>Manage your customer relationships and contact information</CardDescription>
              <div className="flex gap-4">
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search customers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64"
                  />
                </div>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="prospect">Prospect</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredCustomers.map((customer) => (
                  <Link key={customer.id} href={`/crm/${customer.id}`} className="block hover:bg-muted/50 rounded-lg">
                    <Card>
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <CardTitle className="text-lg">{customer.name}</CardTitle>
                              <Badge variant={getStatusColor(customer.status)}>{customer.status}</Badge>
                            </div>
                            <CardDescription>{customer.address}</CardDescription>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.preventDefault()
                                setCustomerToEdit(customer)
                              }}
                            >
                              <FilePen className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.preventDefault()
                                setCustomerToDelete(customer)
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                          <div className="space-y-3">
                            <Label className="text-sm font-medium">Contact Information</Label>
                            <div className="space-y-2">
                              {customer.contact_name && (
                                <div className="flex items-center gap-2 text-sm">
                                  <Users className="h-4 w-4 text-muted-foreground" />
                                  {customer.contact_name}
                                </div>
                              )}
                              {customer.contact_email && (
                                <div className="flex items-center gap-2 text-sm">
                                  <Mail className="h-4 w-4 text-muted-foreground" />
                                  {customer.contact_email}
                                </div>
                              )}
                              {customer.contact_phone && (
                                <div className="flex items-center gap-2 text-sm">
                                  <Phone className="h-4 w-4 text-muted-foreground" />
                                  {customer.contact_phone}
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="space-y-3">
                            <Label className="text-sm font-medium">Terms</Label>
                            <div className="space-y-2 text-sm">
                              <div>
                                <strong>Incoterm:</strong> {customer.incoterm || "N/A"}
                              </div>
                              <div>
                                <strong>Payment:</strong> {customer.payment_term || "N/A"}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <AlertDialog open={!!customerToDelete} onOpenChange={(open) => !open && setCustomerToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the customer "{customerToDelete?.name}".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteCustomer}>Continue</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={!!customerToEdit} onOpenChange={(open) => !open && setCustomerToEdit(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Customer</DialogTitle>
            <DialogDescription>Update the details of the customer below.</DialogDescription>
          </DialogHeader>
          {customerToEdit && <EditCustomerForm setOpen={(open) => !open && setCustomerToEdit(null)} customer={customerToEdit} />}
        </DialogContent>
      </Dialog>
    </div>
  )
}