"use client"

import { AppShell } from "@/components/app-shell"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"
import { FileText, Upload, Download, Search, Eye, Edit, Trash2, Plus, Calendar, User, Tag } from "lucide-react"

export default function DocsPage() {
  const { t } = useI18n()
  return (
    <AppShell>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Document Center</h1>
            <p className="text-muted-foreground">
              Manage export compliance documents, certificates, and business records
            </p>
          </div>
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        </div>
        
        <Tabs defaultValue="export-docs" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="export-docs">Export Documents</TabsTrigger>
            <TabsTrigger value="certificates">Certificates</TabsTrigger>
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
          </TabsList>
          
          <TabsContent value="export-docs" className="space-y-6">
            <ExportDocumentsCard />
          </TabsContent>
          
          <TabsContent value="certificates" className="space-y-6">
            <CertificatesCard />
          </TabsContent>
          
          <TabsContent value="contracts" className="space-y-6">
            <ContractsCard />
          </TabsContent>
          
          <TabsContent value="compliance" className="space-y-6">
            <ComplianceCard />
          </TabsContent>
        </Tabs>
      </div>
    </AppShell>
  )
}

function ExportDocumentsCard() {
  const { t } = useI18n()
  const [documents, setDocuments] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("")
  const [filterStatus, setFilterStatus] = useState("")

  useEffect(() => {
    safeJson("/api/documents/export", []).then(setDocuments)
  }, [])

  const mockDocuments = [
    {
      id: 1,
      name: "Commercial Invoice - EXP001",
      type: "Commercial Invoice",
      export_declaration: "EXP001",
      status: "approved",
      created_date: "2024-01-15",
      file_size: "245 KB",
      created_by: "John Smith"
    },
    {
      id: 2,
      name: "Packing List - EXP001",
      type: "Packing List",
      export_declaration: "EXP001",
      status: "pending",
      created_date: "2024-01-15",
      file_size: "128 KB",
      created_by: "Sarah Johnson"
    },
    {
      id: 3,
      name: "Bill of Lading - EXP002",
      type: "Bill of Lading",
      export_declaration: "EXP002",
      status: "approved",
      created_date: "2024-01-14",
      file_size: "189 KB",
      created_by: "Mike Wilson"
    },
    {
      id: 4,
      name: "Certificate of Origin - EXP001",
      type: "Certificate of Origin",
      export_declaration: "EXP001",
      status: "draft",
      created_date: "2024-01-13",
      file_size: "156 KB",
      created_by: "Lisa Chen"
    }
  ]

  const filteredDocuments = mockDocuments.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.export_declaration.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = !filterType || doc.type === filterType
    const matchesStatus = !filterStatus || doc.status === filterStatus
    return matchesSearch && matchesType && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Export Documents
            </CardTitle>
            <CardDescription>
              Manage export-related documents and compliance paperwork
            </CardDescription>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Generate Document
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Types</SelectItem>
                <SelectItem value="Commercial Invoice">Commercial Invoice</SelectItem>
                <SelectItem value="Packing List">Packing List</SelectItem>
                <SelectItem value="Bill of Lading">Bill of Lading</SelectItem>
                <SelectItem value="Certificate of Origin">Certificate of Origin</SelectItem>
                <SelectItem value="Export License">Export License</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Document Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Export Declaration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created Date</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Created By</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDocuments.map((doc) => (
                <TableRow key={doc.id}>
                  <TableCell className="font-medium">{doc.name}</TableCell>
                  <TableCell>{doc.type}</TableCell>
                  <TableCell>{doc.export_declaration}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(doc.status)}>
                      {doc.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{doc.created_date}</TableCell>
                  <TableCell>{doc.file_size}</TableCell>
                  <TableCell>{doc.created_by}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}

function CertificatesCard() {
  const { t } = useI18n()
  
  const mockCertificates = [
    {
      id: 1,
      name: "ISO 9001:2015 Quality Management",
      type: "Quality Certificate",
      issuer: "SGS International",
      issue_date: "2023-06-15",
      expiry_date: "2026-06-15",
      status: "active",
      file_size: "1.2 MB"
    },
    {
      id: 2,
      name: "OEKO-TEX Standard 100",
      type: "Textile Certificate",
      issuer: "OEKO-TEX Association",
      issue_date: "2023-09-20",
      expiry_date: "2024-09-20",
      status: "expiring_soon",
      file_size: "856 KB"
    },
    {
      id: 3,
      name: "GOTS Organic Textile Certificate",
      type: "Organic Certificate",
      issuer: "Control Union",
      issue_date: "2023-03-10",
      expiry_date: "2024-03-10",
      status: "expired",
      file_size: "945 KB"
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'expiring_soon': return 'bg-yellow-100 text-yellow-800'
      case 'expired': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Certificates & Licenses
            </CardTitle>
            <CardDescription>
              Manage quality certificates, licenses, and compliance documents
            </CardDescription>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Certificate
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Certificate Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Issuer</TableHead>
              <TableHead>Issue Date</TableHead>
              <TableHead>Expiry Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Size</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockCertificates.map((cert) => (
              <TableRow key={cert.id}>
                <TableCell className="font-medium">{cert.name}</TableCell>
                <TableCell>{cert.type}</TableCell>
                <TableCell>{cert.issuer}</TableCell>
                <TableCell>{cert.issue_date}</TableCell>
                <TableCell>{cert.expiry_date}</TableCell>
                <TableCell>
                  <Badge className={getStatusColor(cert.status)}>
                    {cert.status.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell>{cert.file_size}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

function ContractsCard() {
  const { t } = useI18n()
  
  const mockContracts = [
    {
      id: 1,
      name: "Sales Contract - Fashion Retailer Inc",
      type: "Sales Contract",
      customer: "Fashion Retailer Inc",
      contract_date: "2024-01-10",
      value: "$125,000",
      status: "active",
      file_size: "2.1 MB"
    },
    {
      id: 2,
      name: "Purchase Agreement - Cotton Suppliers Ltd",
      type: "Purchase Contract",
      supplier: "Cotton Suppliers Ltd",
      contract_date: "2023-12-15",
      value: "$85,000",
      status: "completed",
      file_size: "1.8 MB"
    },
    {
      id: 3,
      name: "Service Agreement - Logistics Partner",
      type: "Service Contract",
      partner: "Global Logistics Co",
      contract_date: "2024-01-05",
      value: "$45,000",
      status: "pending",
      file_size: "1.5 MB"
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Contract Documents
            </CardTitle>
            <CardDescription>
              Manage sales contracts, purchase agreements, and service contracts
            </CardDescription>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Upload Contract
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Contract Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Party</TableHead>
              <TableHead>Contract Date</TableHead>
              <TableHead>Value</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Size</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockContracts.map((contract) => (
              <TableRow key={contract.id}>
                <TableCell className="font-medium">{contract.name}</TableCell>
                <TableCell>{contract.type}</TableCell>
                <TableCell>{contract.customer || contract.supplier || contract.partner}</TableCell>
                <TableCell>{contract.contract_date}</TableCell>
                <TableCell>{contract.value}</TableCell>
                <TableCell>
                  <Badge className={getStatusColor(contract.status)}>
                    {contract.status}
                  </Badge>
                </TableCell>
                <TableCell>{contract.file_size}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

function ComplianceCard() {
  const { t } = useI18n()
  
  const mockCompliance = [
    {
      id: 1,
      name: "Export Control Classification",
      type: "Export Compliance",
      regulation: "EAR (Export Administration Regulations)",
      last_updated: "2024-01-12",
      next_review: "2024-07-12",
      status: "compliant",
      file_size: "456 KB"
    },
    {
      id: 2,
      name: "Customs Valuation Report",
      type: "Customs Compliance",
      regulation: "WTO Valuation Agreement",
      last_updated: "2024-01-08",
      next_review: "2024-04-08",
      status: "review_required",
      file_size: "789 KB"
    },
    {
      id: 3,
      name: "Anti-Dumping Duty Assessment",
      type: "Trade Compliance",
      regulation: "WTO Anti-Dumping Agreement",
      last_updated: "2023-12-20",
      next_review: "2024-03-20",
      status: "compliant",
      file_size: "623 KB"
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'bg-green-100 text-green-800'
      case 'review_required': return 'bg-yellow-100 text-yellow-800'
      case 'non_compliant': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Compliance Documents
            </CardTitle>
            <CardDescription>
              Track regulatory compliance and trade documentation
            </CardDescription>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Compliance Doc
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Document Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Regulation</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead>Next Review</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Size</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockCompliance.map((doc) => (
              <TableRow key={doc.id}>
                <TableCell className="font-medium">{doc.name}</TableCell>
                <TableCell>{doc.type}</TableCell>
                <TableCell>{doc.regulation}</TableCell>
                <TableCell>{doc.last_updated}</TableCell>
                <TableCell>{doc.next_review}</TableCell>
                <TableCell>
                  <Badge className={getStatusColor(doc.status)}>
                    {doc.status.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell>{doc.file_size}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}