"use client"

import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"

export default function ProductionPage() {
  return (
    <AppShell>
      <div className="grid gap-6">
        <WorkOrdersCard />
      </div>
    </AppShell>
  )
}

function WorkOrdersCard() {
  const { t } = useI18n()
  const [sales, setSales] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [rows, setRows] = useState<any[]>([])
  const [form, setForm] = useState({ number: "", sales_contract_id: "", product_id: "", qty: 100 })

  async function load() {
    try {
      const [s, p, w] = await Promise.all([
        safeJson("/api/contracts/sales", []),
        safeJson("/api/products", []),
        safeJson("/api/production/work-orders", []),
      ])
      setSales(Array.isArray(s) ? s : [])
      setProducts(Array.isArray(p) ? p : [])
      setRows(Array.isArray(w) ? w : [])
    } catch (error) {
      console.error("Failed to load production data:", error)
      setSales([])
      setProducts([])
      setRows([])
    }
  }
  useEffect(() => {
    load()
  }, [])

  async function create() {
    const { number, sales_contract_id, product_id, qty } = form
    if (!number || !sales_contract_id || !product_id || !qty) return
    await fetch("/api/production/work-orders", { method: "POST", body: JSON.stringify(form) })
    setForm({ number: "", sales_contract_id: "", product_id: "", qty: 100 })
    await load()
  }

  async function advance(woId: string, opId: string) {
    await fetch(`/api/production/work-orders/${woId}`, {
      method: "PATCH",
      body: JSON.stringify({ action: "advance", opId }),
    })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/production/work-orders/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("production.title")}</CardTitle>
        <CardDescription>{t("production.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-5">
          <div className="grid gap-1">
            <Label>{t("field.woNumber")}</Label>
            <Input value={form.number} onChange={(e) => setForm({ ...form, number: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.salesContract")}</Label>
            <Select value={form.sales_contract_id} onValueChange={(v) => setForm({ ...form, sales_contract_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {(sales || []).map((s) => (
                  <SelectItem key={s.id} value={s.id}>
                    {s.number}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.product")}</Label>
            <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {(products || []).map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.sku} - {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.qty")}</Label>
            <Input type="number" value={form.qty} onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })} />
          </div>
          <div className="flex items-end">
            <Button onClick={create}>{t("action.createWO")}</Button>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>WO</TableHead>
              <TableHead>{t("field.product")}</TableHead>
              <TableHead>{t("field.qty")}</TableHead>
              <TableHead>Operations</TableHead>
              <TableHead>{t("table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {(rows || []).map((w) => (
              <TableRow key={w.id}>
                <TableCell>{w.number}</TableCell>
                <TableCell>{products.find((p) => p.id === w.product_id)?.name}</TableCell>
                <TableCell>{w.qty}</TableCell>
                <TableCell className="space-x-1">
                  {(w.operations || []).map((o: any) => (
                    <Button
                      key={o.id}
                      size="sm"
                      variant={o.status === "done" ? "secondary" : "outline"}
                      onClick={() => advance(w.id, o.id)}
                    >
                      {o.name}: {o.status}
                    </Button>
                  ))}
                </TableCell>
                <TableCell className="text-right">
                  <Button size="sm" variant="ghost" onClick={() => remove(w.id)}>
                    {t("action.delete")}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {rows.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-muted-foreground">
                  {t("production.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
