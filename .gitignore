# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage
/test-results
*.db
*.sqlite

# next.js
/.next/
/out/

# production
/build
/dist

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*
.env.test

# IDE
.vscode/
.idea/

# Logs
logs/
*.log

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
