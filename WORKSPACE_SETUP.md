# Manufacturing ERP - Complete Workspace Setup Guide

## 🚀 Project Overview
**Manufacturing ERP System** - Enterprise-grade multi-tenant ERP system built with Next.js 14, PostgreSQL, and Auth0 authentication.

## 📁 Project Structure
```
V0-Silk-Jhon/
├── app/                    # Next.js 14 App Router
│   ├── api/               # API Routes
│   ├── dashboard/         # Dashboard pages
│   ├── customers/         # Customer management
│   ├── products/          # Product management
│   └── contracts/         # Contract management
├── components/            # React components
├── lib/                   # Utilities and configurations
│   ├── db.ts             # PostgreSQL connection
│   ├── schema-postgres.ts # Database schema
│   └── api-helpers.ts    # API utilities
├── docs/                  # Project documentation
├── .env.local            # Environment variables
├── vercel.json           # Deployment configuration
└── package.json          # Dependencies
```

## 🔧 Current System Status

### ✅ Completed Features
- **Phase 1 COMPLETE**: Customers & Products modules fully functional
- **Contract Template System**: Professional sales/purchase templates with PDF export
- **Multi-tenant Security**: Enterprise-grade data isolation with Auth0
- **PostgreSQL Migration**: Upgraded to PostgreSQL 17.6 (latest stable)
- **Sentry Integration**: Error tracking and monitoring (JUST ADDED)

### 🎯 Current Issue
**Production Database Connection Error**: 
- Error: `getaddrinfo ENOTFOUND db.jwdryepnhrigricdxenc.supabase.co`
- Root Cause: Database URL mismatch between environments
- Status: Sentry monitoring added to capture detailed error information

## 🔑 Environment Configuration

### Local Development (.env.local)
```bash
# Auth0 Configuration
AUTH0_SECRET=****************************************************************
AUTH0_BASE_URL=http://localhost:3000
AUTH0_ISSUER_BASE_URL=https://dev-tejx02ztaj7oufoc.us.auth0.com
AUTH0_CLIENT_ID=To2oAQX05DtstsgKXdLvsUYAaRO23QCK
AUTH0_CLIENT_SECRET=****************************************************************

# Database Configuration
DATABASE_URL=********************************************/postgres?sslmode=require

# Sentry Configuration (NEWLY ADDED)
SENTRY_ORG=dassodev
SENTRY_PROJECT=manufacturing-erp
SENTRY_DSN=https://<EMAIL>/****************
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/****************

# PostgreSQL Settings
USE_POSTGRESQL=true
MIGRATION_PHASE=production
ENFORCE_TENANT_ISOLATION=true
ENABLE_AUDIT_LOGGING=true
```

### Production (vercel.json)
```json
{
  "env": {
    "DATABASE_URL": "*****************************************************************/postgres",
    "DATABASE_URL_POSTGRESQL": "*****************************************************************/postgres",
    "SENTRY_ORG": "dassodev",
    "SENTRY_PROJECT": "manufacturing-erp",
    "SENTRY_DSN": "https://<EMAIL>/****************",
    "NEXT_PUBLIC_SENTRY_DSN": "https://<EMAIL>/****************"
  }
}
```

## 🔍 Database Connection Analysis

### Issue Identified
The database connection is failing because of URL format mismatch:
- **Local (incorrect)**: `db.jwdryepnhrigricdxenc.supabase.co:5432`
- **Production (correct)**: `aws-0-ap-southeast-1.pooler.supabase.com:6543`

### Connection Priority (lib/db.ts)
```typescript
const connectionUrl = process.env.DATABASE_URL_POSTGRESQL ||
                     process.env.POSTGRES_LOCAL_URL ||
                     process.env.DATABASE_URL ||
                     'postgresql://erp_user:SecureERP2024!@localhost:5432/manufacturing_erp';
```

## 📊 Sentry Configuration Details

### Organization Settings
- **Organization Slug**: `dassodev`
- **Project Name**: `manufacturing-erp`
- **Project ID**: `****************`
- **DSN**: `https://<EMAIL>/****************`

### Enhanced Error Tracking
Added to critical endpoints:
- `/api/companies/ensure` - Company creation errors
- `/api/health` - Database connection monitoring
- Includes user context, environment details, and error classification

## 🧪 Test Accounts
- **Primary**: <EMAIL> (Password: Ashatank@@2020)
- **Secondary**: <EMAIL> (Password: Ashatank@@2020)

## 🌐 Deployment URLs
- **Production**: https://silk-road-john.vercel.app
- **Repository**: https://github.com/dassodev/V0-Silk-Jhon.git
- **Sentry Dashboard**: https://sentry.io/organizations/dassodev/projects/manufacturing-erp/

## 📚 Key Documentation Files
- `SYSTEM_STATUS.md` - Current system state
- `TECHNICAL_REFERENCE.md` - API and schema reference
- `PHASE_2_PLAN.md` - Development roadmap
- `QUICK_START_GUIDE.md` - Session context for future work

## 🚨 Current Priority: Database Connection Fix

### Immediate Action Required
1. **Fix Database URL Mismatch**: Update local environment to use correct Supabase URL
2. **Verify Sentry Integration**: Confirm error tracking is working in production
3. **Test Production Deployment**: Ensure all API endpoints work correctly

### Next Steps for VSCode Setup
1. Open this workspace in VSCode
2. Install recommended extensions (Next.js, TypeScript, Tailwind CSS)
3. Run `npm install` to install dependencies
4. Configure Sentry properly in development environment
5. Test local development server with `npm run dev`

## 🔧 Quick Commands
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Run database migrations
npm run db:migrate

# Check database health
curl http://localhost:3000/api/health
```

## 🎯 Development Priorities
1. **Fix Production Database Connection** (URGENT)
2. **Complete Phase 2**: Sales Contract CRUD operations
3. **Implement Quality Control Module** (48 tasks ready)
4. **Advanced Inventory Management**
5. **Next.js 15 Compatibility**

## 💾 AI Assistant Memory Context
This workspace contains the complete Manufacturing ERP system with:
- Enterprise-grade multi-tenant security
- PostgreSQL 17.6 database
- Auth0 authentication
- Sentry error tracking
- Professional contract template system
- Production-ready customer/product modules
- Comprehensive documentation and development roadmap
