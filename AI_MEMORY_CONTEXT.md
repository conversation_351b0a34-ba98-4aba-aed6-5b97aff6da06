# AI Assistant Memory & Context - Manufacturing ERP Project

## 🧠 Complete Project Memory

### Manufacturing ERP Core System Status
- **Phase 1 COMPLETE**: Customers and Products modules 100% production-ready
- **Enterprise Security**: Multi-tenant isolation with Auth0, zero cross-contamination verified
- **Database**: PostgreSQL 17.6 (latest stable), 5-year support window, 6.56ms avg response
- **Contract Templates**: Professional sales/purchase templates with full-screen viewer and PDF export
- **Architecture**: Next.js 14 App Router, Drizzle ORM, Tailwind CSS, TypeScript

### Current Critical Issue (December 19, 2024)
**Production Database Connection Failure**:
- Error: `getaddrinfo ENOTFOUND db.jwdryepnhrigricdxenc.supabase.co`
- Root Cause: Database URL format mismatch between local and production
- Local URL (incorrect): `db.jwdryepnhrigricdxenc.supabase.co:5432`
- Production URL (correct): `aws-0-ap-southeast-1.pooler.supabase.com:6543`
- **Sentry Integration Added**: Real-time error tracking configured today

### User Preferences & Requirements
- **Data Approach**: Real production data over mock data, interconnected database records
- **UI/UX**: Table/list layouts over cards, professional enterprise appearance
- **Development**: Step-by-step Git explanations, production-ready implementations
- **Testing**: Manual testing preferred over automated Chrome MCP debugging
- **Deployment**: Chinese cloud platforms (Alibaba/Tencent) for final production
- **Authentication**: Auth0 with both email/password and Google OAuth support
- **Package Management**: Always use package managers (npm/yarn) never manual file editing

### Technical Architecture Details

#### Database Configuration (lib/db.ts)
```typescript
const connectionUrl = process.env.DATABASE_URL_POSTGRESQL ||
                     process.env.POSTGRES_LOCAL_URL ||
                     process.env.DATABASE_URL ||
                     'postgresql://erp_user:SecureERP2024!@localhost:5432/manufacturing_erp';
```

#### Multi-Tenant Security Implementation
- `withTenantAuth()` middleware for API endpoints
- `getTenantContext()` for server-side pages
- Automatic company creation via `/api/companies/ensure`
- `company_id` filtering in all database queries

#### Authentication Flow
- Auth0 integration with automatic user onboarding
- Creates isolated company workspaces for new users
- Supports both email/password and Google OAuth
- Test accounts: <EMAIL> / <EMAIL> (Ashatank@@2020)

### Contract Template System Architecture
- **Components**: 
  - `contract-document-viewer.tsx` (full-screen 98% width viewer)
  - `pdf-contract-document.tsx` (client-side PDF generation)
- **API**: `/api/contracts/templates` for template management
- **Features**: Professional document formatting, React-PDF export, clean UI
- **Status**: Phase 2 Contract Template System COMPLETE

### Quality Control Module Status
- **Readiness**: 15/100 (mockup stage)
- **Implementation Plan**: 48 tasks across 4 phases created
- **Database Schema**: 5 quality tables with relationships designed
- **Priority**: Scheduled after Phase 2 sales contract CRUD completion

### Development Environment & Tools
- **Context7 MCP**: Access to Next.js docs (3604 code snippets, trust score 10)
- **Chrome MCP**: Browser automation and testing capabilities
- **Sentry Integration**: Real-time error tracking and performance monitoring
- **Local Development**: PostgreSQL with Homebrew installation option

### Deployment Configuration
- **Production URL**: https://silk-road-john.vercel.app
- **Repository**: https://github.com/dassodev/V0-Silk-Jhon.git
- **Vercel Project**: silk-road-john
- **Supabase Project**: manufacturing-erp (ID: jwdryepnhrigricdxenc)
- **Sentry Project**: dassodev/manufacturing-erp

### Documentation Status
All documentation comprehensively updated to reflect:
- PostgreSQL migration completion
- Contract template system implementation
- Enterprise-grade security verification
- Current system architecture and API references
- Development roadmap prioritizing business features

### Immediate Action Items
1. **URGENT**: Fix production database connection URL mismatch
2. **Verify**: Sentry error tracking in production environment
3. **Complete**: Phase 2 sales contract CRUD operations
4. **Implement**: Quality Control Module (48 tasks ready)
5. **Upgrade**: Next.js 15 compatibility improvements

### Key Files for VSCode Workspace
- `WORKSPACE_SETUP.md` - Complete setup guide
- `SYSTEM_STATUS.md` - Current system state
- `TECHNICAL_REFERENCE.md` - API and schema reference
- `PHASE_2_PLAN.md` - Development roadmap
- `QUICK_START_GUIDE.md` - Session context
- `.env.local` - Environment configuration
- `vercel.json` - Production deployment settings

### Development Workflow
1. Local development with Auth0 configured for both local and production
2. Manual testing and user feedback collection
3. Git commit with detailed explanations
4. Automatic GitHub deployment to Vercel
5. Sentry monitoring for production issues
6. User validation and iteration

This memory context ensures continuity across AI assistant sessions and provides complete project understanding for VSCode development environment setup.
