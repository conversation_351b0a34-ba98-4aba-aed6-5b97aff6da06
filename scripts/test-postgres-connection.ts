#!/usr/bin/env tsx

/**
 * Manufacturing ERP - PostgreSQL Connection Test
 * 
 * Tests the new PostgreSQL-only database connection
 */

import { db } from '../lib/db';

async function testConnection() {
  try {
    console.log('🐘 Testing PostgreSQL connection...');
    
    // Test basic query
    const result = await db.execute('SELECT current_database(), current_user, version()');
    console.log('✅ Connection successful!');
    console.log('Database:', (result as any)[0]?.current_database);
    console.log('User:', (result as any)[0]?.current_user);
    console.log('Version:', (result as any)[0]?.version?.split(' ').slice(0, 2).join(' '));
    
    // Test table existence
    const tables = await db.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);
    
    console.log(`\n📊 Found ${(tables as any).length} tables:`);
    (tables as any).forEach((table: any) => {
      console.log(`  - ${table.table_name}`);
    });
    
    console.log('\n🎉 PostgreSQL connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Connection test failed:', error);
    process.exit(1);
  }
}

testConnection();
