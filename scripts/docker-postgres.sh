#!/bin/bash

# Manufacturing ERP - PostgreSQL Docker Management Script
# Provides easy commands to manage the PostgreSQL development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env.docker"
PROJECT_NAME="manufacturing-erp"

# Functions
print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  Manufacturing ERP - PostgreSQL Docker Manager"
    echo "=================================================="
    echo -e "${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_requirements() {
    print_info "Checking requirements..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Requirements check passed"
}

setup_directories() {
    print_info "Setting up directories..."
    
    mkdir -p docker/volumes/postgres
    mkdir -p docker/volumes/pgadmin
    mkdir -p docker/volumes/redis
    mkdir -p docker/postgres/init
    mkdir -p docker/pgadmin
    
    # Set proper permissions
    chmod 755 docker/volumes/postgres
    chmod 755 docker/volumes/pgadmin
    chmod 755 docker/volumes/redis
    
    print_success "Directories created successfully"
}

start_services() {
    print_info "Starting PostgreSQL services..."
    
    # Load environment variables
    if [ -f "$ENV_FILE" ]; then
        export $(cat $ENV_FILE | grep -v '^#' | xargs)
    fi
    
    # Start services
    docker-compose --project-name $PROJECT_NAME up -d
    
    print_success "Services started successfully"
    print_info "PostgreSQL: localhost:5432"
    print_info "pgAdmin: http://localhost:5050"
    print_info "Redis: localhost:6379"
}

stop_services() {
    print_info "Stopping PostgreSQL services..."
    
    docker-compose --project-name $PROJECT_NAME down
    
    print_success "Services stopped successfully"
}

restart_services() {
    print_info "Restarting PostgreSQL services..."
    
    docker-compose --project-name $PROJECT_NAME restart
    
    print_success "Services restarted successfully"
}

show_status() {
    print_info "Service Status:"
    docker-compose --project-name $PROJECT_NAME ps
}

show_logs() {
    local service=${1:-postgres}
    print_info "Showing logs for service: $service"
    docker-compose --project-name $PROJECT_NAME logs -f $service
}

connect_postgres() {
    print_info "Connecting to PostgreSQL..."
    docker exec -it manufacturing-erp-postgres psql -U erp_user -d manufacturing_erp
}

backup_database() {
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    print_info "Creating database backup: $backup_file"
    
    docker exec manufacturing-erp-postgres pg_dump -U erp_user manufacturing_erp > "backups/$backup_file"
    
    print_success "Backup created: backups/$backup_file"
}

restore_database() {
    local backup_file=$1
    if [ -z "$backup_file" ]; then
        print_error "Please specify backup file: ./scripts/docker-postgres.sh restore <backup_file>"
        exit 1
    fi
    
    print_warning "This will restore the database from: $backup_file"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Restoring database..."
        docker exec -i manufacturing-erp-postgres psql -U erp_user manufacturing_erp < "$backup_file"
        print_success "Database restored successfully"
    else
        print_info "Restore cancelled"
    fi
}

clean_volumes() {
    print_warning "This will delete all PostgreSQL data!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Stopping services..."
        docker-compose --project-name $PROJECT_NAME down -v
        
        print_info "Cleaning volumes..."
        rm -rf docker/volumes/postgres/*
        rm -rf docker/volumes/pgadmin/*
        rm -rf docker/volumes/redis/*
        
        print_success "Volumes cleaned successfully"
    else
        print_info "Clean cancelled"
    fi
}

show_help() {
    print_header
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start PostgreSQL services"
    echo "  stop        Stop PostgreSQL services"
    echo "  restart     Restart PostgreSQL services"
    echo "  status      Show service status"
    echo "  logs        Show service logs (default: postgres)"
    echo "  connect     Connect to PostgreSQL CLI"
    echo "  backup      Create database backup"
    echo "  restore     Restore database from backup"
    echo "  clean       Clean all volumes (WARNING: deletes data)"
    echo "  setup       Setup directories and permissions"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start all services"
    echo "  $0 logs postgres           # Show PostgreSQL logs"
    echo "  $0 logs pgadmin            # Show pgAdmin logs"
    echo "  $0 backup                  # Create backup"
    echo "  $0 restore backup.sql      # Restore from backup"
}

# Main script logic
case "${1:-help}" in
    start)
        print_header
        check_requirements
        setup_directories
        start_services
        ;;
    stop)
        print_header
        stop_services
        ;;
    restart)
        print_header
        restart_services
        ;;
    status)
        print_header
        show_status
        ;;
    logs)
        print_header
        show_logs $2
        ;;
    connect)
        print_header
        connect_postgres
        ;;
    backup)
        print_header
        mkdir -p backups
        backup_database
        ;;
    restore)
        print_header
        restore_database $2
        ;;
    clean)
        print_header
        clean_volumes
        ;;
    setup)
        print_header
        check_requirements
        setup_directories
        ;;
    help|*)
        show_help
        ;;
esac
