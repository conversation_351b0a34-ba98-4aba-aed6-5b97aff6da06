#!/usr/bin/env tsx

/**
 * Test User Session Creator
 * 
 * This script helps create and verify proper multi-tenant isolation
 * by setting up test scenarios and providing testing instructions.
 */

import { db } from '../lib/db'
import { companies } from '../lib/schema-postgres'
import { eq } from 'drizzle-orm'

interface TestUser {
  email: string
  auth0UserId: string
  companyName: string
  companyId: string
}

async function createTestUserScenario(): Promise<void> {
  console.log('🧪 MULTI-TENANT ISOLATION TEST SETUP')
  console.log('====================================')
  
  // Check existing companies
  const existingCompanies = await db.query.companies.findMany()
  console.log(`📊 Current companies in system: ${existingCompanies.length}`)
  existingCompanies.forEach(company => {
    console.log(`   - ${company.name} (${company.email})`)
  })
  
  console.log('\n🎯 PROPER TESTING INSTRUCTIONS')
  console.log('==============================')
  
  console.log('\n1. 🔐 AUTHENTICATION ISOLATION TEST:')
  console.log('   a) Open Chrome in INCOGNITO mode')
  console.log('   b) Navigate to: http://localhost:3000')
  console.log('   c) Click "Get Started" or "Login"')
  console.log('   d) On Auth0 screen, click "Sign up"')
  console.log('   e) Create account with: <EMAIL>')
  console.log('   f) Complete Auth0 registration')
  
  console.log('\n2. 📊 EXPECTED NEW USER BEHAVIOR:')
  console.log('   ✅ Should redirect to /dashboard')
  console.log('   ✅ Should show "Welcome" (not "Welcome back")')
  console.log('   ✅ Should show empty dashboard (all zeros)')
  console.log('   ✅ Should prompt for company profile setup')
  
  console.log('\n3. 🏢 COMPANY PROFILE CREATION:')
  console.log('   a) Complete company profile with:')
  console.log('      - Company Name: "Test Isolation Company"')
  console.log('      - Email: <EMAIL>')
  console.log('      - Industry: Manufacturing')
  console.log('      - Address: Different from existing')
  console.log('   b) Submit and verify profile creation')
  
  console.log('\n4. 🔍 ISOLATION VERIFICATION:')
  console.log('   a) Check dashboard shows all zeros')
  console.log('   b) Visit each ERP module:')
  console.log('      - /customers (should be empty)')
  console.log('      - /products (should be empty)')
  console.log('      - /suppliers (should be empty)')
  console.log('      - /contracts (should be empty)')
  
  console.log('\n5. 📝 DATA CREATION TEST:')
  console.log('   a) Create test customer: "Isolation Test Customer"')
  console.log('   b) Create test product: "Isolation Test Product"')
  console.log('   c) Verify data appears in dashboard counts')
  console.log('   d) Verify data appears in respective modules')
  
  console.log('\n6. 🔄 CROSS-TENANT VERIFICATION:')
  console.log('   a) Open NEW browser window (not incognito)')
  console.log('   b) Login as original user (<EMAIL>)')
  console.log('   c) Verify original data is still there')
  console.log('   d) Verify new test data is NOT visible')
  
  console.log('\n🚨 CRITICAL SUCCESS CRITERIA:')
  console.log('=============================')
  console.log('✅ New user sees completely empty system')
  console.log('✅ New user can create their own data')
  console.log('✅ New user data is isolated from existing data')
  console.log('✅ Existing user data remains unchanged')
  console.log('✅ No cross-tenant data visibility')
  
  console.log('\n🛠️  DEBUGGING TOOLS:')
  console.log('===================')
  console.log('If issues persist, run these commands:')
  console.log('   npm run diagnose:isolation')
  console.log('   npm run test:comprehensive-security')
  
  console.log('\n📱 BROWSER DEVELOPER TOOLS CHECK:')
  console.log('==================================')
  console.log('1. Open Developer Tools (F12)')
  console.log('2. Go to Network tab')
  console.log('3. Navigate to /products page')
  console.log('4. Check API call to /api/products')
  console.log('5. Verify response contains only your company data')
  console.log('6. Check all returned records have same company_id')
  
  console.log('\n🎯 EXPECTED API RESPONSE FORMAT:')
  console.log('================================')
  console.log('For new user (should be empty):')
  console.log('   GET /api/products → []')
  console.log('   GET /api/customers → []')
  console.log('   GET /api/suppliers → []')
  console.log('')
  console.log('For existing user:')
  console.log('   GET /api/products → [{company_id: "same_id", ...}, ...]')
  console.log('   All records should have identical company_id')
}

async function verifyCurrentSystemState(): Promise<void> {
  console.log('\n🔍 CURRENT SYSTEM STATE VERIFICATION')
  console.log('====================================')
  
  try {
    // Get all companies and their data counts
    const companies = await db.query.companies.findMany()
    
    for (const company of companies) {
      console.log(`\n🏢 Company: ${company.name} (${company.email})`)
      console.log(`   Company ID: ${company.company_id || company.id}`)
      console.log(`   Auth0 ID: ${company.auth0_user_id}`)
      
      // Count data for this company
      const customers = await db.query.customers.findMany({
        where: eq(companies.id, company.id)
      })
      
      const products = await db.query.products.findMany({
        where: eq(companies.id, company.id)
      })
      
      console.log(`   📊 Data: ${customers.length} customers, ${products.length} products`)
    }
    
  } catch (error) {
    console.error('Error verifying system state:', error)
  }
}

// Run if called directly
if (require.main === module) {
  Promise.all([
    createTestUserScenario(),
    verifyCurrentSystemState()
  ]).catch(error => {
    console.error('💥 Test setup failed:', error)
    process.exit(1)
  })
}

export { createTestUserScenario, verifyCurrentSystemState }
