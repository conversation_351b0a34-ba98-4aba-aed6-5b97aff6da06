#!/usr/bin/env tsx

/**
 * Multi-Tenant Security Test Suite
 * 
 * This script tests the multi-tenant isolation implementation to ensure
 * that companies can only access their own data and cannot see or modify
 * data from other companies.
 * 
 * 🛡️ CRITICAL SECURITY TESTING 🛡️
 * This test suite verifies that the multi-tenant security fixes are working
 * correctly and that no data leakage exists between companies.
 * 
 * Usage: npm run test:security
 */

import { db, uid } from '../lib/db'
import { companies, customers, products } from '../lib/schema-postgres'
import { eq, and } from 'drizzle-orm'

interface SecurityTestResults {
  testsRun: number
  testsPassed: number
  testsFailed: number
  vulnerabilities: string[]
  details: string[]
}

async function runSecurityTests(): Promise<SecurityTestResults> {
  const results: SecurityTestResults = {
    testsRun: 0,
    testsPassed: 0,
    testsFailed: 0,
    vulnerabilities: [],
    details: []
  }

  console.log('🛡️  Starting Multi-Tenant Security Test Suite...')
  console.log('🔍 Testing for data isolation vulnerabilities')
  console.log('')

  try {
    // Test 1: Verify company isolation in customers table
    console.log('🧪 Test 1: Customer data isolation')
    results.testsRun++
    
    const allCompanies = await db.query.companies.findMany()
    if (allCompanies.length < 2) {
      results.details.push('⚠️  Warning: Need at least 2 companies to test isolation. Creating test companies...')
      
      // Create test companies for isolation testing
      const testCompany1Id = uid('company')
      const testCompany2Id = uid('company')
      
      await db.insert(companies).values([
        {
          id: testCompany1Id,
          auth0_user_id: 'test-user-1',
          name: 'Test Company 1',
          email: '<EMAIL>',
          onboarding_completed: 'true'
        },
        {
          id: testCompany2Id,
          auth0_user_id: 'test-user-2', 
          name: 'Test Company 2',
          email: '<EMAIL>',
          onboarding_completed: 'true'
        }
      ])
      
      // Create test customers for each company
      await db.insert(customers).values([
        {
          id: uid('cust'),
          company_id: testCompany1Id,
          name: 'Customer for Company 1'
        },
        {
          id: uid('cust'),
          company_id: testCompany2Id,
          name: 'Customer for Company 2'
        }
      ])
    }

    // Get companies for testing
    const testCompanies = await db.query.companies.findMany({ limit: 2 })
    const company1 = testCompanies[0]
    const company2 = testCompanies[1]

    // Test customer isolation
    const company1Customers = await db.query.customers.findMany({
      where: eq(customers.company_id, company1.id)
    })
    
    const company2Customers = await db.query.customers.findMany({
      where: eq(customers.company_id, company2.id)
    })

    // Verify no cross-contamination
    const crossContamination = company1Customers.some(c => c.company_id === company2.id) ||
                              company2Customers.some(c => c.company_id === company1.id)

    if (crossContamination) {
      results.testsFailed++
      results.vulnerabilities.push('CRITICAL: Customer data cross-contamination detected!')
      results.details.push('❌ Test 1 FAILED: Customers from different companies are mixed')
    } else {
      results.testsPassed++
      results.details.push('✅ Test 1 PASSED: Customer data properly isolated')
    }

    // Test 2: Verify company isolation in products table
    console.log('🧪 Test 2: Product data isolation')
    results.testsRun++

    const company1Products = await db.query.products.findMany({
      where: eq(products.company_id, company1.id)
    })
    
    const company2Products = await db.query.products.findMany({
      where: eq(products.company_id, company2.id)
    })

    const productCrossContamination = company1Products.some(p => p.company_id === company2.id) ||
                                     company2Products.some(p => p.company_id === company1.id)

    if (productCrossContamination) {
      results.testsFailed++
      results.vulnerabilities.push('CRITICAL: Product data cross-contamination detected!')
      results.details.push('❌ Test 2 FAILED: Products from different companies are mixed')
    } else {
      results.testsPassed++
      results.details.push('✅ Test 2 PASSED: Product data properly isolated')
    }

    // Test 3: Verify all customers have company_id
    console.log('🧪 Test 3: All customers have company association')
    results.testsRun++

    const customersWithoutCompany = await db.query.customers.findMany()
    const orphanedCustomers = customersWithoutCompany.filter(c => !c.company_id)

    if (orphanedCustomers.length > 0) {
      results.testsFailed++
      results.vulnerabilities.push(`CRITICAL: ${orphanedCustomers.length} customers without company association!`)
      results.details.push(`❌ Test 3 FAILED: Found ${orphanedCustomers.length} orphaned customers`)
    } else {
      results.testsPassed++
      results.details.push('✅ Test 3 PASSED: All customers have company association')
    }

    // Test 4: Verify all products have company_id
    console.log('🧪 Test 4: All products have company association')
    results.testsRun++

    const productsWithoutCompany = await db.query.products.findMany()
    const orphanedProducts = productsWithoutCompany.filter(p => !p.company_id)

    if (orphanedProducts.length > 0) {
      results.testsFailed++
      results.vulnerabilities.push(`CRITICAL: ${orphanedProducts.length} products without company association!`)
      results.details.push(`❌ Test 4 FAILED: Found ${orphanedProducts.length} orphaned products`)
    } else {
      results.testsPassed++
      results.details.push('✅ Test 4 PASSED: All products have company association')
    }

    // Test 5: Verify foreign key constraints
    console.log('🧪 Test 5: Foreign key constraint validation')
    results.testsRun++

    try {
      // Try to create a customer with invalid company_id
      const invalidCustomerId = uid('cust')
      await db.insert(customers).values({
        id: invalidCustomerId,
        company_id: 'invalid-company-id',
        name: 'Test Customer with Invalid Company'
      })
      
      // If we get here, foreign key constraint is not working
      results.testsFailed++
      results.vulnerabilities.push('CRITICAL: Foreign key constraints not enforced!')
      results.details.push('❌ Test 5 FAILED: Could create customer with invalid company_id')
      
      // Clean up the invalid record
      await db.delete(customers).where(eq(customers.id, invalidCustomerId))
      
    } catch (error) {
      // This is expected - foreign key constraint should prevent the insert
      results.testsPassed++
      results.details.push('✅ Test 5 PASSED: Foreign key constraints properly enforced')
    }

    console.log('')
    console.log('🔍 Security testing completed!')

  } catch (error) {
    results.vulnerabilities.push(`Test execution error: ${error}`)
    console.error(`💥 Security test error: ${error}`)
  }

  return results
}

// Print security test results
function printSecurityResults(results: SecurityTestResults) {
  console.log('')
  console.log('🛡️  SECURITY TEST RESULTS')
  console.log('========================')
  console.log(`Tests run: ${results.testsRun}`)
  console.log(`Tests passed: ${results.testsPassed}`)
  console.log(`Tests failed: ${results.testsFailed}`)
  
  if (results.vulnerabilities.length > 0) {
    console.log('')
    console.log('🚨 CRITICAL VULNERABILITIES FOUND:')
    results.vulnerabilities.forEach(vuln => console.log(`   💥 ${vuln}`))
    console.log('')
    console.log('❌ SECURITY STATUS: VULNERABLE - DO NOT DEPLOY TO PRODUCTION')
  } else {
    console.log('')
    console.log('✅ SECURITY STATUS: SECURE - Multi-tenant isolation working correctly')
  }
  
  console.log('')
  console.log('📋 DETAILED RESULTS:')
  results.details.forEach(detail => console.log(`   ${detail}`))
  
  console.log('')
  if (results.testsFailed === 0) {
    console.log('🎉 All security tests passed! Your system is properly isolated.')
  } else {
    console.log('⚠️  Security issues detected. Please fix vulnerabilities before deployment.')
  }
}

// Run tests if called directly
if (require.main === module) {
  runSecurityTests()
    .then(printSecurityResults)
    .catch(error => {
      console.error('💥 Security testing failed:', error)
      process.exit(1)
    })
}

export { runSecurityTests, printSecurityResults }
