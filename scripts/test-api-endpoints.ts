#!/usr/bin/env tsx

/**
 * Manufacturing ERP - API Endpoints Testing Script
 * 
 * Comprehensive testing of all API endpoints with PostgreSQL backend
 * Tests CRUD operations, data consistency, and response validation
 */

import { db } from '../lib/db';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title: string) {
  console.log('\n' + colorize('='.repeat(70), 'cyan'));
  console.log(colorize(`  ${title}`, 'bright'));
  console.log(colorize('='.repeat(70), 'cyan'));
}

function printSection(title: string) {
  console.log('\n' + colorize(`🔍 ${title}`, 'blue'));
  console.log(colorize('-'.repeat(50), 'blue'));
}

interface TestResult {
  endpoint: string;
  method: string;
  passed: boolean;
  responseTime: number;
  statusCode?: number;
  error?: string;
  dataCount?: number;
}

async function testDatabaseConnection(): Promise<boolean> {
  printSection('Database Connection Test');
  
  try {
    // Test basic query
    const result = await db.execute('SELECT current_database(), current_user, version()');
    console.log(colorize('✅ PostgreSQL connection successful', 'green'));
    console.log(`   Database: ${(result as any)[0]?.current_database}`);
    console.log(`   User: ${(result as any)[0]?.current_user}`);
    console.log(`   Version: ${(result as any)[0]?.version?.split(' ').slice(0, 2).join(' ')}`);
    return true;
  } catch (error) {
    console.log(colorize(`❌ Database connection failed: ${error}`, 'red'));
    return false;
  }
}

async function testDataRetrieval(): Promise<TestResult[]> {
  printSection('Data Retrieval Tests');
  
  const results: TestResult[] = [];
  
  // Test companies data
  try {
    const startTime = Date.now();
    const companies = await db.query.companies.findMany();
    const responseTime = Date.now() - startTime;
    
    results.push({
      endpoint: 'companies',
      method: 'SELECT',
      passed: true,
      responseTime,
      dataCount: companies.length
    });
    
    console.log(colorize(`✅ Companies: ${companies.length} records (${responseTime}ms)`, 'green'));
    
    // Show sample company data
    if (companies.length > 0) {
      const sample = companies[0];
      console.log(`   Sample: ${sample.name} (${sample.email})`);
    }
    
  } catch (error) {
    results.push({
      endpoint: 'companies',
      method: 'SELECT',
      passed: false,
      responseTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log(colorize(`❌ Companies query failed: ${error}`, 'red'));
  }
  
  // Test customers data
  try {
    const startTime = Date.now();
    const customers = await db.query.customers.findMany({
      with: { company: true }
    });
    const responseTime = Date.now() - startTime;
    
    results.push({
      endpoint: 'customers',
      method: 'SELECT',
      passed: true,
      responseTime,
      dataCount: customers.length
    });
    
    console.log(colorize(`✅ Customers: ${customers.length} records (${responseTime}ms)`, 'green'));
    
    // Test relationship data
    if (customers.length > 0) {
      const sample = customers[0];
      console.log(`   Sample: ${sample.name} → Company: ${sample.company?.name}`);
    }
    
  } catch (error) {
    results.push({
      endpoint: 'customers',
      method: 'SELECT',
      passed: false,
      responseTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log(colorize(`❌ Customers query failed: ${error}`, 'red'));
  }
  
  // Test products data
  try {
    const startTime = Date.now();
    const products = await db.query.products.findMany({
      with: { company: true }
    });
    const responseTime = Date.now() - startTime;
    
    results.push({
      endpoint: 'products',
      method: 'SELECT',
      passed: true,
      responseTime,
      dataCount: products.length
    });
    
    console.log(colorize(`✅ Products: ${products.length} records (${responseTime}ms)`, 'green'));
    
    if (products.length > 0) {
      const sample = products[0];
      console.log(`   Sample: ${sample.name} (${sample.sku}) → Company: ${sample.company?.name}`);
    }
    
  } catch (error) {
    results.push({
      endpoint: 'products',
      method: 'SELECT',
      passed: false,
      responseTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log(colorize(`❌ Products query failed: ${error}`, 'red'));
  }
  
  // Test suppliers data
  try {
    const startTime = Date.now();
    const suppliers = await db.query.suppliers.findMany({
      with: { company: true }
    });
    const responseTime = Date.now() - startTime;
    
    results.push({
      endpoint: 'suppliers',
      method: 'SELECT',
      passed: true,
      responseTime,
      dataCount: suppliers.length
    });
    
    console.log(colorize(`✅ Suppliers: ${suppliers.length} records (${responseTime}ms)`, 'green'));
    
    if (suppliers.length > 0) {
      const sample = suppliers[0];
      console.log(`   Sample: ${sample.name} → Company: ${sample.company?.name}`);
    }
    
  } catch (error) {
    results.push({
      endpoint: 'suppliers',
      method: 'SELECT',
      passed: false,
      responseTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log(colorize(`❌ Suppliers query failed: ${error}`, 'red'));
  }
  
  // Test contract templates data
  try {
    const startTime = Date.now();
    const templates = await db.query.contractTemplates.findMany({
      with: { company: true }
    });
    const responseTime = Date.now() - startTime;
    
    results.push({
      endpoint: 'contract_templates',
      method: 'SELECT',
      passed: true,
      responseTime,
      dataCount: templates.length
    });
    
    console.log(colorize(`✅ Contract Templates: ${templates.length} records (${responseTime}ms)`, 'green'));
    
    if (templates.length > 0) {
      const sample = templates[0];
      console.log(`   Sample: ${sample.name} (${sample.type}) → Company: ${sample.company?.name}`);
    }
    
  } catch (error) {
    results.push({
      endpoint: 'contract_templates',
      method: 'SELECT',
      passed: false,
      responseTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log(colorize(`❌ Contract Templates query failed: ${error}`, 'red'));
  }
  
  // Test sales contracts data
  try {
    const startTime = Date.now();
    const salesContracts = await db.query.salesContracts.findMany({
      with: { 
        company: true,
        customer: true,
        items: {
          with: { product: true }
        }
      }
    });
    const responseTime = Date.now() - startTime;
    
    results.push({
      endpoint: 'sales_contracts',
      method: 'SELECT',
      passed: true,
      responseTime,
      dataCount: salesContracts.length
    });
    
    console.log(colorize(`✅ Sales Contracts: ${salesContracts.length} records (${responseTime}ms)`, 'green'));
    
    if (salesContracts.length > 0) {
      const sample = salesContracts[0];
      console.log(`   Sample: ${sample.number} → Customer: ${sample.customer?.name}, Items: ${sample.items?.length || 0}`);
    }
    
  } catch (error) {
    results.push({
      endpoint: 'sales_contracts',
      method: 'SELECT',
      passed: false,
      responseTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log(colorize(`❌ Sales Contracts query failed: ${error}`, 'red'));
  }
  
  return results;
}

async function testMultiTenantIsolation(): Promise<TestResult[]> {
  printSection('Multi-Tenant Isolation Tests');

  const results: TestResult[] = [];

  try {
    // Test company isolation
    const startTime = Date.now();

    // Get all companies
    const companies = await db.query.companies.findMany();

    if (companies.length > 1) {
      // Test that customers are properly isolated by company
      for (const company of companies.slice(0, 2)) { // Test first 2 companies
        const customers = await db.query.customers.findMany({
          where: (customers, { eq }) => eq(customers.company_id, company.id)
        });

        console.log(colorize(`✅ Company "${company.name}": ${customers.length} customers`, 'green'));

        // Verify no cross-company data leakage
        const crossCompanyCustomers = await db.query.customers.findMany({
          where: (customers, { ne }) => ne(customers.company_id, company.id)
        });

        if (crossCompanyCustomers.length > 0) {
          console.log(colorize(`   Other companies: ${crossCompanyCustomers.length} customers (properly isolated)`, 'cyan'));
        }
      }
    }

    const responseTime = Date.now() - startTime;

    results.push({
      endpoint: 'multi_tenant_isolation',
      method: 'SELECT',
      passed: true,
      responseTime,
      dataCount: companies.length
    });

  } catch (error) {
    results.push({
      endpoint: 'multi_tenant_isolation',
      method: 'SELECT',
      passed: false,
      responseTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log(colorize(`❌ Multi-tenant isolation test failed: ${error}`, 'red'));
  }

  return results;
}

async function testPerformanceBenchmarks(): Promise<TestResult[]> {
  printSection('Performance Benchmarks');

  const results: TestResult[] = [];

  // Test concurrent queries
  try {
    const startTime = Date.now();

    // Run multiple queries concurrently
    const promises = [
      db.query.companies.findMany(),
      db.query.customers.findMany({ with: { company: true } }),
      db.query.products.findMany({ with: { company: true } }),
      db.query.suppliers.findMany({ with: { company: true } }),
      db.query.contractTemplates.findMany({ with: { company: true } })
    ];

    const [companies, customers, products, suppliers, templates] = await Promise.all(promises);
    const responseTime = Date.now() - startTime;

    const totalRecords = companies.length + customers.length + products.length + suppliers.length + templates.length;

    results.push({
      endpoint: 'concurrent_queries',
      method: 'SELECT',
      passed: true,
      responseTime,
      dataCount: totalRecords
    });

    console.log(colorize(`✅ Concurrent queries: ${totalRecords} total records (${responseTime}ms)`, 'green'));
    console.log(`   Average: ${(responseTime / 5).toFixed(2)}ms per query`);
    console.log(`   Throughput: ${(totalRecords / responseTime * 1000).toFixed(2)} records/second`);

  } catch (error) {
    results.push({
      endpoint: 'concurrent_queries',
      method: 'SELECT',
      passed: false,
      responseTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log(colorize(`❌ Concurrent queries test failed: ${error}`, 'red'));
  }

  // Test complex join queries
  try {
    const startTime = Date.now();

    const complexQuery = await db.query.salesContracts.findMany({
      with: {
        company: true,
        customer: true,
        template: true,
        items: {
          with: {
            product: true
          }
        }
      }
    });

    const responseTime = Date.now() - startTime;

    results.push({
      endpoint: 'complex_joins',
      method: 'SELECT',
      passed: true,
      responseTime,
      dataCount: complexQuery.length
    });

    console.log(colorize(`✅ Complex joins: ${complexQuery.length} contracts with full relations (${responseTime}ms)`, 'green'));

    if (complexQuery.length > 0) {
      const sample = complexQuery[0];
      console.log(`   Sample: Contract ${sample.number} with ${sample.items?.length || 0} items`);
    }

  } catch (error) {
    results.push({
      endpoint: 'complex_joins',
      method: 'SELECT',
      passed: false,
      responseTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log(colorize(`❌ Complex joins test failed: ${error}`, 'red'));
  }

  return results;
}

async function generateTestReport(allResults: TestResult[]): Promise<void> {
  printSection('API Testing Report');

  const passed = allResults.filter(r => r.passed).length;
  const failed = allResults.filter(r => !r.passed).length;
  const avgResponseTime = allResults
    .filter(r => r.passed)
    .reduce((sum, r) => sum + r.responseTime, 0) / passed;

  console.log(colorize(`📊 API Testing Summary:`, 'bright'));
  console.log(`   Total tests: ${allResults.length}`);
  console.log(`   Passed: ${colorize(passed.toString(), 'green')}`);
  console.log(`   Failed: ${colorize(failed.toString(), failed > 0 ? 'red' : 'green')}`);
  console.log(`   Average response time: ${avgResponseTime.toFixed(2)}ms`);

  if (failed > 0) {
    console.log(colorize(`\n❌ Failed Tests:`, 'red'));
    allResults.filter(r => !r.passed).forEach(result => {
      console.log(`   • ${result.endpoint} (${result.method}): ${result.error}`);
    });
  }

  // Performance analysis
  const fastQueries = allResults.filter(r => r.passed && r.responseTime < 50);
  const slowQueries = allResults.filter(r => r.passed && r.responseTime > 100);

  console.log(colorize(`\n⚡ Performance Analysis:`, 'cyan'));
  console.log(`   Fast queries (<50ms): ${fastQueries.length}`);
  console.log(`   Slow queries (>100ms): ${slowQueries.length}`);

  if (slowQueries.length > 0) {
    console.log(colorize(`   Slow queries:`, 'yellow'));
    slowQueries.forEach(query => {
      console.log(`     • ${query.endpoint}: ${query.responseTime}ms`);
    });
  }

  // Save detailed report
  const reportPath = `api-test-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: allResults.length,
      passed,
      failed,
      averageResponseTime: avgResponseTime,
      overallSuccess: failed === 0
    },
    results: allResults
  };

  require('fs').writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(colorize(`\n📄 Detailed report saved: ${reportPath}`, 'cyan'));

  if (failed === 0) {
    console.log(colorize('\n🎉 All API tests passed!', 'green'));
    console.log(colorize('✅ PostgreSQL backend is fully functional', 'green'));
  } else {
    console.log(colorize('\n⚠️  Some API tests failed', 'red'));
    console.log(colorize('🔧 Please review and fix issues before proceeding', 'red'));
  }
}

async function main() {
  printHeader('Manufacturing ERP - API Endpoints Testing');

  try {
    // Test database connection first
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      console.error(colorize('❌ Database connection failed. Cannot proceed with API tests.', 'red'));
      process.exit(1);
    }

    // Run all tests
    const allResults: TestResult[] = [];

    allResults.push(...await testDataRetrieval());
    allResults.push(...await testMultiTenantIsolation());
    allResults.push(...await testPerformanceBenchmarks());

    // Generate comprehensive report
    await generateTestReport(allResults);

  } catch (error) {
    console.error(colorize(`\n❌ API testing failed: ${error}`, 'red'));
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}
