#!/usr/bin/env tsx

/**
 * Create Auth0 Test User Script
 * 
 * This script <NAME_EMAIL> user in Auth0
 * using a direct approach that should work with the current setup.
 */

import { db } from '../lib/db'
import { companies } from '../lib/schema-postgres'
import { eq } from 'drizzle-orm'

async function createAuth0UserManually() {
  console.log('🔧 AUTH0 USER CREATION GUIDE')
  console.log('============================')
  console.log('')
  console.log('To fix the multi-tenant isolation issue, we need to create the')
  console.log('<EMAIL> user in Auth0. Here\'s how:')
  console.log('')
  console.log('🔗 STEP 1: Go to Auth0 Dashboard')
  console.log('   URL: https://manage.auth0.com/dashboard/us/dev-tejx02ztaj7oufoc/')
  console.log('')
  console.log('👤 STEP 2: Navigate to User Management > Users')
  console.log('')
  console.log('➕ STEP 3: Click "Create User" button')
  console.log('')
  console.log('📝 STEP 4: Fill in the user details:')
  console.log('   Email: <EMAIL>')
  console.log('   Password: Ashatank@@2020')
  console.log('   Connection: Username-Password-Authentication')
  console.log('   Email Verified: ✅ Check this box')
  console.log('')
  console.log('💾 STEP 5: Click "Create" to save the user')
  console.log('')
  console.log('🔍 STEP 6: After creation, note the User ID')
  console.log('   It should look like: auth0|[random_string]')
  console.log('   Example: auth0|64f8a1b2c3d4e5f6g7h8i9j0')
  console.log('')
  console.log('🔄 STEP 7: Update the database with the correct Auth0 ID')
  console.log('   Run this script again after creating the user')
  console.log('')
}

async function updateDatabaseWithCorrectAuth0Id() {
  console.log('🔄 DATABASE UPDATE HELPER')
  console.log('=========================')
  console.log('')
  console.log('After creating the Auth0 user, you need to update the database.')
  console.log('The current database record has:')
  
  const currentCompany = await db.query.companies.findFirst({
    where: eq(companies.email, '<EMAIL>')
  })
  
  if (currentCompany) {
    console.log(`   Email: ${currentCompany.email}`)
    console.log(`   Current Auth0 ID: ${currentCompany.auth0_user_id}`)
    console.log('')
    console.log('🔧 To update with the correct Auth0 ID, run:')
    console.log('')
    console.log('   sqlite3 dev.db "UPDATE companies SET auth0_user_id = \'NEW_AUTH0_ID\' WHERE email = \'<EMAIL>\';"')
    console.log('')
    console.log('   Replace NEW_AUTH0_ID with the actual ID from Auth0 (e.g., auth0|64f8a1b2c3d4e5f6g7h8i9j0)')
    console.log('')
  } else {
    console.log('❌ No company record <NAME_EMAIL>')
  }
}

async function testCurrentSetup() {
  console.log('🧪 CURRENT SETUP TEST')
  console.log('=====================')
  
  const companies_list = await db.query.companies.findMany()
  
  console.log('📊 Current companies in database:')
  companies_list.forEach((company, index) => {
    const status = company.email === '<EMAIL>' ? '✅ Working' : 
                   company.email === '<EMAIL>' ? '❌ Needs Auth0 user' : 
                   '⚠️  Test user'
    
    console.log(`${index + 1}. ${company.name} (${company.email}) - ${status}`)
    console.log(`   Auth0 ID: ${company.auth0_user_id}`)
    console.log('')
  })
}

async function provideTestingInstructions() {
  console.log('🧪 TESTING INSTRUCTIONS AFTER SETUP')
  console.log('====================================')
  console.log('')
  console.log('After creating the Auth0 user and updating the database:')
  console.log('')
  console.log('1. 🔐 Open Chrome in incognito mode')
  console.log('2. 🌐 Navigate to: http://localhost:3000')
  console.log('3. 🔑 Click "Get Started" and <NAME_EMAIL>')
  console.log('4. 📊 Verify the dashboard shows empty data (0 customers, 0 products)')
  console.log('5. 🔄 Open new browser window and <NAME_EMAIL>')
  console.log('6. ✅ Verify original data is still there and isolated')
  console.log('')
  console.log('🎯 Expected Results:')
  console.log('   - <EMAIL> sees empty dashboard')
  console.log('   - <EMAIL> sees their existing data')
  console.log('   - No cross-contamination between users')
  console.log('')
}

async function main() {
  console.log('🚀 AUTH0 TEST USER CREATION HELPER')
  console.log('==================================')
  
  await createAuth0UserManually()
  await updateDatabaseWithCorrectAuth0Id()
  await testCurrentSetup()
  await provideTestingInstructions()
  
  console.log('🎉 Setup guide completed!')
  console.log('')
  console.log('📋 SUMMARY OF ACTIONS NEEDED:')
  console.log('1. Create <EMAIL> user in Auth0 Dashboard')
  console.log('2. Update database with correct Auth0 user ID')
  console.log('3. Test multi-tenant isolation in incognito mode')
  console.log('4. Report back with results')
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 Script failed:', error)
    process.exit(1)
  })
}
