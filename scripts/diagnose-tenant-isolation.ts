#!/usr/bin/env tsx

/**
 * Multi-Tenant Isolation Diagnostic Script
 * 
 * This script diagnoses the multi-tenant isolation failure by checking:
 * 1. Database state and company records
 * 2. Data distribution across companies
 * 3. API endpoint behavior
 * 4. Session and authentication state
 */

import { db } from '../lib/db'
import { companies, customers, products, suppliers, samples, salesContracts, purchaseContracts } from '../lib/schema-postgres'
import { eq } from 'drizzle-orm'

interface DiagnosticResults {
  companies: any[]
  dataDistribution: {
    customers: { total: number; withCompanyId: number; withoutCompanyId: number }
    products: { total: number; withCompanyId: number; withoutCompanyId: number }
    suppliers: { total: number; withCompanyId: number; withoutCompanyId: number }
    samples: { total: number; withCompanyId: number; withoutCompanyId: number }
    salesContracts: { total: number; withCompanyId: number; withoutCompanyId: number }
    purchaseContracts: { total: number; withCompanyId: number; withoutCompanyId: number }
  }
  orphanedData: {
    customers: any[]
    products: any[]
    suppliers: any[]
    samples: any[]
    salesContracts: any[]
    purchaseContracts: any[]
  }
}

async function runDiagnostics(): Promise<DiagnosticResults> {
  console.log('🔍 MULTI-TENANT ISOLATION DIAGNOSTIC')
  console.log('====================================')
  
  const results: DiagnosticResults = {
    companies: [],
    dataDistribution: {
      customers: { total: 0, withCompanyId: 0, withoutCompanyId: 0 },
      products: { total: 0, withCompanyId: 0, withoutCompanyId: 0 },
      suppliers: { total: 0, withCompanyId: 0, withoutCompanyId: 0 },
      samples: { total: 0, withCompanyId: 0, withoutCompanyId: 0 },
      salesContracts: { total: 0, withCompanyId: 0, withoutCompanyId: 0 },
      purchaseContracts: { total: 0, withCompanyId: 0, withoutCompanyId: 0 }
    },
    orphanedData: {
      customers: [],
      products: [],
      suppliers: [],
      samples: [],
      salesContracts: [],
      purchaseContracts: []
    }
  }

  try {
    // Step 1: Get all companies
    console.log('📊 Step 1: Analyzing companies...')
    results.companies = await db.query.companies.findMany()
    console.log(`Found ${results.companies.length} companies:`)
    results.companies.forEach(company => {
      console.log(`  - ${company.name} (${company.email}) - Auth0: ${company.auth0_user_id}`)
    })

    // Step 2: Analyze data distribution
    console.log('\n📊 Step 2: Analyzing data distribution...')
    
    // Customers
    const allCustomers = await db.query.customers.findMany()
    results.dataDistribution.customers.total = allCustomers.length
    results.dataDistribution.customers.withCompanyId = allCustomers.filter(c => c.company_id).length
    results.dataDistribution.customers.withoutCompanyId = allCustomers.filter(c => !c.company_id).length
    results.orphanedData.customers = allCustomers.filter(c => !c.company_id)
    
    // Products
    const allProducts = await db.query.products.findMany()
    results.dataDistribution.products.total = allProducts.length
    results.dataDistribution.products.withCompanyId = allProducts.filter(p => p.company_id).length
    results.dataDistribution.products.withoutCompanyId = allProducts.filter(p => !p.company_id).length
    results.orphanedData.products = allProducts.filter(p => !p.company_id)
    
    // Suppliers
    const allSuppliers = await db.query.suppliers.findMany()
    results.dataDistribution.suppliers.total = allSuppliers.length
    results.dataDistribution.suppliers.withCompanyId = allSuppliers.filter(s => s.company_id).length
    results.dataDistribution.suppliers.withoutCompanyId = allSuppliers.filter(s => !s.company_id).length
    results.orphanedData.suppliers = allSuppliers.filter(s => !s.company_id)
    
    // Samples
    const allSamples = await db.query.samples.findMany()
    results.dataDistribution.samples.total = allSamples.length
    results.dataDistribution.samples.withCompanyId = allSamples.filter(s => s.company_id).length
    results.dataDistribution.samples.withoutCompanyId = allSamples.filter(s => !s.company_id).length
    results.orphanedData.samples = allSamples.filter(s => !s.company_id)
    
    // Sales Contracts
    const allSalesContracts = await db.query.salesContracts.findMany()
    results.dataDistribution.salesContracts.total = allSalesContracts.length
    results.dataDistribution.salesContracts.withCompanyId = allSalesContracts.filter(c => c.company_id).length
    results.dataDistribution.salesContracts.withoutCompanyId = allSalesContracts.filter(c => !c.company_id).length
    results.orphanedData.salesContracts = allSalesContracts.filter(c => !c.company_id)
    
    // Purchase Contracts
    const allPurchaseContracts = await db.query.purchaseContracts.findMany()
    results.dataDistribution.purchaseContracts.total = allPurchaseContracts.length
    results.dataDistribution.purchaseContracts.withCompanyId = allPurchaseContracts.filter(c => c.company_id).length
    results.dataDistribution.purchaseContracts.withoutCompanyId = allPurchaseContracts.filter(c => !c.company_id).length
    results.orphanedData.purchaseContracts = allPurchaseContracts.filter(c => !c.company_id)

    console.log('\n📊 DATA DISTRIBUTION ANALYSIS:')
    console.log('==============================')
    Object.entries(results.dataDistribution).forEach(([table, stats]) => {
      const orphanedCount = stats.withoutCompanyId
      const status = orphanedCount > 0 ? '🚨 CRITICAL' : '✅ OK'
      console.log(`${status} ${table}: ${stats.total} total, ${stats.withCompanyId} with company_id, ${orphanedCount} orphaned`)
    })

    // Step 3: Check for orphaned data details
    if (Object.values(results.orphanedData).some(arr => arr.length > 0)) {
      console.log('\n🚨 ORPHANED DATA DETECTED:')
      console.log('==========================')
      Object.entries(results.orphanedData).forEach(([table, data]) => {
        if (data.length > 0) {
          console.log(`❌ ${table}: ${data.length} records without company_id`)
          data.forEach((record: any) => {
            console.log(`   - ID: ${record.id}, Name: ${record.name || record.number || 'N/A'}`)
          })
        }
      })
    }

  } catch (error) {
    console.error('💥 Diagnostic error:', error)
  }

  return results
}

function printDiagnosticSummary(results: DiagnosticResults) {
  console.log('\n🎯 DIAGNOSTIC SUMMARY')
  console.log('====================')
  
  const totalOrphaned = Object.values(results.dataDistribution)
    .reduce((sum, stats) => sum + stats.withoutCompanyId, 0)
  
  if (totalOrphaned > 0) {
    console.log('🚨 CRITICAL ISSUE DETECTED:')
    console.log(`   ${totalOrphaned} records exist without company_id association`)
    console.log('   This explains why new users can see existing data!')
    console.log('')
    console.log('🛠️  IMMEDIATE ACTIONS REQUIRED:')
    console.log('   1. Fix orphaned data by associating with correct companies')
    console.log('   2. Ensure all API endpoints properly handle missing tenant context')
    console.log('   3. Add fallback protection for orphaned data')
  } else {
    console.log('✅ All data properly associated with companies')
    console.log('   Issue may be in session management or frontend caching')
  }
  
  console.log(`\n📊 Companies: ${results.companies.length}`)
  console.log('📊 Data Summary:')
  Object.entries(results.dataDistribution).forEach(([table, stats]) => {
    console.log(`   ${table}: ${stats.total} total (${stats.withoutCompanyId} orphaned)`)
  })
}

// Run diagnostics if called directly
if (require.main === module) {
  runDiagnostics()
    .then(printDiagnosticSummary)
    .catch(error => {
      console.error('💥 Diagnostic script failed:', error)
      process.exit(1)
    })
}

export { runDiagnostics, printDiagnosticSummary }
