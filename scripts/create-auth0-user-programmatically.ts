#!/usr/bin/env tsx

/**
 * Create Auth0 User Programmatically
 * 
 * This script uses the Auth0 Management API to create the test user
 * using your existing Auth0 credentials.
 */

async function createAuth0UserWithAPI() {
  console.log('🔧 CREATING AUTH0 USER PROGRAMMATICALLY')
  console.log('======================================')
  
  const domain = process.env.AUTH0_ISSUER_BASE_URL?.replace('https://', '') || 'dev-tejx02ztaj7oufoc.us.auth0.com'
  const clientId = process.env.AUTH0_CLIENT_ID || 'To2oAQX05DtstsgKXdLvsUYAaRO23QCK'
  const clientSecret = process.env.AUTH0_CLIENT_SECRET || '****************************************************************'
  
  console.log(`🔗 Auth0 Domain: ${domain}`)
  console.log(`🔑 Client ID: ${clientId}`)
  console.log('')
  
  try {
    // Step 1: Get Management API token
    console.log('🎫 Getting Management API token...')
    
    const tokenResponse = await fetch(`https://${domain}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id: clientId,
        client_secret: clientSecret,
        audience: `https://${domain}/api/v2/`,
        grant_type: 'client_credentials'
      })
    })
    
    if (!tokenResponse.ok) {
      throw new Error(`Failed to get token: ${tokenResponse.status} ${tokenResponse.statusText}`)
    }
    
    const tokenData = await tokenResponse.json()
    const accessToken = tokenData.access_token
    
    console.log('✅ Got Management API token')
    
    // Step 2: Check if user already exists
    console.log('🔍 Checking if user already exists...')
    
    const existingUsersResponse = await fetch(`https://${domain}/api/v2/users-by-email?email=<EMAIL>`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (existingUsersResponse.ok) {
      const existingUsers = await existingUsersResponse.json()
      
      if (existingUsers.length > 0) {
        console.log('✅ User already exists!')
        console.log(`   User ID: ${existingUsers[0].user_id}`)
        console.log(`   Email: ${existingUsers[0].email}`)
        console.log(`   Created: ${existingUsers[0].created_at}`)
        
        // Update database with correct Auth0 ID
        await updateDatabaseWithAuth0Id(existingUsers[0].user_id)
        return existingUsers[0]
      }
    }
    
    // Step 3: Create new user
    console.log('👤 Creating new user...')
    
    const createUserResponse = await fetch(`https://${domain}/api/v2/users`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Ashatank@@2020',
        connection: 'Username-Password-Authentication',
        email_verified: true,
        name: 'Samovic Mental'
      })
    })
    
    if (!createUserResponse.ok) {
      const errorData = await createUserResponse.json()
      throw new Error(`Failed to create user: ${createUserResponse.status} - ${JSON.stringify(errorData)}`)
    }
    
    const newUser = await createUserResponse.json()
    
    console.log('🎉 User created successfully!')
    console.log(`   User ID: ${newUser.user_id}`)
    console.log(`   Email: ${newUser.email}`)
    console.log(`   Name: ${newUser.name}`)
    
    // Update database with correct Auth0 ID
    await updateDatabaseWithAuth0Id(newUser.user_id)
    
    return newUser
    
  } catch (error) {
    console.error('❌ Error creating Auth0 user:', error)
    
    // Provide manual instructions as fallback
    console.log('')
    console.log('🔧 FALLBACK: MANUAL USER CREATION')
    console.log('=================================')
    console.log('Since the API approach failed, please create the user manually:')
    console.log('')
    console.log('1. Go to: https://manage.auth0.com/dashboard/us/dev-tejx02ztaj7oufoc/')
    console.log('2. Navigate to: User Management > Users')
    console.log('3. Click: "Create User"')
    console.log('4. Fill in:')
    console.log('   - Email: <EMAIL>')
    console.log('   - Password: Ashatank@@2020')
    console.log('   - Connection: Username-Password-Authentication')
    console.log('   - Email Verified: ✅ Yes')
    console.log('5. Click: "Create"')
    console.log('6. Note the User ID and run: npm run update-auth0-id')
    
    throw error
  }
}

async function updateDatabaseWithAuth0Id(auth0UserId: string) {
  console.log(`🔄 Updating database with Auth0 ID: ${auth0UserId}`)
  
  try {
    const { db } = await import('../lib/db')
    const { companies } = await import('../lib/schema')
    const { eq } = await import('drizzle-orm')
    
    await db.update(companies)
      .set({ auth0_user_id: auth0UserId })
      .where(eq(companies.email, '<EMAIL>'))
    
    console.log('✅ Database updated successfully')
    
    // Verify the update
    const updatedCompany = await db.query.companies.findFirst({
      where: eq(companies.email, '<EMAIL>')
    })
    
    if (updatedCompany) {
      console.log(`✅ Verified: ${updatedCompany.email} -> ${updatedCompany.auth0_user_id}`)
    }
    
  } catch (error) {
    console.error('❌ Error updating database:', error)
    console.log('')
    console.log('🔧 Manual database update required:')
    console.log(`   sqlite3 dev.db "UPDATE companies SET auth0_user_id = '${auth0UserId}' WHERE email = '<EMAIL>';"`)
  }
}

async function testUserCreation() {
  console.log('🧪 TESTING USER CREATION RESULT')
  console.log('===============================')
  
  try {
    const { db } = await import('../lib/db')
    const { companies } = await import('../lib/schema')
    
    const companies_list = await db.query.companies.findMany()
    
    console.log('📊 Current companies in database:')
    companies_list.forEach((company, index) => {
      const status = company.email === '<EMAIL>' ? '✅ Ready' :
                     company.email === '<EMAIL>' ? '✅ Ready (if Auth0 user created)' :
                     '⚠️  Test user'
      
      console.log(`${index + 1}. ${company.email} - ${status}`)
      console.log(`   Auth0 ID: ${company.auth0_user_id}`)
      console.log('')
    })
    
    console.log('🎯 NEXT STEPS:')
    console.log('1. Test login with both users in incognito mode')
    console.log('2. Verify multi-tenant isolation')
    console.log('3. Run: npm run test:comprehensive-security')
    
  } catch (error) {
    console.error('❌ Error testing setup:', error)
  }
}

async function main() {
  try {
    console.log('🚀 AUTH0 USER CREATION SCRIPT')
    console.log('=============================')
    console.log('')
    
    await createAuth0UserWithAPI()
    await testUserCreation()
    
    console.log('')
    console.log('🎉 Auth0 user creation completed!')
    console.log('✅ Ready for multi-tenant isolation testing')
    
  } catch (error) {
    console.error('💥 Script failed:', error)
    console.log('')
    console.log('📋 Please follow the manual instructions provided above')
  }
}

if (require.main === module) {
  main()
}
