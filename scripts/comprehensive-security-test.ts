#!/usr/bin/env tsx

/**
 * Comprehensive Multi-Tenant Security Test Suite
 * 
 * This script tests ALL secured API endpoints to ensure complete
 * multi-tenant isolation across the entire Manufacturing ERP system.
 * 
 * 🛡️ COMPREHENSIVE SECURITY TESTING 🛡️
 * Tests every API endpoint for proper authentication and tenant isolation.
 * 
 * Usage: npm run test:comprehensive-security
 */

interface EndpointTest {
  endpoint: string
  method: string
  description: string
  expectsAuth: boolean
}

interface SecurityTestResults {
  totalEndpoints: number
  securedEndpoints: number
  unsecuredEndpoints: number
  authenticationTests: { passed: number; failed: number }
  endpointResults: Array<{
    endpoint: string
    method: string
    description: string
    authRequired: boolean
    status: 'SECURE' | 'VULNERABLE' | 'ERROR'
    details: string
  }>
}

// Comprehensive list of all ERP API endpoints that should be secured
const ENDPOINTS_TO_TEST: EndpointTest[] = [
  // Core ERP Modules
  { endpoint: '/api/customers', method: 'GET', description: 'Customer list', expectsAuth: true },
  { endpoint: '/api/products', method: 'GET', description: 'Product list', expectsAuth: true },
  { endpoint: '/api/suppliers', method: 'GET', description: 'Supplier list', expectsAuth: true },
  { endpoint: '/api/samples', method: 'GET', description: 'Sample list', expectsAuth: true },
  
  // Contract Management
  { endpoint: '/api/contracts/sales', method: 'GET', description: 'Sales contracts', expectsAuth: true },
  { endpoint: '/api/contracts/purchase', method: 'GET', description: 'Purchase contracts', expectsAuth: true },
  { endpoint: '/api/contracts/templates', method: 'GET', description: 'Contract templates', expectsAuth: true },
  
  // Finance & Invoicing
  { endpoint: '/api/finance/ar', method: 'GET', description: 'Accounts receivable', expectsAuth: true },
  { endpoint: '/api/finance/ap', method: 'GET', description: 'Accounts payable', expectsAuth: true },
  
  // Inventory Management
  { endpoint: '/api/inventory/lots', method: 'GET', description: 'Stock lots', expectsAuth: true },
  { endpoint: '/api/inventory/txns', method: 'GET', description: 'Stock transactions', expectsAuth: true },
  
  // Production Management
  { endpoint: '/api/production/work-orders', method: 'GET', description: 'Work orders', expectsAuth: true },
  
  // Quality Control
  { endpoint: '/api/quality/inspections', method: 'GET', description: 'Quality inspections', expectsAuth: true },
  
  // Export & Declarations
  { endpoint: '/api/export/declarations', method: 'GET', description: 'Export declarations', expectsAuth: true },
  
  // System Endpoints (should not require auth)
  { endpoint: '/api/health', method: 'GET', description: 'Health check', expectsAuth: false },
]

async function testEndpointAuthentication(endpoint: EndpointTest): Promise<{
  endpoint: string
  method: string
  description: string
  authRequired: boolean
  status: 'SECURE' | 'VULNERABLE' | 'ERROR'
  details: string
}> {
  try {
    console.log(`🧪 Testing ${endpoint.method} ${endpoint.endpoint}`)
    
    // Make unauthenticated request using curl
    const { spawn } = require('child_process')
    
    const curlResult = await new Promise<{ stdout: string; stderr: string; code: number }>((resolve) => {
      const curl = spawn('curl', [
        '-X', endpoint.method,
        '-s', // Silent mode
        '-w', '%{http_code}', // Write HTTP status code
        `http://localhost:3000${endpoint.endpoint}`
      ])
      
      let stdout = ''
      let stderr = ''
      
      curl.stdout.on('data', (data: Buffer) => {
        stdout += data.toString()
      })
      
      curl.stderr.on('data', (data: Buffer) => {
        stderr += data.toString()
      })
      
      curl.on('close', (code: number) => {
        resolve({ stdout, stderr, code })
      })
    })
    
    // Extract HTTP status code (last 3 characters)
    const httpStatus = curlResult.stdout.slice(-3)
    const responseBody = curlResult.stdout.slice(0, -3)
    
    if (endpoint.expectsAuth) {
      // Should return 401 Unauthorized for secured endpoints
      if (httpStatus === '401') {
        return {
          endpoint: endpoint.endpoint,
          method: endpoint.method,
          description: endpoint.description,
          authRequired: true,
          status: 'SECURE',
          details: 'Properly rejects unauthenticated requests (401)'
        }
      } else if (httpStatus === '200') {
        return {
          endpoint: endpoint.endpoint,
          method: endpoint.method,
          description: endpoint.description,
          authRequired: false,
          status: 'VULNERABLE',
          details: `CRITICAL: Returns data without authentication (${httpStatus})`
        }
      } else {
        return {
          endpoint: endpoint.endpoint,
          method: endpoint.method,
          description: endpoint.description,
          authRequired: false,
          status: 'ERROR',
          details: `Unexpected response: ${httpStatus} - ${responseBody.substring(0, 100)}`
        }
      }
    } else {
      // Should return 200 OK for public endpoints
      if (httpStatus === '200') {
        return {
          endpoint: endpoint.endpoint,
          method: endpoint.method,
          description: endpoint.description,
          authRequired: false,
          status: 'SECURE',
          details: 'Public endpoint working correctly (200)'
        }
      } else {
        return {
          endpoint: endpoint.endpoint,
          method: endpoint.method,
          description: endpoint.description,
          authRequired: false,
          status: 'ERROR',
          details: `Public endpoint error: ${httpStatus}`
        }
      }
    }
    
  } catch (error) {
    return {
      endpoint: endpoint.endpoint,
      method: endpoint.method,
      description: endpoint.description,
      authRequired: false,
      status: 'ERROR',
      details: `Test error: ${error}`
    }
  }
}

async function runComprehensiveSecurityTests(): Promise<SecurityTestResults> {
  console.log('🛡️  COMPREHENSIVE MULTI-TENANT SECURITY TEST SUITE')
  console.log('==================================================')
  console.log(`🔍 Testing ${ENDPOINTS_TO_TEST.length} API endpoints for security compliance`)
  console.log('')
  
  const results: SecurityTestResults = {
    totalEndpoints: ENDPOINTS_TO_TEST.length,
    securedEndpoints: 0,
    unsecuredEndpoints: 0,
    authenticationTests: { passed: 0, failed: 0 },
    endpointResults: []
  }
  
  // Test each endpoint
  for (const endpoint of ENDPOINTS_TO_TEST) {
    const testResult = await testEndpointAuthentication(endpoint)
    results.endpointResults.push(testResult)
    
    if (testResult.status === 'SECURE') {
      if (testResult.authRequired) {
        results.securedEndpoints++
      }
      results.authenticationTests.passed++
    } else if (testResult.status === 'VULNERABLE') {
      results.unsecuredEndpoints++
      results.authenticationTests.failed++
    } else {
      results.authenticationTests.failed++
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  return results
}

function printComprehensiveResults(results: SecurityTestResults) {
  console.log('')
  console.log('🛡️  COMPREHENSIVE SECURITY TEST RESULTS')
  console.log('========================================')
  console.log(`📊 Total Endpoints Tested: ${results.totalEndpoints}`)
  console.log(`🔒 Secured Endpoints: ${results.securedEndpoints}`)
  console.log(`⚠️  Unsecured Endpoints: ${results.unsecuredEndpoints}`)
  console.log(`✅ Authentication Tests Passed: ${results.authenticationTests.passed}`)
  console.log(`❌ Authentication Tests Failed: ${results.authenticationTests.failed}`)
  
  console.log('')
  console.log('📋 DETAILED ENDPOINT RESULTS:')
  console.log('==============================')
  
  results.endpointResults.forEach(result => {
    const statusIcon = result.status === 'SECURE' ? '✅' : result.status === 'VULNERABLE' ? '🚨' : '⚠️'
    const authIcon = result.authRequired ? '🔒' : '🌐'
    
    console.log(`${statusIcon} ${authIcon} ${result.method} ${result.endpoint}`)
    console.log(`   ${result.description}: ${result.details}`)
  })
  
  console.log('')
  
  if (results.unsecuredEndpoints === 0 && results.authenticationTests.failed === 0) {
    console.log('🎉 SECURITY STATUS: FULLY SECURE')
    console.log('✅ All ERP endpoints properly implement multi-tenant security!')
    console.log('✅ Authentication required for all sensitive endpoints')
    console.log('✅ System ready for production deployment')
  } else {
    console.log('🚨 SECURITY STATUS: VULNERABILITIES DETECTED')
    console.log(`❌ ${results.unsecuredEndpoints} endpoints lack proper security`)
    console.log(`❌ ${results.authenticationTests.failed} authentication tests failed`)
    console.log('⚠️  DO NOT deploy to production until all vulnerabilities are fixed')
  }
  
  console.log('')
  console.log('📚 Next Steps:')
  if (results.unsecuredEndpoints > 0) {
    console.log('   1. Fix unsecured endpoints by adding withTenantAuth middleware')
    console.log('   2. Re-run comprehensive security tests')
    console.log('   3. Verify all tests pass before deployment')
  } else {
    console.log('   1. Perform user acceptance testing')
    console.log('   2. Deploy to production with confidence')
    console.log('   3. Monitor security in production environment')
  }
}

// Run comprehensive security tests if called directly
if (require.main === module) {
  runComprehensiveSecurityTests()
    .then(printComprehensiveResults)
    .catch(error => {
      console.error('💥 Comprehensive security testing failed:', error)
      process.exit(1)
    })
}

export { runComprehensiveSecurityTests, printComprehensiveResults }
