#!/usr/bin/env tsx

/**
 * Manufacturing ERP - Database Connection Test Script
 * 
 * Tests both SQLite and PostgreSQL connections during migration
 * Validates configuration and provides detailed connection information
 */

import { 
  testDatabaseConnections, 
  getDatabaseInfo, 
  getMigrationConfig,
  getDualDatabaseConnections,
  createSQLiteConnection,
  createPostgreSQLConnection
} from '../lib/db-config';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title: string) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize(`  ${title}`, 'bright'));
  console.log(colorize('='.repeat(60), 'cyan'));
}

function printSection(title: string) {
  console.log('\n' + colorize(`📋 ${title}`, 'blue'));
  console.log(colorize('-'.repeat(40), 'blue'));
}

async function testBasicConnections() {
  printSection('Basic Connection Tests');
  
  const results = await testDatabaseConnections();
  
  // Display results
  console.log('\n🗄️ SQLite Connection:');
  if (results.sqlite.connected) {
    console.log(colorize('  ✅ Connected successfully', 'green'));
  } else {
    console.log(colorize('  ❌ Connection failed', 'red'));
    if (results.sqlite.error) {
      console.log(colorize(`  Error: ${results.sqlite.error}`, 'red'));
    }
  }
  
  console.log('\n🐘 PostgreSQL Connection:');
  if (results.postgresql.connected) {
    console.log(colorize('  ✅ Connected successfully', 'green'));
  } else {
    console.log(colorize('  ❌ Connection failed', 'red'));
    if (results.postgresql.error) {
      console.log(colorize(`  Error: ${results.postgresql.error}`, 'red'));
    }
  }
  
  return results;
}

async function testDatabaseQueries() {
  printSection('Database Query Tests');
  
  const migrationConfig = getMigrationConfig();
  
  // Test SQLite queries
  if (migrationConfig.useSQLite) {
    try {
      console.log('\n🗄️ Testing SQLite queries...');
      const sqliteDb = createSQLiteConnection();
      
      // Test basic query using raw SQL
      const Database = require('better-sqlite3');
      const config = getDatabaseInfo();
      const sqlite = new Database(config.config.sqlite.path);

      const sqliteVersion = sqlite.prepare('SELECT sqlite_version() as version').get();
      console.log(colorize(`  ✅ SQLite Version: ${sqliteVersion.version}`, 'green'));

      // Test schema query
      const tables = sqlite.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
      `).all();
      console.log(colorize(`  ✅ Tables found: ${tables.length}`, 'green'));
      
    } catch (error) {
      console.log(colorize(`  ❌ SQLite query failed: ${error}`, 'red'));
    }
  }
  
  // Test PostgreSQL queries
  if (migrationConfig.usePostgreSQL || migrationConfig.enableDualDatabase) {
    try {
      console.log('\n🐘 Testing PostgreSQL queries...');
      const postgresDb = createPostgreSQLConnection();
      
      // Test basic query using postgres client directly
      const postgres = require('postgres');
      const config = getDatabaseInfo();
      const pgUrl = config.config.postgresql.url.replace(':***@', ':SecureERP2024!@');
      const sql = postgres(pgUrl);

      const pgVersion = await sql`SELECT version()`;
      const versionString = pgVersion[0]?.version?.split(' ').slice(0, 2).join(' ');
      console.log(colorize(`  ✅ PostgreSQL Version: ${versionString}`, 'green'));

      // Test extensions
      const extensions = await sql`
        SELECT extname FROM pg_extension
        WHERE extname IN ('uuid-ossp', 'pg_stat_statements', 'pg_trgm', 'btree_gin')
        ORDER BY extname
      `;
      console.log(colorize(`  ✅ Extensions installed: ${extensions.length}`, 'green'));

      // Test custom functions
      const functions = await sql`
        SELECT proname FROM pg_proc
        WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'erp_functions')
        ORDER BY proname
      `;
      console.log(colorize(`  ✅ Custom functions: ${functions.length}`, 'green'));

      await sql.end();
      
    } catch (error) {
      console.log(colorize(`  ❌ PostgreSQL query failed: ${error}`, 'red'));
    }
  }
}

async function displayConfiguration() {
  printSection('Configuration Information');
  
  const dbInfo = getDatabaseInfo();
  
  console.log('\n🔧 Migration Configuration:');
  console.log(`  Phase: ${colorize(dbInfo.migration.migrationPhase, 'yellow')}`);
  console.log(`  Primary Database: ${colorize(dbInfo.primary, 'yellow')}`);
  console.log(`  Use PostgreSQL: ${dbInfo.migration.usePostgreSQL ? colorize('Yes', 'green') : colorize('No', 'red')}`);
  console.log(`  Use SQLite: ${dbInfo.migration.useSQLite ? colorize('Yes', 'green') : colorize('No', 'red')}`);
  console.log(`  Dual Database: ${dbInfo.migration.enableDualDatabase ? colorize('Enabled', 'green') : colorize('Disabled', 'red')}`);
  console.log(`  Debug Mode: ${dbInfo.migration.migrationDebug ? colorize('On', 'green') : colorize('Off', 'red')}`);
  
  console.log('\n🗄️ SQLite Configuration:');
  console.log(`  Path: ${colorize(dbInfo.config.sqlite.path, 'cyan')}`);
  console.log(`  Enabled: ${dbInfo.config.sqlite.enabled ? colorize('Yes', 'green') : colorize('No', 'red')}`);
  
  console.log('\n🐘 PostgreSQL Configuration:');
  console.log(`  URL: ${colorize(dbInfo.config.postgresql.url, 'cyan')}`);
  console.log(`  Enabled: ${dbInfo.config.postgresql.enabled ? colorize('Yes', 'green') : colorize('No', 'red')}`);
}

async function testDualDatabaseMode() {
  printSection('Dual Database Mode Test');
  
  const migrationConfig = getMigrationConfig();
  
  if (!migrationConfig.enableDualDatabase) {
    console.log(colorize('  ⚠️ Dual database mode is disabled', 'yellow'));
    console.log('  To enable: Set ENABLE_DUAL_DATABASE=true in .env.local');
    return;
  }
  
  try {
    console.log('\n🔄 Testing dual database connections...');
    const connections = getDualDatabaseConnections();
    
    // Test both connections with direct clients
    const Database = require('better-sqlite3');
    const postgres = require('postgres');

    const config = getDatabaseInfo();
    const sqlite = new Database(config.config.sqlite.path);
    sqlite.prepare('SELECT 1').get();
    console.log(colorize('  ✅ SQLite connection in dual mode: OK', 'green'));

    const pgUrl = config.config.postgresql.url.replace(':***@', ':SecureERP2024!@');
    const sql = postgres(pgUrl);
    await sql`SELECT 1`;
    console.log(colorize('  ✅ PostgreSQL connection in dual mode: OK', 'green'));
    await sql.end();
    
    console.log(colorize('  🎉 Dual database mode is working correctly!', 'green'));
    
  } catch (error) {
    console.log(colorize(`  ❌ Dual database mode failed: ${error}`, 'red'));
  }
}

async function performanceTest() {
  printSection('Performance Test');
  
  const migrationConfig = getMigrationConfig();
  
  // SQLite performance test
  if (migrationConfig.useSQLite) {
    try {
      console.log('\n🗄️ SQLite Performance Test...');
      const sqliteDb = createSQLiteConnection();
      
      const Database = require('better-sqlite3');
      const config = getDatabaseInfo();
      const sqlite = new Database(config.config.sqlite.path);
      const stmt = sqlite.prepare('SELECT 1');

      const start = Date.now();
      for (let i = 0; i < 100; i++) {
        stmt.get();
      }
      const sqliteTime = Date.now() - start;
      
      console.log(colorize(`  ✅ 100 queries in ${sqliteTime}ms (${(sqliteTime/100).toFixed(2)}ms avg)`, 'green'));
      
    } catch (error) {
      console.log(colorize(`  ❌ SQLite performance test failed: ${error}`, 'red'));
    }
  }
  
  // PostgreSQL performance test
  if (migrationConfig.usePostgreSQL || migrationConfig.enableDualDatabase) {
    try {
      console.log('\n🐘 PostgreSQL Performance Test...');
      const postgresDb = createPostgreSQLConnection();
      
      const postgres = require('postgres');
      const config = getDatabaseInfo();
      const pgUrl = config.config.postgresql.url.replace(':***@', ':SecureERP2024!@');
      const sql = postgres(pgUrl);

      const start = Date.now();
      for (let i = 0; i < 100; i++) {
        await sql`SELECT 1`;
      }
      const postgresTime = Date.now() - start;
      await sql.end();
      
      console.log(colorize(`  ✅ 100 queries in ${postgresTime}ms (${(postgresTime/100).toFixed(2)}ms avg)`, 'green'));
      
    } catch (error) {
      console.log(colorize(`  ❌ PostgreSQL performance test failed: ${error}`, 'red'));
    }
  }
}

async function main() {
  printHeader('Manufacturing ERP - Database Connection Test');
  
  try {
    // Display configuration
    await displayConfiguration();
    
    // Test basic connections
    const connectionResults = await testBasicConnections();
    
    // Test database queries
    await testDatabaseQueries();
    
    // Test dual database mode
    await testDualDatabaseMode();
    
    // Performance test
    await performanceTest();
    
    // Summary
    printSection('Test Summary');
    
    const sqliteOk = connectionResults.sqlite.connected;
    const postgresOk = connectionResults.postgresql.connected;
    
    if (sqliteOk && postgresOk) {
      console.log(colorize('\n🎉 All database connections are working correctly!', 'green'));
      console.log(colorize('✅ Ready for PostgreSQL migration', 'green'));
    } else if (sqliteOk) {
      console.log(colorize('\n⚠️ SQLite is working, PostgreSQL needs attention', 'yellow'));
      console.log(colorize('🔧 Fix PostgreSQL connection before proceeding', 'yellow'));
    } else if (postgresOk) {
      console.log(colorize('\n⚠️ PostgreSQL is working, SQLite needs attention', 'yellow'));
      console.log(colorize('🔧 Fix SQLite connection for migration compatibility', 'yellow'));
    } else {
      console.log(colorize('\n❌ Both database connections failed', 'red'));
      console.log(colorize('🔧 Check configuration and database services', 'red'));
    }
    
  } catch (error) {
    console.error(colorize(`\n❌ Test failed: ${error}`, 'red'));
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}
