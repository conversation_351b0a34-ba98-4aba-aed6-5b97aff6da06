#!/usr/bin/env tsx

/**
 * Production Database Migration Script
 * 
 * This script runs the Drizzle migrations against the production Supabase database
 * to create all required tables and schema.
 */

import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";
import * as schema from "../lib/schema-postgres";

async function runProductionMigration() {
  console.log('🚀 PRODUCTION DATABASE MIGRATION');
  console.log('=================================');
  
  // Use the production DATABASE_URL from environment
  const connectionUrl = process.env.DATABASE_URL;
  
  if (!connectionUrl) {
    console.error('❌ DATABASE_URL environment variable is required');
    process.exit(1);
  }
  
  console.log('🔗 Connecting to production database...');
  console.log(`📍 URL: ${connectionUrl.replace(/:[^:@]*@/, ':***@')}`);
  
  try {
    // Create PostgreSQL connection
    const client = postgres(connectionUrl, {
      max: 1, // Single connection for migration
      idle_timeout: 20,
      connect_timeout: 10,
    });
    
    // Create Drizzle instance
    const db = drizzle(client, { schema });
    
    console.log('✅ Connected to database');
    console.log('📦 Running migrations...');
    
    // Run migrations
    await migrate(db, { migrationsFolder: "./drizzle" });
    
    console.log('✅ Migrations completed successfully!');
    
    // Test the migration by checking if companies table exists
    console.log('🔍 Verifying migration...');
    
    const result = await client`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'companies'
    `;
    
    if (result.length > 0) {
      console.log('✅ Companies table created successfully');
    } else {
      console.log('❌ Companies table not found');
    }
    
    // List all created tables
    const tables = await client`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log(`📊 Created ${tables.length} tables:`);
    tables.forEach((table, index) => {
      console.log(`  ${index + 1}. ${table.table_name}`);
    });
    
    await client.end();
    console.log('🎉 Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runProductionMigration().catch(console.error);
