#!/usr/bin/env tsx

/**
 * Manufacturing ERP - SQLite to PostgreSQL Data Migration Script
 * 
 * Comprehensive migration script that transfers all data from SQLite to PostgreSQL
 * with proper data type conversion, validation, and error handling
 */

import Database from "better-sqlite3";
import postgres from "postgres";
import path from "path";
import fs from "fs";

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title: string) {
  console.log('\n' + colorize('='.repeat(70), 'cyan'));
  console.log(colorize(`  ${title}`, 'bright'));
  console.log(colorize('='.repeat(70), 'cyan'));
}

function printSection(title: string) {
  console.log('\n' + colorize(`📊 ${title}`, 'blue'));
  console.log(colorize('-'.repeat(50), 'blue'));
}

// Migration statistics
interface MigrationStats {
  tablesProcessed: number;
  totalRecords: number;
  successfulMigrations: number;
  errors: string[];
  tableStats: Record<string, { source: number; migrated: number; errors: number }>;
}

// Database connections
let sqliteDb: Database.Database;
let postgresDb: postgres.Sql;

// Tables to migrate in dependency order (respecting foreign keys)
const MIGRATION_ORDER = [
  'companies',
  'customers',
  'suppliers', 
  'products',
  'samples',
  'contract_templates',
  'sales_contracts',
  'purchase_contracts',
  'sales_contract_items',
  'purchase_contract_items',
  'work_orders',
  'stock_lots',
  'stock_txns',
  'declarations',
  'declaration_items',
  'ar_invoices',
  'ap_invoices',
  'quality_inspections',
  'quality_defects',
  'quality_standards',
  'quality_certificates',
  'inspection_results'
];

async function initializeConnections() {
  printSection('Database Connections');
  
  // SQLite connection
  const sqlitePath = path.join(process.cwd(), "dev.db");
  if (!fs.existsSync(sqlitePath)) {
    throw new Error(`SQLite database not found at: ${sqlitePath}`);
  }
  
  sqliteDb = new Database(sqlitePath, { readonly: true });
  console.log(colorize(`✅ SQLite connected: ${sqlitePath}`, 'green'));
  
  // PostgreSQL connection
  const postgresUrl = process.env.DATABASE_URL_POSTGRESQL || 
                     process.env.POSTGRES_LOCAL_URL || 
                     process.env.DATABASE_URL ||
                     'postgresql://erp_user:SecureERP2024!@localhost:5432/manufacturing_erp';
  
  postgresDb = postgres(postgresUrl, {
    max: 5, // Limited connections for migration
    idle_timeout: 20,
    connect_timeout: 10,
  });
  
  // Test PostgreSQL connection
  await postgresDb`SELECT 1`;
  console.log(colorize(`✅ PostgreSQL connected: ${postgresUrl.replace(/:[^:@]*@/, ':***@')}`, 'green'));
}

async function analyzeSourceData(): Promise<Record<string, number>> {
  printSection('Source Data Analysis');
  
  const tableCounts: Record<string, number> = {};
  
  for (const tableName of MIGRATION_ORDER) {
    try {
      const result = sqliteDb.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get() as { count: number };
      tableCounts[tableName] = result.count;
      
      if (result.count > 0) {
        console.log(colorize(`📋 ${tableName}: ${result.count} records`, 'yellow'));
      } else {
        console.log(colorize(`📋 ${tableName}: empty`, 'cyan'));
      }
    } catch (error) {
      console.log(colorize(`⚠️  ${tableName}: table not found (skipping)`, 'yellow'));
      tableCounts[tableName] = 0;
    }
  }
  
  const totalRecords = Object.values(tableCounts).reduce((sum, count) => sum + count, 0);
  console.log(colorize(`\n📊 Total records to migrate: ${totalRecords}`, 'bright'));
  
  return tableCounts;
}

function convertSQLiteToPostgres(value: any, columnName: string): any {
  if (value === null || value === undefined) {
    return null;
  }
  
  // Handle timestamp columns
  if (columnName.includes('_at') || columnName.includes('date')) {
    if (typeof value === 'number') {
      // SQLite timestamp (Unix timestamp in milliseconds)
      return new Date(value);
    } else if (typeof value === 'string') {
      // ISO string or date string
      return new Date(value);
    }
  }
  
  // Handle boolean-like text fields
  if (typeof value === 'string' && (value === 'true' || value === 'false')) {
    return value; // Keep as text in PostgreSQL
  }
  
  return value;
}

async function migrateTable(tableName: string, stats: MigrationStats): Promise<void> {
  console.log(colorize(`\n🔄 Migrating table: ${tableName}`, 'blue'));
  
  try {
    // Get source data
    const sourceData = sqliteDb.prepare(`SELECT * FROM ${tableName}`).all();
    
    if (sourceData.length === 0) {
      console.log(colorize(`  ⏭️  No data to migrate`, 'yellow'));
      stats.tableStats[tableName] = { source: 0, migrated: 0, errors: 0 };
      return;
    }
    
    console.log(colorize(`  📥 Found ${sourceData.length} records`, 'cyan'));
    
    // Clear existing data in PostgreSQL table
    await postgresDb`DELETE FROM ${postgresDb(tableName)}`;
    console.log(colorize(`  🧹 Cleared existing PostgreSQL data`, 'yellow'));
    
    let migratedCount = 0;
    let errorCount = 0;
    
    // Migrate records in batches
    const batchSize = 100;
    for (let i = 0; i < sourceData.length; i += batchSize) {
      const batch = sourceData.slice(i, i + batchSize);
      
      for (const record of batch) {
        try {
          // Convert data types
          const convertedRecord: Record<string, any> = {};
          for (const [key, value] of Object.entries(record)) {
            convertedRecord[key] = convertSQLiteToPostgres(value, key);
          }
          
          // Build dynamic insert query
          const columns = Object.keys(convertedRecord);
          const values = Object.values(convertedRecord);
          
          // Create parameterized query
          const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
          const query = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;
          
          await postgresDb.unsafe(query, values);
          migratedCount++;
          
        } catch (error) {
          errorCount++;
          const errorMsg = `${tableName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          stats.errors.push(errorMsg);
          console.log(colorize(`    ❌ Error migrating record: ${errorMsg}`, 'red'));
        }
      }
      
      // Progress indicator
      const progress = Math.min(i + batchSize, sourceData.length);
      console.log(colorize(`  📊 Progress: ${progress}/${sourceData.length} (${Math.round(progress/sourceData.length*100)}%)`, 'cyan'));
    }
    
    console.log(colorize(`  ✅ Migrated: ${migratedCount}/${sourceData.length} records`, 'green'));
    if (errorCount > 0) {
      console.log(colorize(`  ⚠️  Errors: ${errorCount}`, 'yellow'));
    }
    
    stats.tableStats[tableName] = { 
      source: sourceData.length, 
      migrated: migratedCount, 
      errors: errorCount 
    };
    stats.successfulMigrations += migratedCount;
    
  } catch (error) {
    const errorMsg = `Failed to migrate table ${tableName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
    stats.errors.push(errorMsg);
    console.log(colorize(`  ❌ ${errorMsg}`, 'red'));
    
    stats.tableStats[tableName] = { source: 0, migrated: 0, errors: 1 };
  }
}

async function validateMigration(tableCounts: Record<string, number>): Promise<boolean> {
  printSection('Migration Validation');

  let allValid = true;

  for (const tableName of MIGRATION_ORDER) {
    if (tableCounts[tableName] === 0) continue;

    try {
      const result = await postgresDb`SELECT COUNT(*) as count FROM ${postgresDb(tableName)}`;
      const postgresCount = parseInt(result[0].count);
      const sqliteCount = tableCounts[tableName];

      if (postgresCount === sqliteCount) {
        console.log(colorize(`✅ ${tableName}: ${postgresCount}/${sqliteCount} records`, 'green'));
      } else {
        console.log(colorize(`❌ ${tableName}: ${postgresCount}/${sqliteCount} records (MISMATCH)`, 'red'));
        allValid = false;
      }
    } catch (error) {
      console.log(colorize(`❌ ${tableName}: Validation failed - ${error}`, 'red'));
      allValid = false;
    }
  }

  return allValid;
}

async function generateMigrationReport(stats: MigrationStats, tableCounts: Record<string, number>): Promise<void> {
  printSection('Migration Report');

  const reportPath = `migration-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;

  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      tablesProcessed: stats.tablesProcessed,
      totalSourceRecords: stats.totalRecords,
      successfulMigrations: stats.successfulMigrations,
      totalErrors: stats.errors.length,
      migrationSuccess: stats.errors.length === 0
    },
    tableDetails: stats.tableStats,
    errors: stats.errors,
    sourceCounts: tableCounts
  };

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  console.log(colorize(`📊 Migration Summary:`, 'bright'));
  console.log(`   Tables processed: ${stats.tablesProcessed}`);
  console.log(`   Total source records: ${stats.totalRecords}`);
  console.log(`   Successfully migrated: ${stats.successfulMigrations}`);
  console.log(`   Errors: ${stats.errors.length}`);
  console.log(`   Success rate: ${((stats.successfulMigrations / stats.totalRecords) * 100).toFixed(2)}%`);

  if (stats.errors.length > 0) {
    console.log(colorize(`\n⚠️  Errors encountered:`, 'yellow'));
    stats.errors.slice(0, 10).forEach(error => {
      console.log(colorize(`   • ${error}`, 'red'));
    });
    if (stats.errors.length > 10) {
      console.log(colorize(`   ... and ${stats.errors.length - 10} more errors`, 'red'));
    }
  }

  console.log(colorize(`\n📄 Detailed report saved: ${reportPath}`, 'cyan'));
}

async function cleanupConnections(): Promise<void> {
  try {
    if (sqliteDb) {
      sqliteDb.close();
      console.log(colorize('🔌 SQLite connection closed', 'cyan'));
    }

    if (postgresDb) {
      await postgresDb.end();
      console.log(colorize('🔌 PostgreSQL connection closed', 'cyan'));
    }
  } catch (error) {
    console.log(colorize(`⚠️  Error closing connections: ${error}`, 'yellow'));
  }
}

async function main() {
  printHeader('Manufacturing ERP - SQLite to PostgreSQL Data Migration');

  const stats: MigrationStats = {
    tablesProcessed: 0,
    totalRecords: 0,
    successfulMigrations: 0,
    errors: [],
    tableStats: {}
  };

  try {
    // Initialize database connections
    await initializeConnections();

    // Analyze source data
    const tableCounts = await analyzeSourceData();
    stats.totalRecords = Object.values(tableCounts).reduce((sum, count) => sum + count, 0);

    if (stats.totalRecords === 0) {
      console.log(colorize('\n✅ No data to migrate - SQLite database is empty', 'green'));
      return;
    }

    // Confirm migration
    console.log(colorize(`\n⚠️  This will migrate ${stats.totalRecords} records from SQLite to PostgreSQL`, 'yellow'));
    console.log(colorize('⚠️  Existing PostgreSQL data will be cleared for each table', 'yellow'));

    // For automated execution, skip confirmation
    // In interactive mode, you could add: const confirmed = await askForConfirmation();

    printSection('Data Migration');

    // Migrate tables in dependency order
    for (const tableName of MIGRATION_ORDER) {
      if (tableCounts[tableName] > 0) {
        await migrateTable(tableName, stats);
        stats.tablesProcessed++;
      }
    }

    // Validate migration
    const validationPassed = await validateMigration(tableCounts);

    // Generate report
    await generateMigrationReport(stats, tableCounts);

    if (validationPassed && stats.errors.length === 0) {
      console.log(colorize('\n🎉 Migration completed successfully!', 'green'));
      console.log(colorize('✅ All data has been migrated from SQLite to PostgreSQL', 'green'));
    } else {
      console.log(colorize('\n⚠️  Migration completed with issues', 'yellow'));
      console.log(colorize('🔍 Please review the migration report for details', 'yellow'));
    }

  } catch (error) {
    console.error(colorize(`\n❌ Migration failed: ${error}`, 'red'));
    stats.errors.push(`Critical error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    process.exit(1);
  } finally {
    await cleanupConnections();
  }
}

// Run the migration
if (require.main === module) {
  main().catch(console.error);
}
