#!/bin/bash

# Manufacturing ERP Development Workflow Scripts
# Usage: ./scripts/dev-workflow.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Pre-flight checks
preflight_check() {
    print_info "Running pre-flight checks..."
    
    # Check Node.js version
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js version: $NODE_VERSION"
    else
        print_error "Node.js not found. Please install Node.js v18+"
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm version: $NPM_VERSION"
    else
        print_error "npm not found"
        exit 1
    fi
    
    # Check Git
    if command_exists git; then
        GIT_VERSION=$(git --version)
        print_success "$GIT_VERSION"
    else
        print_error "Git not found"
        exit 1
    fi
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Are you in the project root?"
        exit 1
    fi
    
    print_success "Pre-flight checks completed"
}

# Setup development environment
setup_dev() {
    print_info "Setting up development environment..."
    
    # Install dependencies
    print_info "Installing dependencies..."
    npm install
    
    # Check environment file
    if [ ! -f ".env.local" ]; then
        print_warning ".env.local not found. Please create it with required variables."
        print_info "Required variables:"
        echo "  - DATABASE_URL"
        echo "  - AUTH0_SECRET"
        echo "  - AUTH0_BASE_URL"
        echo "  - AUTH0_ISSUER_BASE_URL"
        echo "  - AUTH0_CLIENT_ID"
        echo "  - AUTH0_CLIENT_SECRET"
    else
        print_success ".env.local found"
    fi
    
    print_success "Development environment setup completed"
}

# Run quality checks
quality_check() {
    print_info "Running quality checks..."
    
    # Type checking
    print_info "Running TypeScript type check..."
    if npm run type-check; then
        print_success "TypeScript type check passed"
    else
        print_error "TypeScript type check failed"
        exit 1
    fi
    
    # Linting
    print_info "Running ESLint..."
    if npm run lint; then
        print_success "ESLint passed"
    else
        print_error "ESLint failed"
        exit 1
    fi
    
    # Build check
    print_info "Running build check..."
    if npm run build; then
        print_success "Build check passed"
    else
        print_error "Build check failed"
        exit 1
    fi
    
    print_success "All quality checks passed"
}

# Create feature branch
create_feature() {
    if [ -z "$2" ]; then
        print_error "Feature name required. Usage: ./dev-workflow.sh feature [feature-name]"
        exit 1
    fi
    
    FEATURE_NAME="$2"
    BRANCH_NAME="feature/$FEATURE_NAME"
    
    print_info "Creating feature branch: $BRANCH_NAME"
    
    # Ensure we're on main and up to date
    git checkout main
    git pull origin main
    
    # Create and checkout feature branch
    git checkout -b "$BRANCH_NAME"
    
    # Create feature documentation
    mkdir -p docs/features
    cat > "docs/features/$FEATURE_NAME.md" << EOF
# Feature: $FEATURE_NAME

## Overview
Brief description of the feature.

## Requirements
- [ ] Requirement 1
- [ ] Requirement 2

## Implementation Plan
1. Step 1
2. Step 2

## Testing Checklist
- [ ] Functionality works locally
- [ ] Multi-tenant isolation verified
- [ ] Authentication working
- [ ] Database operations successful
- [ ] UI/UX responsive
- [ ] Error handling implemented

## Deployment Notes
Any special deployment considerations.
EOF
    
    print_success "Feature branch created: $BRANCH_NAME"
    print_info "Feature documentation created: docs/features/$FEATURE_NAME.md"
}

# Deploy to production
deploy() {
    print_info "Preparing for deployment..."
    
    # Run quality checks first
    quality_check
    
    # Check if we're on main branch
    CURRENT_BRANCH=$(git branch --show-current)
    if [ "$CURRENT_BRANCH" != "main" ]; then
        print_error "Must be on main branch to deploy. Current branch: $CURRENT_BRANCH"
        exit 1
    fi
    
    # Check for uncommitted changes
    if ! git diff-index --quiet HEAD --; then
        print_error "Uncommitted changes detected. Please commit or stash changes."
        exit 1
    fi
    
    # Push to trigger deployment
    print_info "Pushing to production..."
    git push origin main
    
    print_success "Deployment triggered!"
    print_info "Monitor deployment at: https://vercel.com/dashboard"
    print_warning "Wait 3-5 minutes before testing production"
}

# Show help
show_help() {
    echo "Manufacturing ERP Development Workflow"
    echo ""
    echo "Usage: ./scripts/dev-workflow.sh [command]"
    echo ""
    echo "Commands:"
    echo "  preflight    Run pre-flight checks"
    echo "  setup        Setup development environment"
    echo "  quality      Run quality checks (type-check, lint, build)"
    echo "  feature      Create new feature branch"
    echo "  deploy       Deploy to production"
    echo "  help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./scripts/dev-workflow.sh preflight"
    echo "  ./scripts/dev-workflow.sh setup"
    echo "  ./scripts/dev-workflow.sh feature customer-management"
    echo "  ./scripts/dev-workflow.sh quality"
    echo "  ./scripts/dev-workflow.sh deploy"
}

# Main script logic
case "$1" in
    "preflight")
        preflight_check
        ;;
    "setup")
        preflight_check
        setup_dev
        ;;
    "quality")
        quality_check
        ;;
    "feature")
        create_feature "$@"
        ;;
    "deploy")
        deploy
        ;;
    "help"|"")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
