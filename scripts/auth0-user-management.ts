#!/usr/bin/env tsx

/**
 * Auth0 User Management Script
 * 
 * This script helps manage Auth0 users for multi-tenant testing.
 * It can create users, verify existing users, and ensure proper
 * user accounts exist for testing multi-tenant isolation.
 * 
 * Usage:
 * - npm run auth0:create-test-users
 * - npm run auth0:verify-users
 * - npm run auth0:list-users
 */

import { db } from '../lib/db'
import { companies } from '../lib/schema-postgres'
import { eq } from 'drizzle-orm'

// For now, let's create a simpler approach without the Management API
// We'll focus on database synchronization and manual user creation instructions

interface TestUser {
  email: string
  password: string
  name: string
  companyName: string
  expectedAuth0Id?: string
}

// Test users that should exist in Auth0
const TEST_USERS: TestUser[] = [
  {
    email: '<EMAIL>',
    password: 'Ashatank@@2020',
    name: 'Dasso中国',
    companyName: 'Silk Road Manufacturing Co.',
    expectedAuth0Id: 'google-oauth2|113494700746753712423' // Existing Google OAuth user
  },
  {
    email: '<EMAIL>',
    password: 'Ashatank@@2020',
    name: 'Samovic Mental',
    companyName: 'Test New Company'
  }
]

/**
 * Check current database state
 */
async function checkDatabaseUsers() {
  try {
    console.log('🔍 Checking database companies...')

    const companies_list = await db.query.companies.findMany()

    console.log(`📊 Found ${companies_list.length} companies in database:`)
    console.log('================================')

    companies_list.forEach((company, index) => {
      console.log(`${index + 1}. ${company.name}`)
      console.log(`   Email: ${company.email}`)
      console.log(`   Auth0 ID: ${company.auth0_user_id}`)
      console.log(`   Onboarding: ${company.onboarding_completed}`)
      console.log(`   Created: ${company.created_at}`)
      console.log('')
    })

    return companies_list
  } catch (error) {
    console.error('❌ Error fetching database companies:', error)
    throw error
  }
}

/**
 * Provide manual Auth0 user creation instructions
 */
async function provideAuth0Instructions() {
  console.log('📋 MANUAL AUTH0 USER CREATION INSTRUCTIONS')
  console.log('==========================================')
  console.log('')
  console.log('Since we need to create Auth0 users manually, please follow these steps:')
  console.log('')
  console.log('🔗 1. Go to Auth0 Dashboard:')
  console.log('   https://manage.auth0.com/dashboard/us/dev-tejx02ztaj7oufoc/')
  console.log('')
  console.log('👤 2. Navigate to User Management > Users')
  console.log('')
  console.log('➕ 3. Click "Create User" and add these test users:')
  console.log('')

  TEST_USERS.forEach((user, index) => {
    if (user.email === '<EMAIL>') {
      console.log(`   ${index + 1}. ${user.email} (Already exists via Google OAuth)`)
      console.log(`      ✅ This user should already exist`)
    } else {
      console.log(`   ${index + 1}. ${user.email}`)
      console.log(`      Email: ${user.email}`)
      console.log(`      Password: ${user.password}`)
      console.log(`      Name: ${user.name}`)
      console.log(`      Connection: Username-Password-Authentication`)
      console.log(`      Email Verified: ✅ Yes`)
    }
    console.log('')
  })

  console.log('🔧 4. After creating users, run this script again to sync with database')
  console.log('')
}

/**
 * Create database company records for test users
 */
async function createDatabaseCompanies() {
  console.log('🏢 CREATING DATABASE COMPANY RECORDS')
  console.log('====================================')

  const results = {
    existing: [] as any[],
    created: [] as any[],
    errors: [] as any[]
  }

  for (const testUser of TEST_USERS) {
    try {
      console.log(`\n🔍 Checking company for: ${testUser.email}`)

      // Check if company already exists
      const existingCompany = await db.query.companies.findFirst({
        where: eq(companies.email, testUser.email)
      })

      if (existingCompany) {
        console.log(`✅ Company exists: ${existingCompany.name}`)
        results.existing.push(existingCompany)
      } else {
        console.log(`❌ Company not found, creating...`)

        // Generate company ID
        const companyId = `company_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        // Create new company
        const newCompany = {
          id: companyId,
          auth0_user_id: testUser.expectedAuth0Id || `auth0|${testUser.email.split('@')[0]}`,
          name: testUser.companyName,
          display_name: testUser.companyName,
          email: testUser.email,
          onboarding_completed: 'true',
          status: 'active' as const,
          created_at: Math.floor(Date.now() / 1000),
          updated_at: Math.floor(Date.now() / 1000)
        }

        await db.insert(companies).values(newCompany)
        console.log(`✅ Created company: ${companyId}`)
        results.created.push(newCompany)
      }
    } catch (error) {
      console.error(`💥 Error processing ${testUser.email}:`, error)
      results.errors.push({
        email: testUser.email,
        error: error
      })
    }
  }

  return results
}

/**
 * Test multi-tenant isolation setup
 */
async function testMultiTenantSetup() {
  console.log('🧪 TESTING MULTI-TENANT SETUP')
  console.log('==============================')

  const companies_list = await db.query.companies.findMany()

  console.log(`📊 Companies in database: ${companies_list.length}`)

  // Check for proper isolation
  const companyIds = companies_list.map(c => c.id)
  const uniqueCompanyIds = new Set(companyIds)

  if (companyIds.length === uniqueCompanyIds.size) {
    console.log('✅ All companies have unique IDs')
  } else {
    console.log('❌ Duplicate company IDs detected!')
  }

  // Check Auth0 ID uniqueness
  const auth0Ids = companies_list.map(c => c.auth0_user_id).filter(Boolean)
  const uniqueAuth0Ids = new Set(auth0Ids)

  if (auth0Ids.length === uniqueAuth0Ids.size) {
    console.log('✅ All companies have unique Auth0 IDs')
  } else {
    console.log('❌ Duplicate Auth0 IDs detected!')
  }

  console.log('')
  console.log('📋 Company Summary:')
  companies_list.forEach((company, index) => {
    console.log(`${index + 1}. ${company.name} (${company.email})`)
    console.log(`   Company ID: ${company.id}`)
    console.log(`   Auth0 ID: ${company.auth0_user_id}`)
    console.log('')
  })
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting Auth0 Setup & Database Sync...')

    // Step 1: Check current database state
    console.log('\n📋 STEP 1: Current Database State')
    await checkDatabaseUsers()

    // Step 2: Provide Auth0 instructions
    console.log('\n📋 STEP 2: Auth0 User Creation Instructions')
    await provideAuth0Instructions()

    // Step 3: Create/verify database companies
    console.log('\n📋 STEP 3: Database Company Setup')
    const results = await createDatabaseCompanies()

    // Step 4: Test multi-tenant setup
    console.log('\n📋 STEP 4: Multi-Tenant Setup Test')
    await testMultiTenantSetup()

    // Step 5: Final summary
    console.log('\n🎯 FINAL SUMMARY')
    console.log('================')
    console.log(`✅ Existing companies: ${results.existing.length}`)
    console.log(`🆕 Created companies: ${results.created.length}`)
    console.log(`❌ Errors: ${results.errors.length}`)

    if (results.created.length > 0) {
      console.log('\n🆕 Created Companies:')
      results.created.forEach((company: any) => {
        console.log(`   - ${company.name} (${company.email})`)
      })
    }

    console.log('\n📋 NEXT STEPS:')
    console.log('==============')
    console.log('1. 🔗 Go to Auth0 Dashboard and create the users manually')
    console.log('2. 🧪 Test login with both users in incognito mode')
    console.log('3. 🔍 Verify multi-tenant isolation is working')
    console.log('4. 📊 Run: npm run test:comprehensive-security')

    console.log('\n🎉 Auth0 setup preparation completed!')

  } catch (error) {
    console.error('💥 Auth0 setup failed:', error)
    process.exit(1)
  }
}

// Export functions for individual use
export {
  checkDatabaseUsers,
  provideAuth0Instructions,
  createDatabaseCompanies,
  testMultiTenantSetup
}

// Run main function if called directly
if (require.main === module) {
  main()
}
