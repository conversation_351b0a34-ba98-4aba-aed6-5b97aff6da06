#!/usr/bin/env tsx

/**
 * Manufacturing ERP - Schema Import Update Script
 * 
 * Updates all imports from lib/schema to lib/schema-postgres
 * for the PostgreSQL migration
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title: string) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize(`  ${title}`, 'bright'));
  console.log(colorize('='.repeat(60), 'cyan'));
}

async function findFilesWithSchemaImports(): Promise<string[]> {
  const patterns = [
    'app/**/*.ts',
    'app/**/*.tsx',
    'lib/**/*.ts',
    'lib/**/*.tsx',
    'scripts/**/*.ts',
    'components/**/*.ts',
    'components/**/*.tsx',
  ];

  const allFiles: string[] = [];
  
  for (const pattern of patterns) {
    const files = await glob(pattern, { 
      ignore: [
        'node_modules/**',
        '.next/**',
        'dist/**',
        'build/**',
        'lib/schema.ts', // Don't update the original schema file
        'lib/schema-postgres.ts', // Don't update the new schema file
      ]
    });
    allFiles.push(...files);
  }

  // Filter files that actually import from lib/schema
  const filesWithSchemaImports: string[] = [];
  
  for (const file of allFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('from "@/lib/schema-postgres"') || 
          content.includes('from "../lib/schema-postgres"') ||
          content.includes('from "../../lib/schema-postgres"') ||
          content.includes('from "../../../lib/schema-postgres"') ||
          content.includes('from "../../../../lib/schema-postgres"') ||
          content.includes("from '@/lib/schema-postgres'") ||
          content.includes("from '../lib/schema-postgres'")) {
        filesWithSchemaImports.push(file);
      }
    } catch (error) {
      console.warn(`Warning: Could not read file ${file}:`, error);
    }
  }

  return filesWithSchemaImports;
}

function updateSchemaImports(filePath: string): { updated: boolean; changes: string[] } {
  const content = fs.readFileSync(filePath, 'utf8');
  const changes: string[] = [];
  let updatedContent = content;
  let updated = false;

  // Update various import patterns
  const importPatterns = [
    {
      pattern: /from "@\/lib\/schema"/g,
      replacement: 'from "@/lib/schema-postgres"',
      description: 'Updated @/lib/schema import'
    },
    {
      pattern: /from "\.\.\/lib\/schema"/g,
      replacement: 'from "../lib/schema-postgres"',
      description: 'Updated ../lib/schema import'
    },
    {
      pattern: /from "\.\.\/\.\.\/lib\/schema"/g,
      replacement: 'from "../../lib/schema-postgres"',
      description: 'Updated ../../lib/schema import'
    },
    {
      pattern: /from "\.\.\/\.\.\/\.\.\/lib\/schema"/g,
      replacement: 'from "../../../lib/schema-postgres"',
      description: 'Updated ../../../lib/schema import'
    },
    {
      pattern: /from "\.\.\/\.\.\/\.\.\/\.\.\/lib\/schema"/g,
      replacement: 'from "../../../../lib/schema-postgres"',
      description: 'Updated ../../../../lib/schema import'
    },
    {
      pattern: /from '@\/lib\/schema'/g,
      replacement: "from '@/lib/schema-postgres'",
      description: "Updated @/lib/schema import (single quotes)"
    },
    {
      pattern: /from '\.\.\/lib\/schema'/g,
      replacement: "from '../lib/schema-postgres'",
      description: "Updated ../lib/schema import (single quotes)"
    },
  ];

  for (const { pattern, replacement, description } of importPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      updatedContent = updatedContent.replace(pattern, replacement);
      changes.push(`${description} (${matches.length} occurrence${matches.length > 1 ? 's' : ''})`);
      updated = true;
    }
  }

  if (updated) {
    fs.writeFileSync(filePath, updatedContent, 'utf8');
  }

  return { updated, changes };
}

async function main() {
  printHeader('Manufacturing ERP - Schema Import Update');

  console.log(colorize('🔍 Finding files with schema imports...', 'blue'));
  
  const files = await findFilesWithSchemaImports();
  
  console.log(colorize(`📁 Found ${files.length} files with schema imports:`, 'yellow'));
  files.forEach(file => console.log(`  - ${file}`));

  if (files.length === 0) {
    console.log(colorize('✅ No files need updating!', 'green'));
    return;
  }

  console.log(colorize('\n🔄 Updating schema imports...', 'blue'));

  let totalUpdated = 0;
  let totalChanges = 0;

  for (const file of files) {
    try {
      const result = updateSchemaImports(file);
      
      if (result.updated) {
        totalUpdated++;
        totalChanges += result.changes.length;
        
        console.log(colorize(`✅ Updated: ${file}`, 'green'));
        result.changes.forEach(change => {
          console.log(`    ${change}`);
        });
      } else {
        console.log(colorize(`⏭️  Skipped: ${file} (no changes needed)`, 'yellow'));
      }
    } catch (error) {
      console.error(colorize(`❌ Error updating ${file}:`, 'red'), error);
    }
  }

  console.log(colorize('\n📊 Update Summary:', 'cyan'));
  console.log(`  Files processed: ${files.length}`);
  console.log(`  Files updated: ${totalUpdated}`);
  console.log(`  Total changes: ${totalChanges}`);

  if (totalUpdated > 0) {
    console.log(colorize('\n🎉 Schema import update completed successfully!', 'green'));
    console.log(colorize('All files now use the PostgreSQL schema.', 'green'));
  } else {
    console.log(colorize('\n✅ All files were already up to date!', 'green'));
  }
}

// Run the update
if (require.main === module) {
  main().catch(console.error);
}
