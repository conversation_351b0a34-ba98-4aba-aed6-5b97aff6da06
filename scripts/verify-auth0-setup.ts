#!/usr/bin/env tsx

/**
 * Auth0 Setup Verification Script
 * 
 * This script helps verify and fix the Auth0 setup for proper
 * multi-tenant isolation testing.
 */

import { db } from '../lib/db'
import { companies } from '../lib/schema-postgres'
import { eq } from 'drizzle-orm'

async function verifyDatabaseSetup() {
  console.log('🔍 VERIFYING DATABASE SETUP')
  console.log('===========================')
  
  const companies_list = await db.query.companies.findMany()
  
  console.log(`📊 Found ${companies_list.length} companies:`)
  console.log('')
  
  companies_list.forEach((company, index) => {
    console.log(`${index + 1}. ${company.name}`)
    console.log(`   Email: ${company.email}`)
    console.log(`   Company ID: ${company.id}`)
    console.log(`   Auth0 ID: ${company.auth0_user_id}`)
    console.log(`   Onboarding: ${company.onboarding_completed}`)
    console.log(`   Status: ${company.status}`)
    console.log('')
  })
  
  return companies_list
}

async function createProperTestUser() {
  console.log('🛠️  CREATING PROPER TEST USER SETUP')
  console.log('====================================')
  
  // Check if samovicmental user exists
  const existingUser = await db.query.companies.findFirst({
    where: eq(companies.email, '<EMAIL>')
  })
  
  if (existingUser) {
    console.log('✅ <EMAIL> company record exists')
    console.log(`   Current Auth0 ID: ${existingUser.auth0_user_id}`)
    
    // Update with a more realistic Auth0 ID format
    const newAuth0Id = 'auth0|66f8a1b2c3d4e5f6g7h8i9j0' // Realistic Auth0 format
    
    await db.update(companies)
      .set({ auth0_user_id: newAuth0Id })
      .where(eq(companies.email, '<EMAIL>'))
    
    console.log(`✅ Updated Auth0 ID to: ${newAuth0Id}`)
  } else {
    console.log('❌ No company record <NAME_EMAIL>')
    
    // Create the company record
    const newCompany = {
      id: `company_${Date.now()}_samovic`,
      auth0_user_id: 'auth0|66f8a1b2c3d4e5f6g7h8i9j0',
      name: 'Samovic Test Company',
      display_name: 'Samovic Test Company',
      email: '<EMAIL>',
      onboarding_completed: 'true',
      status: 'active' as const,
      created_at: Math.floor(Date.now() / 1000),
      updated_at: Math.floor(Date.now() / 1000)
    }
    
    await db.insert(companies).values(newCompany)
    console.log(`✅ Created company record: ${newCompany.id}`)
  }
}

async function simulateAuth0UserCreation() {
  console.log('🎭 SIMULATING AUTH0 USER CREATION')
  console.log('=================================')
  console.log('')
  console.log('Since we cannot directly create Auth0 users via API without')
  console.log('proper Management API credentials, here\'s what needs to happen:')
  console.log('')
  console.log('🔗 MANUAL AUTH0 SETUP REQUIRED:')
  console.log('1. Go to: https://manage.auth0.com/dashboard/us/dev-tejx02ztaj7oufoc/')
  console.log('2. Navigate to User Management > Users')
  console.log('3. Click "Create User"')
  console.log('4. Fill in:')
  console.log('   - Email: <EMAIL>')
  console.log('   - Password: Ashatank@@2020')
  console.log('   - Connection: Username-Password-Authentication')
  console.log('   - Email Verified: ✅ Yes')
  console.log('5. After creation, note the User ID (should be like: auth0|...)')
  console.log('6. Update database with: npm run update-auth0-id')
  console.log('')
}

async function testMultiTenantIsolation() {
  console.log('🧪 TESTING MULTI-TENANT ISOLATION READINESS')
  console.log('============================================')
  
  const companies_list = await db.query.companies.findMany()
  
  // Check for unique company IDs
  const companyIds = companies_list.map(c => c.id)
  const uniqueCompanyIds = new Set(companyIds)
  
  if (companyIds.length === uniqueCompanyIds.size) {
    console.log('✅ All companies have unique IDs')
  } else {
    console.log('❌ Duplicate company IDs detected!')
  }
  
  // Check for unique Auth0 IDs
  const auth0Ids = companies_list.map(c => c.auth0_user_id).filter(Boolean)
  const uniqueAuth0Ids = new Set(auth0Ids)
  
  if (auth0Ids.length === uniqueAuth0Ids.size) {
    console.log('✅ All companies have unique Auth0 IDs')
  } else {
    console.log('❌ Duplicate Auth0 IDs detected!')
  }
  
  // Check for proper onboarding status
  const completedOnboarding = companies_list.filter(c => c.onboarding_completed === 'true')
  console.log(`✅ ${completedOnboarding.length}/${companies_list.length} companies have completed onboarding`)
  
  console.log('')
  console.log('📊 ISOLATION TEST SUMMARY:')
  console.log('==========================')
  
  const testUsers = companies_list.filter(c => 
    c.email === '<EMAIL>' || c.email === '<EMAIL>'
  )
  
  testUsers.forEach((company, index) => {
    const readyStatus = company.email === '<EMAIL>' ? '✅ Ready (existing user)' :
                       company.email === '<EMAIL>' ? '⚠️  Needs Auth0 user creation' :
                       '❓ Unknown'
    
    console.log(`${index + 1}. ${company.email}`)
    console.log(`   Company: ${company.name}`)
    console.log(`   Auth0 ID: ${company.auth0_user_id}`)
    console.log(`   Status: ${readyStatus}`)
    console.log('')
  })
}

async function provideFinalInstructions() {
  console.log('📋 FINAL SETUP INSTRUCTIONS')
  console.log('===========================')
  console.log('')
  console.log('🎯 TO COMPLETE AUTH0 SETUP:')
  console.log('')
  console.log('1. 🔗 Create Auth0 User:')
  console.log('   - Go to Auth0 Dashboard')
  console.log('   - Create user: <EMAIL>')
  console.log('   - Password: Ashatank@@2020')
  console.log('   - Note the generated User ID')
  console.log('')
  console.log('2. 🔄 Update Database:')
  console.log('   - Run: sqlite3 dev.db "UPDATE companies SET auth0_user_id = \'ACTUAL_AUTH0_ID\' WHERE email = \'<EMAIL>\';"')
  console.log('   - Replace ACTUAL_AUTH0_ID with the real ID from Auth0')
  console.log('')
  console.log('3. 🧪 Test Multi-Tenant Isolation:')
  console.log('   - Open incognito browser')
  console.log('   - <NAME_EMAIL>')
  console.log('   - Verify empty dashboard (0 customers, 0 products)')
  console.log('   - Open new window, <NAME_EMAIL>')
  console.log('   - Verify existing data is still there')
  console.log('')
  console.log('4. 📊 Verify Success:')
  console.log('   - Run: npm run test:comprehensive-security')
  console.log('   - All tests should pass')
  console.log('')
  console.log('🎉 EXPECTED OUTCOME:')
  console.log('   - Perfect multi-tenant isolation')
  console.log('   - Each user sees only their own data')
  console.log('   - No cross-contamination between companies')
  console.log('')
}

async function main() {
  try {
    console.log('🚀 AUTH0 SETUP VERIFICATION & FIX')
    console.log('=================================')
    console.log('')
    
    // Step 1: Verify current database setup
    await verifyDatabaseSetup()
    
    // Step 2: Create/fix test user setup
    await createProperTestUser()
    
    // Step 3: Simulate Auth0 user creation process
    await simulateAuth0UserCreation()
    
    // Step 4: Test multi-tenant isolation readiness
    await testMultiTenantIsolation()
    
    // Step 5: Provide final instructions
    await provideFinalInstructions()
    
    console.log('✅ Auth0 setup verification completed!')
    console.log('')
    console.log('🎯 NEXT ACTION: Create the Auth0 user manually as instructed above')
    
  } catch (error) {
    console.error('💥 Verification failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
