#!/usr/bin/env tsx

/**
 * Manufacturing ERP - Migration Data Integrity Validation Script
 * 
 * Comprehensive validation of data integrity after SQLite to PostgreSQL migration
 * Checks foreign key relationships, data consistency, and multi-tenant isolation
 */

import Database from "better-sqlite3";
import postgres from "postgres";
import path from "path";

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title: string) {
  console.log('\n' + colorize('='.repeat(70), 'cyan'));
  console.log(colorize(`  ${title}`, 'bright'));
  console.log(colorize('='.repeat(70), 'cyan'));
}

function printSection(title: string) {
  console.log('\n' + colorize(`🔍 ${title}`, 'blue'));
  console.log(colorize('-'.repeat(50), 'blue'));
}

interface ValidationResult {
  test: string;
  passed: boolean;
  details: string;
  critical: boolean;
}

// Database connections
let sqliteDb: Database.Database;
let postgresDb: postgres.Sql;

async function initializeConnections() {
  // SQLite connection
  const sqlitePath = path.join(process.cwd(), "dev.db");
  sqliteDb = new Database(sqlitePath, { readonly: true });
  
  // PostgreSQL connection
  const postgresUrl = process.env.DATABASE_URL_POSTGRESQL || 
                     process.env.POSTGRES_LOCAL_URL || 
                     process.env.DATABASE_URL ||
                     'postgresql://erp_user:SecureERP2024!@localhost:5432/manufacturing_erp';
  
  postgresDb = postgres(postgresUrl, { max: 5 });
  
  console.log(colorize('✅ Database connections initialized', 'green'));
}

async function validateRecordCounts(): Promise<ValidationResult[]> {
  printSection('Record Count Validation');
  
  const results: ValidationResult[] = [];
  const tables = [
    'companies', 'customers', 'suppliers', 'products', 'samples',
    'contract_templates', 'sales_contracts', 'purchase_contracts',
    'sales_contract_items', 'purchase_contract_items', 'work_orders',
    'stock_lots', 'stock_txns', 'declarations', 'declaration_items',
    'ar_invoices', 'ap_invoices'
  ];
  
  for (const table of tables) {
    try {
      // Get SQLite count
      const sqliteResult = sqliteDb.prepare(`SELECT COUNT(*) as count FROM ${table}`).get() as { count: number };
      const sqliteCount = sqliteResult.count;
      
      // Get PostgreSQL count
      const postgresResult = await postgresDb`SELECT COUNT(*) as count FROM ${postgresDb(table)}`;
      const postgresCount = parseInt(postgresResult[0].count);
      
      const passed = sqliteCount === postgresCount;
      
      results.push({
        test: `Record count: ${table}`,
        passed,
        details: `SQLite: ${sqliteCount}, PostgreSQL: ${postgresCount}`,
        critical: true
      });
      
      if (passed) {
        console.log(colorize(`✅ ${table}: ${postgresCount} records`, 'green'));
      } else {
        console.log(colorize(`❌ ${table}: SQLite ${sqliteCount} ≠ PostgreSQL ${postgresCount}`, 'red'));
      }
      
    } catch (error) {
      results.push({
        test: `Record count: ${table}`,
        passed: false,
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        critical: true
      });
      console.log(colorize(`❌ ${table}: Validation error`, 'red'));
    }
  }
  
  return results;
}

async function validateForeignKeyRelationships(): Promise<ValidationResult[]> {
  printSection('Foreign Key Relationship Validation');
  
  const results: ValidationResult[] = [];
  
  // Test key relationships
  const relationships = [
    {
      name: 'Customers → Companies',
      query: `
        SELECT COUNT(*) as orphans 
        FROM customers c 
        LEFT JOIN companies co ON c.company_id = co.id 
        WHERE co.id IS NULL
      `
    },
    {
      name: 'Products → Companies',
      query: `
        SELECT COUNT(*) as orphans 
        FROM products p 
        LEFT JOIN companies co ON p.company_id = co.id 
        WHERE co.id IS NULL
      `
    },
    {
      name: 'Sales Contracts → Customers',
      query: `
        SELECT COUNT(*) as orphans 
        FROM sales_contracts sc 
        LEFT JOIN customers c ON sc.customer_id = c.id 
        WHERE c.id IS NULL
      `
    },
    {
      name: 'Sales Contract Items → Sales Contracts',
      query: `
        SELECT COUNT(*) as orphans 
        FROM sales_contract_items sci 
        LEFT JOIN sales_contracts sc ON sci.contract_id = sc.id 
        WHERE sc.id IS NULL
      `
    },
    {
      name: 'Sales Contract Items → Products',
      query: `
        SELECT COUNT(*) as orphans 
        FROM sales_contract_items sci 
        LEFT JOIN products p ON sci.product_id = p.id 
        WHERE p.id IS NULL
      `
    }
  ];
  
  for (const relationship of relationships) {
    try {
      const result = await postgresDb.unsafe(relationship.query);
      const orphanCount = parseInt(result[0].orphans);
      const passed = orphanCount === 0;
      
      results.push({
        test: relationship.name,
        passed,
        details: `Orphaned records: ${orphanCount}`,
        critical: true
      });
      
      if (passed) {
        console.log(colorize(`✅ ${relationship.name}: No orphaned records`, 'green'));
      } else {
        console.log(colorize(`❌ ${relationship.name}: ${orphanCount} orphaned records`, 'red'));
      }
      
    } catch (error) {
      results.push({
        test: relationship.name,
        passed: false,
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        critical: true
      });
      console.log(colorize(`❌ ${relationship.name}: Validation error`, 'red'));
    }
  }
  
  return results;
}

async function validateMultiTenantIsolation(): Promise<ValidationResult[]> {
  printSection('Multi-Tenant Isolation Validation');
  
  const results: ValidationResult[] = [];
  
  try {
    // Check that all records have company_id
    const tablesWithCompanyId = [
      'customers', 'suppliers', 'products', 'samples', 'contract_templates',
      'sales_contracts', 'purchase_contracts', 'work_orders', 'stock_lots',
      'stock_txns', 'declarations', 'ar_invoices', 'ap_invoices'
    ];
    
    for (const table of tablesWithCompanyId) {
      try {
        const result = await postgresDb.unsafe(`
          SELECT COUNT(*) as null_company_ids 
          FROM ${table} 
          WHERE company_id IS NULL
        `);
        
        const nullCount = parseInt(result[0].null_company_ids);
        const passed = nullCount === 0;
        
        results.push({
          test: `Multi-tenant: ${table} company_id`,
          passed,
          details: `Records with NULL company_id: ${nullCount}`,
          critical: true
        });
        
        if (passed) {
          console.log(colorize(`✅ ${table}: All records have company_id`, 'green'));
        } else {
          console.log(colorize(`❌ ${table}: ${nullCount} records missing company_id`, 'red'));
        }
        
      } catch (error) {
        results.push({
          test: `Multi-tenant: ${table} company_id`,
          passed: false,
          details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          critical: true
        });
      }
    }
    
    // Check company isolation
    const companyCount = await postgresDb`SELECT COUNT(DISTINCT company_id) as count FROM customers`;
    const distinctCompanies = parseInt(companyCount[0].count);
    
    if (distinctCompanies > 0) {
      console.log(colorize(`📊 Found ${distinctCompanies} distinct companies`, 'cyan'));
      
      // Verify no cross-company references
      const crossCompanyCheck = await postgresDb`
        SELECT COUNT(*) as violations
        FROM sales_contracts sc
        JOIN customers c ON sc.customer_id = c.id
        WHERE sc.company_id != c.company_id
      `;
      
      const violations = parseInt(crossCompanyCheck[0].violations);
      const passed = violations === 0;
      
      results.push({
        test: 'Cross-company reference check',
        passed,
        details: `Cross-company violations: ${violations}`,
        critical: true
      });
      
      if (passed) {
        console.log(colorize(`✅ No cross-company reference violations`, 'green'));
      } else {
        console.log(colorize(`❌ ${violations} cross-company reference violations`, 'red'));
      }
    }
    
  } catch (error) {
    results.push({
      test: 'Multi-tenant isolation',
      passed: false,
      details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      critical: true
    });
  }
  
  return results;
}

async function validateDataTypes(): Promise<ValidationResult[]> {
  printSection('Data Type Validation');
  
  const results: ValidationResult[] = [];
  
  try {
    // Check timestamp fields
    const timestampCheck = await postgresDb`
      SELECT COUNT(*) as invalid_timestamps
      FROM companies
      WHERE created_at IS NOT NULL AND created_at::text !~ '^[0-9]{4}-[0-9]{2}-[0-9]{2}'
    `;
    
    const invalidTimestamps = parseInt(timestampCheck[0].invalid_timestamps);
    const passed = invalidTimestamps === 0;
    
    results.push({
      test: 'Timestamp format validation',
      passed,
      details: `Invalid timestamps: ${invalidTimestamps}`,
      critical: false
    });
    
    if (passed) {
      console.log(colorize(`✅ All timestamps properly formatted`, 'green'));
    } else {
      console.log(colorize(`⚠️  ${invalidTimestamps} invalid timestamps found`, 'yellow'));
    }
    
  } catch (error) {
    results.push({
      test: 'Data type validation',
      passed: false,
      details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      critical: false
    });
  }
  
  return results;
}

async function generateValidationReport(allResults: ValidationResult[]): Promise<void> {
  printSection('Validation Report');

  const passed = allResults.filter(r => r.passed).length;
  const failed = allResults.filter(r => !r.passed).length;
  const critical = allResults.filter(r => !r.passed && r.critical).length;

  console.log(colorize(`📊 Validation Summary:`, 'bright'));
  console.log(`   Total tests: ${allResults.length}`);
  console.log(`   Passed: ${colorize(passed.toString(), 'green')}`);
  console.log(`   Failed: ${colorize(failed.toString(), failed > 0 ? 'red' : 'green')}`);
  console.log(`   Critical failures: ${colorize(critical.toString(), critical > 0 ? 'red' : 'green')}`);

  if (failed > 0) {
    console.log(colorize(`\n❌ Failed Tests:`, 'red'));
    allResults.filter(r => !r.passed).forEach(result => {
      const severity = result.critical ? '🔴 CRITICAL' : '🟡 WARNING';
      console.log(`   ${severity} ${result.test}: ${result.details}`);
    });
  }

  // Save detailed report
  const reportPath = `validation-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: allResults.length,
      passed,
      failed,
      criticalFailures: critical,
      overallSuccess: critical === 0
    },
    results: allResults
  };

  require('fs').writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(colorize(`\n📄 Detailed report saved: ${reportPath}`, 'cyan'));

  if (critical === 0) {
    console.log(colorize('\n🎉 Data integrity validation passed!', 'green'));
    console.log(colorize('✅ Migration data is consistent and properly structured', 'green'));
  } else {
    console.log(colorize('\n⚠️  Critical data integrity issues found', 'red'));
    console.log(colorize('🔧 Please review and fix issues before proceeding', 'red'));
  }
}

async function cleanupConnections(): Promise<void> {
  try {
    if (sqliteDb) {
      sqliteDb.close();
    }
    if (postgresDb) {
      await postgresDb.end();
    }
    console.log(colorize('🔌 Database connections closed', 'cyan'));
  } catch (error) {
    console.log(colorize(`⚠️  Error closing connections: ${error}`, 'yellow'));
  }
}

async function main() {
  printHeader('Manufacturing ERP - Migration Data Integrity Validation');

  try {
    await initializeConnections();

    // Run all validation tests
    const allResults: ValidationResult[] = [];

    allResults.push(...await validateRecordCounts());
    allResults.push(...await validateForeignKeyRelationships());
    allResults.push(...await validateMultiTenantIsolation());
    allResults.push(...await validateDataTypes());

    // Generate comprehensive report
    await generateValidationReport(allResults);

  } catch (error) {
    console.error(colorize(`\n❌ Validation failed: ${error}`, 'red'));
    process.exit(1);
  } finally {
    await cleanupConnections();
  }
}

// Run the validation
if (require.main === module) {
  main().catch(console.error);
}
