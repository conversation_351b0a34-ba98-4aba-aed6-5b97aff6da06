/**
 * Fix Contract Templates - Replace Placeholder Text with Template Variables
 * 
 * This script fixes the critical issue where contract templates contain literal
 * placeholder text like "[Your Company Name]" instead of template variables
 * like "{{company_name}}".
 * 
 * Issue: Contract previews show placeholder text instead of actual company data
 * Root Cause: Templates use literal placeholders instead of template variables
 * Solution: Update all templates to use correct {{variable}} format
 */

import { db } from '../lib/db'
import { contractTemplates } from '../lib/schema-postgres'
import { eq } from 'drizzle-orm'

// Mapping of placeholder text to template variables
const PLACEHOLDER_MAPPINGS = {
  // Company Information
  '[Your Company Name]': '{{company_name}}',
  '[Your Legal Company Name]': '{{company_legal_name}}',
  '[Your Company Address]': '{{company_full_address}}',
  '[Your Email]': '{{company_email}}',
  '[Your Phone]': '{{company_phone}}',
  '[Your Website]': '{{company_website}}',
  '[Your Tax ID]': '{{company_tax_id}}',
  '[Your VAT Number]': '{{company_vat_number}}',
  '[Your Registration Number]': '{{company_registration_number}}',
  '[Your Contact Information]': '{{company_phone}}',
  
  // Bank Information
  '[Your Bank Name]': '{{company_bank_name}}',
  '[Your Bank Account]': '{{company_bank_account}}',
  '[Your Bank SWIFT]': '{{company_bank_swift}}',
  '[Your Bank Address]': '{{company_bank_address}}',
  
  // Additional placeholders that might exist
  '[Company Name]': '{{company_name}}',
  '[Company Address]': '{{company_full_address}}',
  '[Company Email]': '{{company_email}}',
  '[Company Phone]': '{{company_phone}}',
  '[Company Tax ID]': '{{company_tax_id}}',
}

async function fixContractTemplates() {
  console.log('🔧 Starting Contract Template Fix...')
  
  try {
    // Get all contract templates
    const templates = await db.query.contractTemplates.findMany()
    console.log(`📋 Found ${templates.length} templates to check`)
    
    let updatedCount = 0
    
    for (const template of templates) {
      let updatedContent = template.content
      let hasChanges = false
      
      console.log(`\n🔍 Checking template: ${template.name} (${template.type})`)
      
      // Apply all placeholder mappings
      for (const [placeholder, variable] of Object.entries(PLACEHOLDER_MAPPINGS)) {
        if (updatedContent.includes(placeholder)) {
          console.log(`  ✅ Replacing "${placeholder}" with "${variable}"`)
          updatedContent = updatedContent.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), variable)
          hasChanges = true
        }
      }
      
      // Update the template if changes were made
      if (hasChanges) {
        await db.update(contractTemplates)
          .set({ content: updatedContent })
          .where(eq(contractTemplates.id, template.id))
        
        updatedCount++
        console.log(`  💾 Updated template: ${template.name}`)
      } else {
        console.log(`  ✅ Template already uses correct format: ${template.name}`)
      }
    }
    
    console.log(`\n🎉 Contract Template Fix Complete!`)
    console.log(`📊 Summary:`)
    console.log(`   - Total templates checked: ${templates.length}`)
    console.log(`   - Templates updated: ${updatedCount}`)
    console.log(`   - Templates already correct: ${templates.length - updatedCount}`)
    
    if (updatedCount > 0) {
      console.log(`\n✅ Contract previews should now display actual company data instead of placeholder text!`)
    }
    
  } catch (error) {
    console.error('❌ Error fixing contract templates:', error)
    throw error
  }
}

// Run the fix if this script is executed directly
if (require.main === module) {
  fixContractTemplates()
    .then(() => {
      console.log('✅ Script completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Script failed:', error)
      process.exit(1)
    })
}

export { fixContractTemplates }
