#!/usr/bin/env node

/**
 * Manufacturing ERP Database Connection Check
 * Validates database connectivity and basic functionality
 */

const { Pool } = require('pg');

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function printSuccess(message) {
  console.log(`${colors.green}✅ ${message}${colors.reset}`);
}

function printError(message) {
  console.log(`${colors.red}❌ ${message}${colors.reset}`);
}

function printWarning(message) {
  console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

function printInfo(message) {
  console.log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
}

async function checkDatabase() {
  console.log('🔍 Checking Manufacturing ERP database connection...\n');

  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    printError('DATABASE_URL environment variable is not set');
    console.log('\n💡 Setup Guide:');
    console.log('1. Set DATABASE_URL in your .env.local file');
    console.log('2. Format: postgresql://username:password@host:port/database');
    console.log('3. Refer to docs/ENVIRONMENT_SETUP.md for detailed instructions');
    process.exit(1);
  }

  const dbUrl = process.env.DATABASE_URL;
  printInfo(`Database URL: ${dbUrl.replace(/:[^:@]*@/, ':***@')}`);

  // Determine environment
  const isLocal = dbUrl.includes('localhost') || dbUrl.includes('127.0.0.1');
  const environment = isLocal ? 'Local Development' : 'Remote/Production';
  printInfo(`Environment: ${environment}`);

  const pool = new Pool({
    connectionString: dbUrl,
    ssl: isLocal ? false : { rejectUnauthorized: false }
  });

  try {
    console.log('\n🔌 Testing database connection...');
    const client = await pool.connect();
    printSuccess('Database connection established');

    // Test basic query
    console.log('\n⏰ Testing basic query...');
    const timeResult = await client.query('SELECT NOW() as current_time');
    const currentTime = timeResult.rows[0].current_time;
    printSuccess(`Database time: ${currentTime}`);

    // Check database version
    console.log('\n🗄️  Checking database version...');
    const versionResult = await client.query('SELECT version()');
    const version = versionResult.rows[0].version;
    const postgresVersion = version.match(/PostgreSQL (\d+\.\d+)/)?.[1] || 'Unknown';
    printSuccess(`PostgreSQL version: ${postgresVersion}`);

    // Check if main tables exist
    console.log('\n📋 Checking Manufacturing ERP tables...');
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('companies', 'customers', 'products', 'suppliers', 'samples')
      ORDER BY table_name;
    `;
    
    const tablesResult = await client.query(tablesQuery);
    const existingTables = tablesResult.rows.map(row => row.table_name);
    const expectedTables = ['companies', 'customers', 'products', 'suppliers', 'samples'];
    
    existingTables.forEach(table => {
      printSuccess(`Table exists: ${table}`);
    });
    
    const missingTables = expectedTables.filter(table => !existingTables.includes(table));
    if (missingTables.length > 0) {
      missingTables.forEach(table => {
        printWarning(`Table missing: ${table}`);
      });
      printInfo('Run database migrations to create missing tables');
    }

    // Check table record counts (if tables exist)
    if (existingTables.length > 0) {
      console.log('\n📊 Table record counts:');
      for (const table of existingTables) {
        try {
          const countResult = await client.query(`SELECT COUNT(*) as count FROM ${table}`);
          const count = countResult.rows[0].count;
          printInfo(`${table}: ${count} records`);
        } catch (error) {
          printWarning(`${table}: Unable to count records (${error.message})`);
        }
      }
    }

    // Test write permissions
    console.log('\n✍️  Testing write permissions...');
    try {
      await client.query('BEGIN');
      await client.query(`
        CREATE TEMP TABLE test_write_permissions (
          id SERIAL PRIMARY KEY,
          test_data TEXT
        )
      `);
      await client.query(`INSERT INTO test_write_permissions (test_data) VALUES ('test')`);
      await client.query('ROLLBACK');
      printSuccess('Write permissions: OK');
    } catch (error) {
      printError(`Write permissions: FAILED (${error.message})`);
    }

    client.release();

    // Summary
    console.log('\n📊 Database Check Summary:');
    printSuccess('Database connection: OK');
    printSuccess(`PostgreSQL version: ${postgresVersion}`);
    printSuccess(`Tables found: ${existingTables.length}/${expectedTables.length}`);
    
    if (missingTables.length === 0) {
      printSuccess('All expected tables are present');
    } else {
      printWarning(`Missing tables: ${missingTables.join(', ')}`);
      console.log('\n💡 Next Steps:');
      console.log('1. Run: npm run db:generate');
      console.log('2. Run: npm run db:migrate');
      console.log('3. Verify: npm run db:check');
    }

    console.log('\n🎉 Database check completed successfully!');

  } catch (error) {
    printError(`Database connection failed: ${error.message}`);
    
    console.log('\n🔧 Troubleshooting Guide:');
    console.log('1. Verify DATABASE_URL is correct');
    console.log('2. Check if database server is running');
    console.log('3. Verify network connectivity');
    console.log('4. Check firewall settings');
    console.log('5. Validate database credentials');
    
    if (error.code === 'ENOTFOUND') {
      printError('DNS resolution failed - check hostname');
    } else if (error.code === 'ECONNREFUSED') {
      printError('Connection refused - check if database server is running');
    } else if (error.code === '28P01') {
      printError('Authentication failed - check username/password');
    } else if (error.code === '3D000') {
      printError('Database does not exist - create the database first');
    }
    
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  printError(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// Run the check
checkDatabase().catch(error => {
  printError(`Unexpected error: ${error.message}`);
  process.exit(1);
});
