#!/usr/bin/env tsx

/**
 * Complete Multi-Tenant Security Setup Script
 * 
 * This script performs the complete security setup process:
 * 1. Runs database migration to add company_id to all tables
 * 2. Tests multi-tenant security implementation
 * 3. Provides comprehensive security status report
 * 
 * 🛡️ CRITICAL SECURITY SETUP 🛡️
 * This script completes the multi-tenant security implementation
 * and verifies that all vulnerabilities have been resolved.
 * 
 * Usage: npm run setup:security
 */

import { runMultiTenantMigration, printMigrationSummary } from './migrate-multi-tenant'
import { runSecurityTests, printSecurityResults } from './test-multi-tenant-security'

interface SecuritySetupResults {
  migrationCompleted: boolean
  migrationErrors: string[]
  securityTestsPassed: boolean
  securityVulnerabilities: string[]
  overallStatus: 'SECURE' | 'VULNERABLE' | 'ERROR'
  recommendations: string[]
}

async function completeSecuritySetup(): Promise<SecuritySetupResults> {
  const results: SecuritySetupResults = {
    migrationCompleted: false,
    migrationErrors: [],
    securityTestsPassed: false,
    securityVulnerabilities: [],
    overallStatus: 'ERROR',
    recommendations: []
  }

  console.log('🛡️  MANUFACTURING ERP - COMPLETE SECURITY SETUP')
  console.log('===============================================')
  console.log('🚀 Starting comprehensive multi-tenant security implementation...')
  console.log('')

  try {
    // Step 1: Run Database Migration
    console.log('📊 STEP 1: DATABASE MIGRATION')
    console.log('==============================')
    
    const migrationStats = await runMultiTenantMigration()
    printMigrationSummary(migrationStats)
    
    if (migrationStats.errors.length === 0) {
      results.migrationCompleted = true
      console.log('✅ Migration completed successfully!')
    } else {
      results.migrationErrors = migrationStats.errors
      console.log('❌ Migration completed with errors!')
    }

    console.log('')
    console.log('⏳ Waiting 2 seconds for database to settle...')
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Step 2: Run Security Tests
    console.log('🔍 STEP 2: SECURITY TESTING')
    console.log('============================')
    
    const securityResults = await runSecurityTests()
    printSecurityResults(securityResults)
    
    if (securityResults.testsFailed === 0 && securityResults.vulnerabilities.length === 0) {
      results.securityTestsPassed = true
      console.log('✅ All security tests passed!')
    } else {
      results.securityVulnerabilities = securityResults.vulnerabilities
      console.log('❌ Security vulnerabilities detected!')
    }

    // Step 3: Overall Assessment
    console.log('')
    console.log('📋 STEP 3: OVERALL SECURITY ASSESSMENT')
    console.log('======================================')
    
    if (results.migrationCompleted && results.securityTestsPassed) {
      results.overallStatus = 'SECURE'
      results.recommendations = [
        'Complete remaining API endpoint security (purchase contracts, invoices, etc.)',
        'Update frontend components to handle tenant-scoped data',
        'Perform comprehensive user acceptance testing',
        'Deploy to production with confidence'
      ]
    } else if (results.migrationCompleted && !results.securityTestsPassed) {
      results.overallStatus = 'VULNERABLE'
      results.recommendations = [
        'Fix identified security vulnerabilities before proceeding',
        'Re-run security tests until all pass',
        'DO NOT deploy to production until secure'
      ]
    } else {
      results.overallStatus = 'ERROR'
      results.recommendations = [
        'Fix migration errors before proceeding',
        'Ensure database schema is properly updated',
        'Contact development team for assistance'
      ]
    }

  } catch (error) {
    console.error('💥 Critical error during security setup:', error)
    results.overallStatus = 'ERROR'
    results.recommendations = [
      'Review error logs and fix critical issues',
      'Ensure database is accessible and writable',
      'Contact development team for assistance'
    ]
  }

  return results
}

function printFinalSecurityReport(results: SecuritySetupResults) {
  console.log('')
  console.log('🛡️  FINAL SECURITY REPORT')
  console.log('=========================')
  
  // Migration Status
  console.log(`📊 Migration Status: ${results.migrationCompleted ? '✅ COMPLETED' : '❌ FAILED'}`)
  if (results.migrationErrors.length > 0) {
    console.log('   Migration Errors:')
    results.migrationErrors.forEach(error => console.log(`   - ${error}`))
  }
  
  // Security Test Status
  console.log(`🔍 Security Tests: ${results.securityTestsPassed ? '✅ PASSED' : '❌ FAILED'}`)
  if (results.securityVulnerabilities.length > 0) {
    console.log('   Security Vulnerabilities:')
    results.securityVulnerabilities.forEach(vuln => console.log(`   - ${vuln}`))
  }
  
  // Overall Status
  console.log('')
  console.log(`🎯 OVERALL STATUS: ${getStatusEmoji(results.overallStatus)} ${results.overallStatus}`)
  
  if (results.overallStatus === 'SECURE') {
    console.log('')
    console.log('🎉 CONGRATULATIONS! 🎉')
    console.log('Your Manufacturing ERP system now has ENTERPRISE-GRADE MULTI-TENANT SECURITY!')
    console.log('')
    console.log('✅ Critical vulnerabilities have been resolved')
    console.log('✅ Database schema properly implements tenant isolation')
    console.log('✅ API endpoints are secured with Auth0 authentication')
    console.log('✅ All security tests are passing')
    console.log('')
    console.log('🚀 Your system is ready for production deployment!')
  } else if (results.overallStatus === 'VULNERABLE') {
    console.log('')
    console.log('⚠️  WARNING: SECURITY ISSUES DETECTED')
    console.log('Your system still has security vulnerabilities that must be fixed.')
    console.log('DO NOT deploy to production until all security tests pass.')
  } else {
    console.log('')
    console.log('💥 ERROR: SETUP INCOMPLETE')
    console.log('Critical errors prevented the security setup from completing.')
    console.log('Please review the errors and try again.')
  }
  
  // Recommendations
  console.log('')
  console.log('📋 NEXT STEPS:')
  results.recommendations.forEach((rec, index) => {
    console.log(`   ${index + 1}. ${rec}`)
  })
  
  console.log('')
  console.log('📚 For detailed information, see: MULTI_TENANT_SECURITY_FIX.md')
  console.log('')
}

function getStatusEmoji(status: string): string {
  switch (status) {
    case 'SECURE': return '🟢'
    case 'VULNERABLE': return '🟡'
    case 'ERROR': return '🔴'
    default: return '⚪'
  }
}

// Run complete security setup if called directly
if (require.main === module) {
  completeSecuritySetup()
    .then(printFinalSecurityReport)
    .then(() => {
      console.log('🛡️  Security setup process completed.')
      process.exit(0)
    })
    .catch(error => {
      console.error('💥 Security setup failed:', error)
      process.exit(1)
    })
}

export { completeSecuritySetup, printFinalSecurityReport }
