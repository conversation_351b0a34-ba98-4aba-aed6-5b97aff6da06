#!/usr/bin/env tsx

/**
 * Create Test User Company
 * 
 * This script creates a company record <NAME_EMAIL>
 * to fix the multi-tenant isolation issue.
 */

import { db, uid } from '../lib/db'
import { companies } from '../lib/schema-postgres'
import { eq } from 'drizzle-orm'

async function createTestUserCompany() {
  console.log('🏢 CREATING COMPANY FOR TEST USER')
  console.log('=================================')
  
  const testUserEmail = '<EMAIL>'
  const testAuth0Id = 'auth0|testuser_example_com' // Realistic Auth0 ID format
  
  try {
    // Check if company already exists
    const existingCompany = await db.query.companies.findFirst({
      where: eq(companies.email, testUserEmail)
    })
    
    if (existingCompany) {
      console.log(`✅ Company already exists for ${testUserEmail}`)
      console.log(`   Company: ${existingCompany.name}`)
      console.log(`   ID: ${existingCompany.id}`)
      console.log(`   Auth0 ID: ${existingCompany.auth0_user_id}`)
      return existingCompany
    }
    
    // Create new company
    console.log(`🆕 Creating new company for ${testUserEmail}`)
    
    const companyId = uid('company')
    const now = new Date()
    const newCompany = {
      id: companyId,
      auth0_user_id: testAuth0Id,
      name: 'Testuser\'s Company',
      display_name: 'Testuser\'s Company',
      email: testUserEmail,
      onboarding_completed: 'true', // Mark as completed for testing
      onboarding_step: 'completed',
      status: 'active' as const,
      created_at: now,
      updated_at: now
    }
    
    await db.insert(companies).values(newCompany)
    
    console.log(`✅ Created company successfully!`)
    console.log(`   Company: ${newCompany.name}`)
    console.log(`   ID: ${newCompany.id}`)
    console.log(`   Auth0 ID: ${newCompany.auth0_user_id}`)
    
    return newCompany
    
  } catch (error) {
    console.error('❌ Error creating test user company:', error)
    throw error
  }
}

async function verifyMultiTenantSetup() {
  console.log('')
  console.log('🔍 VERIFYING MULTI-TENANT SETUP')
  console.log('===============================')
  
  try {
    const allCompanies = await db.query.companies.findMany()
    
    console.log(`📊 Total companies: ${allCompanies.length}`)
    console.log('')
    
    allCompanies.forEach((company, index) => {
      const userType = company.email === '<EMAIL>' ? '👤 Original User' :
                      company.email === '<EMAIL>' ? '🧪 Test User' :
                      company.email === '<EMAIL>' ? '🎭 Samovic User' :
                      '❓ Other User'
      
      console.log(`${index + 1}. ${company.name} - ${userType}`)
      console.log(`   Email: ${company.email}`)
      console.log(`   Company ID: ${company.id}`)
      console.log(`   Auth0 ID: ${company.auth0_user_id}`)
      console.log(`   Onboarding: ${company.onboarding_completed}`)
      console.log('')
    })
    
    // Check for unique company IDs
    const companyIds = allCompanies.map(c => c.id)
    const uniqueCompanyIds = new Set(companyIds)
    
    if (companyIds.length === uniqueCompanyIds.size) {
      console.log('✅ All companies have unique IDs')
    } else {
      console.log('❌ Duplicate company IDs detected!')
    }
    
    // Check for unique Auth0 IDs
    const auth0Ids = allCompanies.map(c => c.auth0_user_id).filter(Boolean)
    const uniqueAuth0Ids = new Set(auth0Ids)
    
    if (auth0Ids.length === uniqueAuth0Ids.size) {
      console.log('✅ All companies have unique Auth0 IDs')
    } else {
      console.log('❌ Duplicate Auth0 IDs detected!')
    }
    
  } catch (error) {
    console.error('❌ Error verifying setup:', error)
  }
}

async function provideTestingInstructions() {
  console.log('')
  console.log('🧪 TESTING INSTRUCTIONS')
  console.log('=======================')
  console.log('')
  console.log('Now that the test user has a company record:')
  console.log('')
  console.log('1. 🔐 Open Chrome incognito mode')
  console.log('2. 🌐 Navigate to: http://localhost:3000')
  console.log('3. 🔑 Login as: <EMAIL> (password: TestPassword123!)')
  console.log('4. 📊 Expected: Dashboard shows 0 customers, 0 products, 0 suppliers')
  console.log('5. 🔄 Open new browser window (not incognito)')
  console.log('6. 🔑 Login as: <EMAIL>')
  console.log('7. ✅ Expected: Original data still visible and isolated')
  console.log('')
  console.log('🎯 SUCCESS CRITERIA:')
  console.log('- <EMAIL> sees empty dashboard')
  console.log('- <EMAIL> sees their existing data')
  console.log('- No 401 errors in browser console')
  console.log('- Perfect multi-tenant isolation')
  console.log('')
  console.log('📊 VERIFICATION COMMANDS:')
  console.log('- npm run test:comprehensive-security')
  console.log('- npm run diagnose:isolation')
  console.log('')
}

async function main() {
  try {
    console.log('🚀 TEST USER COMPANY CREATION')
    console.log('=============================')
    
    await createTestUserCompany()
    await verifyMultiTenantSetup()
    await provideTestingInstructions()
    
    console.log('🎉 Test user company creation completed!')
    console.log('')
    console.log('🎯 NEXT STEP: Test the multi-tenant isolation as instructed above')
    
  } catch (error) {
    console.error('💥 Script failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
