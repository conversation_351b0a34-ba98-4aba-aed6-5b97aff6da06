#!/usr/bin/env node

/**
 * Manufacturing ERP Environment Variables Check
 * Validates that all required environment variables are set
 */

const requiredEnvVars = [
  'DATABASE_URL',
  'AUTH0_SECRET',
  'AUTH0_BASE_URL',
  'AUTH0_ISSUER_BASE_URL',
  'AUTH0_CLIENT_ID',
  'AUTH0_CLIENT_SECRET'
];

const optionalEnvVars = [
  'NEXT_PUBLIC_APP_URL',
  'NODE_ENV',
  'NEXT_PUBLIC_ENABLE_DEBUG',
  'SENTRY_DSN'
];

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function printSuccess(message) {
  console.log(`${colors.green}✅ ${message}${colors.reset}`);
}

function printError(message) {
  console.log(`${colors.red}❌ ${message}${colors.reset}`);
}

function printWarning(message) {
  console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

function printInfo(message) {
  console.log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
}

function checkEnvironmentVariables() {
  console.log('🔍 Checking Manufacturing ERP environment variables...\n');

  // Check required variables
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  const present = requiredEnvVars.filter(envVar => process.env[envVar]);

  // Display results for required variables
  console.log('📋 Required Environment Variables:');
  present.forEach(envVar => {
    const value = process.env[envVar];
    const displayValue = envVar.includes('SECRET') || envVar.includes('PASSWORD') 
      ? '***HIDDEN***' 
      : value.length > 50 
        ? `${value.substring(0, 50)}...` 
        : value;
    printSuccess(`${envVar}: ${displayValue}`);
  });

  missing.forEach(envVar => {
    printError(`${envVar}: NOT SET`);
  });

  // Check optional variables
  console.log('\n📋 Optional Environment Variables:');
  optionalEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      const value = process.env[envVar];
      const displayValue = value.length > 50 ? `${value.substring(0, 50)}...` : value;
      printSuccess(`${envVar}: ${displayValue}`);
    } else {
      printWarning(`${envVar}: NOT SET (optional)`);
    }
  });

  // Environment-specific checks
  console.log('\n🌍 Environment-Specific Checks:');
  
  const nodeEnv = process.env.NODE_ENV || 'development';
  printInfo(`NODE_ENV: ${nodeEnv}`);

  // Check Auth0 configuration consistency
  if (process.env.AUTH0_BASE_URL && process.env.AUTH0_ISSUER_BASE_URL) {
    const baseUrl = process.env.AUTH0_BASE_URL;
    const issuerUrl = process.env.AUTH0_ISSUER_BASE_URL;
    
    if (baseUrl.includes('localhost') && !issuerUrl.includes('localhost')) {
      printSuccess('Auth0 configuration: Development setup detected');
    } else if (!baseUrl.includes('localhost') && !issuerUrl.includes('localhost')) {
      printSuccess('Auth0 configuration: Production setup detected');
    } else {
      printWarning('Auth0 configuration: Mixed local/production URLs detected');
    }
  }

  // Check database URL format
  if (process.env.DATABASE_URL) {
    const dbUrl = process.env.DATABASE_URL;
    if (dbUrl.startsWith('postgresql://')) {
      printSuccess('Database URL: PostgreSQL format detected');
      
      if (dbUrl.includes('localhost')) {
        printInfo('Database: Local development database');
      } else {
        printInfo('Database: Remote/production database');
      }
    } else {
      printWarning('Database URL: Unexpected format (should start with postgresql://)');
    }
  }

  // Summary
  console.log('\n📊 Summary:');
  if (missing.length === 0) {
    printSuccess(`All ${requiredEnvVars.length} required environment variables are set`);
    printInfo('Environment configuration is ready for development/deployment');
    return true;
  } else {
    printError(`${missing.length} required environment variables are missing`);
    printError('Please set the missing variables before proceeding');
    
    console.log('\n💡 Quick Setup Guide:');
    console.log('1. Copy .env.example to .env.local (if available)');
    console.log('2. Fill in the missing environment variables');
    console.log('3. Refer to docs/ENVIRONMENT_SETUP.md for detailed instructions');
    
    return false;
  }
}

// Run the check
const isValid = checkEnvironmentVariables();
process.exit(isValid ? 0 : 1);
