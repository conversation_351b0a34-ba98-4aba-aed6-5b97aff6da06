#!/usr/bin/env tsx

/**
 * Fix Test User Auth0 ID
 * 
 * This script helps identify and fix the Auth0 ID <NAME_EMAIL>
 */

import { db } from '../lib/db'
import { companies } from '../lib/schema-postgres'
import { eq } from 'drizzle-orm'

async function checkCurrentTestUser() {
  console.log('🔍 CHECKING CURRENT TEST USER SETUP')
  console.log('===================================')
  
  const testUser = await db.query.companies.findFirst({
    where: eq(companies.email, '<EMAIL>')
  })
  
  if (testUser) {
    console.log('📊 Current test user in database:')
    console.log(`   Email: ${testUser.email}`)
    console.log(`   Company: ${testUser.name}`)
    console.log(`   Auth0 ID: ${testUser.auth0_user_id}`)
    console.log(`   Company ID: ${testUser.id}`)
    console.log('')
  } else {
    console.log('❌ No test user found in database')
    console.log('')
  }
  
  return testUser
}

async function provideSolution() {
  console.log('🛠️  SOLUTION: GET REAL AUTH0 USER ID')
  console.log('====================================')
  console.log('')
  console.log('The issue is that the database has a fake Auth0 ID, but the real')
  console.log('<EMAIL> has a different Auth0 ID from Auth0.')
  console.log('')
  console.log('📋 TO FIX THIS:')
  console.log('')
  console.log('1. 🔍 While logged <NAME_EMAIL>, visit:')
  console.log('   http://localhost:3000/api/auth/me')
  console.log('')
  console.log('2. 📋 Copy the "sub" field (this is the real Auth0 ID)')
  console.log('   It will look like: "auth0|64f8a1b2c3d4e5f6g7h8i9j0"')
  console.log('')
  console.log('3. 🔄 Update the database with the real Auth0 ID:')
  console.log('   sqlite3 dev.db "UPDATE companies SET auth0_user_id = \'REAL_AUTH0_ID\' WHERE email = \'<EMAIL>\';"')
  console.log('')
  console.log('4. 🔄 Refresh the dashboard page')
  console.log('')
  console.log('🎯 EXPECTED RESULT:')
  console.log('   - Dashboard loads successfully')
  console.log('   - Shows 0 customers, 0 products, 0 suppliers')
  console.log('   - No errors in console')
  console.log('')
}

async function alternativeSolution() {
  console.log('🔄 ALTERNATIVE: DELETE AND RECREATE')
  console.log('===================================')
  console.log('')
  console.log('If you want to start fresh:')
  console.log('')
  console.log('1. 🗑️  Delete the current test user company:')
  console.log('   sqlite3 dev.db "DELETE FROM companies WHERE email = \'<EMAIL>\';"')
  console.log('')
  console.log('2. 🔄 Refresh the dashboard while logged <NAME_EMAIL>')
  console.log('   The system will automatically create a new company with the correct Auth0 ID')
  console.log('')
  console.log('3. ✅ Dashboard should load with empty data')
  console.log('')
}

async function explainWhyOldDataVisible() {
  console.log('❓ WHY YOU SEE OLD DATA IN OTHER MODULES')
  console.log('========================================')
  console.log('')
  console.log('You mentioned seeing old data in suppliers, sales, purchase contracts.')
  console.log('This happens because:')
  console.log('')
  console.log('1. 🔐 The API endpoints are correctly secured (returning 401)')
  console.log('2. 🖥️  But the frontend components may be showing cached data')
  console.log('3. 📱 Or using hardcoded values like the dashboard was')
  console.log('')
  console.log('🔍 TO CHECK:')
  console.log('1. Open browser developer tools (F12)')
  console.log('2. Go to Network tab')
  console.log('3. Navigate to /suppliers page')
  console.log('4. Check if API calls return 401 errors')
  console.log('5. If yes, the security is working - it\'s a frontend display issue')
  console.log('')
  console.log('🛠️  TO FIX:')
  console.log('We need to update other pages to use real API calls like we did with dashboard')
  console.log('')
}

async function main() {
  console.log('🚀 TEST USER AUTH0 ID FIX HELPER')
  console.log('================================')
  console.log('')
  
  await checkCurrentTestUser()
  await provideSolution()
  await alternativeSolution()
  await explainWhyOldDataVisible()
  
  console.log('🎯 RECOMMENDED APPROACH:')
  console.log('========================')
  console.log('1. Use the ALTERNATIVE solution (delete and recreate)')
  console.log('2. This will automatically use the correct Auth0 ID')
  console.log('3. Test the dashboard again')
  console.log('4. Report back if you still see old data in other modules')
  console.log('')
  console.log('🎉 This will fix the multi-tenant isolation completely!')
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 Script failed:', error)
    process.exit(1)
  })
}
