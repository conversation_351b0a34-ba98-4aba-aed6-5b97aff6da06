# 🗄️ PostgreSQL Setup Guide - Manufacturing ERP Migration

This guide provides multiple options for setting up PostgreSQL for the Manufacturing ERP system migration.

## 📋 **Setup Options**

### **Option 1: Docker Setup (Recommended)**

#### **Prerequisites**
- Docker Desktop installed
- Docker Compose available

#### **Quick Start**
```bash
# 1. Start PostgreSQL services
./scripts/docker-postgres.sh start

# 2. Check service status
./scripts/docker-postgres.sh status

# 3. Connect to PostgreSQL
./scripts/docker-postgres.sh connect
```

#### **Services Included**
- **PostgreSQL 15**: Main database server (port 5432)
- **pgAdmin 4**: Web-based database management (port 5050)
- **Redis**: Caching and session storage (port 6379)

#### **Access Information**
- **PostgreSQL**: `localhost:5432`
  - Database: `manufacturing_erp`
  - Username: `erp_user`
  - Password: `SecureERP2024!`

- **pgAdmin**: `http://localhost:5050`
  - Email: `<EMAIL>`
  - Password: `AdminERP2024!`

---

### **Option 2: Homebrew Installation (macOS)**

#### **Installation**
```bash
# Install PostgreSQL 15
brew install postgresql@15

# Start PostgreSQL service
brew services start postgresql@15

# Create database and user
createdb manufacturing_erp
createuser -s erp_user
```

#### **Configuration**
```bash
# Set password for user
psql -d manufacturing_erp -c "ALTER USER erp_user PASSWORD 'SecureERP2024!';"

# Run initialization script
psql -d manufacturing_erp -U erp_user -f docker/postgres/init/01-init-manufacturing-erp.sql
```

---

### **Option 3: Cloud PostgreSQL (Production-Ready)**

#### **Supabase Setup (Recommended)**
1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Note down connection details
4. Update `.env.local` with connection string

#### **Connection String Format**
```
DATABASE_URL="postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres"
```

---

## 🔧 **Environment Configuration**

### **Development Environment**
Create `.env.local` file:
```bash
# PostgreSQL Connection
DATABASE_URL=postgresql://erp_user:SecureERP2024!@localhost:5432/manufacturing_erp

# Migration Control
USE_POSTGRESQL=false
USE_SQLITE=true
ENABLE_DUAL_DATABASE=false

# Development Settings
LOG_LEVEL=debug
ENABLE_QUERY_LOGGING=true
```

### **Docker Environment**
The `.env.docker` file is already configured with optimal settings.

---

## 🧪 **Testing the Setup**

### **Connection Test**
```bash
# Test PostgreSQL connection
psql -h localhost -p 5432 -U erp_user -d manufacturing_erp -c "SELECT version();"
```

### **Performance Test**
```bash
# Run performance benchmark
psql -h localhost -p 5432 -U erp_user -d manufacturing_erp -c "
SELECT 
    setting as max_connections,
    current_setting('shared_buffers') as shared_buffers,
    current_setting('effective_cache_size') as effective_cache_size
FROM pg_settings WHERE name = 'max_connections';
"
```

---

## 📊 **Database Management**

### **Backup and Restore**
```bash
# Create backup
./scripts/docker-postgres.sh backup

# Restore from backup
./scripts/docker-postgres.sh restore backup_20241218_120000.sql
```

### **Monitoring**
```bash
# View service logs
./scripts/docker-postgres.sh logs postgres

# Check service status
./scripts/docker-postgres.sh status
```

---

## 🔍 **Troubleshooting**

### **Common Issues**

#### **Docker Not Found**
```bash
# Install Docker Desktop from https://docker.com
# Or use Homebrew installation method
```

#### **Port Already in Use**
```bash
# Check what's using port 5432
lsof -i :5432

# Stop conflicting service
brew services stop postgresql
```

#### **Permission Denied**
```bash
# Fix directory permissions
chmod -R 755 docker/volumes/
```

### **Connection Issues**
```bash
# Test connection
telnet localhost 5432

# Check PostgreSQL logs
./scripts/docker-postgres.sh logs postgres
```

---

## 🚀 **Next Steps**

After successful PostgreSQL setup:

1. ✅ **Phase 1.1 Complete**: Docker PostgreSQL Container Setup
2. ➡️ **Phase 1.2**: Environment Variables Configuration
3. ➡️ **Phase 1.3**: PostgreSQL Connection Testing
4. ➡️ **Phase 1.4**: Development Database Initialization

---

## 📚 **Additional Resources**

- [PostgreSQL Documentation](https://www.postgresql.org/docs/15/)
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [pgAdmin Documentation](https://www.pgadmin.org/docs/)
- [Supabase Documentation](https://supabase.com/docs)

---

## 🛡️ **Security Notes**

- Default passwords are for development only
- Change passwords for production use
- Enable SSL for production connections
- Regularly update PostgreSQL version
- Monitor access logs and audit trails

---

## 💡 **Performance Optimization**

The PostgreSQL configuration includes:
- **Connection pooling**: 200 max connections
- **Memory optimization**: 256MB shared buffers
- **Query optimization**: Optimized for SSD storage
- **Multi-tenant indexing**: Prepared for company_id filtering
- **Audit logging**: Full modification tracking
