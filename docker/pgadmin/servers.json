{"Servers": {"1": {"Name": "Manufacturing ERP - PostgreSQL", "Group": "Development", "Host": "postgres", "Port": 5432, "MaintenanceDB": "manufacturing_erp", "Username": "erp_user", "Password": "SecureERP2024!", "SSLMode": "prefer", "SSLCert": "<STORAGE_DIR>/.postgresql/postgresql.crt", "SSLKey": "<STORAGE_DIR>/.postgresql/postgresql.key", "SSLRootCert": "<STORAGE_DIR>/.postgresql/root.crt", "SSLCrl": "<STORAGE_DIR>/.postgresql/root.crl", "SSLCompression": 0, "Timeout": 10, "UseSSHTunnel": 0, "TunnelHost": "", "TunnelPort": "22", "TunnelUsername": "", "TunnelAuthentication": 0, "BGColor": "#3498db", "FGColor": "#ffffff", "Service": "", "Passfile": "", "Comment": "Manufacturing ERP PostgreSQL Database - Development Environment"}}}