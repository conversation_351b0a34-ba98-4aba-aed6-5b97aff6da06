-- Manufacturing ERP Database Initialization Script
-- PostgreSQL 15 optimized for multi-tenant Manufacturing ERP system

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create archive directory for WAL archiving
\! mkdir -p /var/lib/postgresql/archive

-- Set up database-level configurations
ALTER DATABASE manufacturing_erp SET timezone TO 'UTC';
ALTER DATABASE manufacturing_erp SET default_text_search_config TO 'pg_catalog.english';

-- Create custom schema for ERP functions
CREATE SCHEMA IF NOT EXISTS erp_functions;

-- Grant permissions to erp_user
GRANT ALL PRIVILEGES ON DATABASE manufacturing_erp TO erp_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO erp_user;
GRANT ALL PRIVILEGES ON SCHEMA erp_functions TO erp_user;

-- Create function for generating UUIDs with prefixes (matching current system)
CREATE OR REPLACE FUNCTION erp_functions.generate_id(prefix TEXT DEFAULT 'id')
RETURNS TEXT AS $$
BEGIN
    RETURN prefix || '_' || REPLACE(gen_random_uuid()::TEXT, '-', '');
END;
$$ LANGUAGE plpgsql;

-- Create function for multi-tenant security validation
CREATE OR REPLACE FUNCTION erp_functions.validate_company_access(
    user_company_id TEXT,
    record_company_id TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Ensure user can only access their company's data
    RETURN user_company_id = record_company_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function for audit logging
CREATE OR REPLACE FUNCTION erp_functions.audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Log all modifications for compliance
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (table_name, operation, record_id, company_id, changed_at)
        VALUES (TG_TABLE_NAME, 'INSERT', NEW.id, NEW.company_id, NOW());
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (table_name, operation, record_id, company_id, changed_at)
        VALUES (TG_TABLE_NAME, 'UPDATE', NEW.id, NEW.company_id, NOW());
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (table_name, operation, record_id, company_id, changed_at)
        VALUES (TG_TABLE_NAME, 'DELETE', OLD.id, OLD.company_id, NOW());
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create audit log table
CREATE TABLE IF NOT EXISTS audit_log (
    id SERIAL PRIMARY KEY,
    table_name TEXT NOT NULL,
    operation TEXT NOT NULL,
    record_id TEXT NOT NULL,
    company_id TEXT NOT NULL,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id TEXT
);

-- Create indexes for audit log
CREATE INDEX IF NOT EXISTS idx_audit_log_company_id ON audit_log(company_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_changed_at ON audit_log(changed_at);

-- Create function for full-text search optimization
CREATE OR REPLACE FUNCTION erp_functions.create_search_vector(
    name TEXT,
    description TEXT DEFAULT '',
    additional_text TEXT DEFAULT ''
)
RETURNS tsvector AS $$
BEGIN
    RETURN to_tsvector('english', 
        COALESCE(name, '') || ' ' || 
        COALESCE(description, '') || ' ' || 
        COALESCE(additional_text, '')
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Performance optimization: Create custom indexes for multi-tenant queries
-- These will be applied after schema migration

-- Grant execute permissions on functions
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA erp_functions TO erp_user;

-- Set up connection pooling parameters
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';

-- Reload configuration
SELECT pg_reload_conf();

-- Create development user for testing (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'erp_dev') THEN
        CREATE ROLE erp_dev WITH LOGIN PASSWORD 'DevERP2024!';
        GRANT CONNECT ON DATABASE manufacturing_erp TO erp_dev;
        GRANT USAGE ON SCHEMA public TO erp_dev;
        GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO erp_dev;
        GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO erp_dev;
    END IF;
END
$$;

-- Log successful initialization
INSERT INTO audit_log (table_name, operation, record_id, company_id, changed_at, user_id)
VALUES ('system', 'INIT', 'database', 'system', NOW(), 'postgres')
ON CONFLICT DO NOTHING;

-- Display initialization summary
\echo 'Manufacturing ERP PostgreSQL Database Initialized Successfully!'
\echo 'Extensions: uuid-ossp, pg_stat_statements, pg_trgm, btree_gin'
\echo 'Custom Functions: generate_id, validate_company_access, audit_trigger'
\echo 'Audit Logging: Enabled'
\echo 'Multi-tenant Security: Configured'
\echo 'Performance Optimization: Applied'
