# PostgreSQL Configuration for Manufacturing ERP System
# Optimized for development and production workloads

#------------------------------------------------------------------------------
# CONNECTIONS AND AUTHENTICATION
#------------------------------------------------------------------------------

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 200                    # Increased for multi-tenant usage
superuser_reserved_connections = 3

# Authentication
authentication_timeout = 1min
password_encryption = scram-sha-256

#------------------------------------------------------------------------------
# RESOURCE USAGE (except WAL)
#------------------------------------------------------------------------------

# Memory Settings (optimized for 8GB+ systems)
shared_buffers = 256MB                   # 25% of RAM for development
effective_cache_size = 1GB               # Available system cache
work_mem = 4MB                          # Per-operation memory
maintenance_work_mem = 64MB             # Maintenance operations
max_worker_processes = 8
max_parallel_workers = 4
max_parallel_workers_per_gather = 2

# Background Writer
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0

#------------------------------------------------------------------------------
# WRITE AHEAD LOG (WAL)
#------------------------------------------------------------------------------

# WAL Settings for reliability
wal_level = replica
wal_buffers = 16MB
checkpoint_completion_target = 0.9
max_wal_size = 1GB
min_wal_size = 80MB

# Archive settings (for backup)
archive_mode = on
archive_command = 'test ! -f /var/lib/postgresql/archive/%f && cp %p /var/lib/postgresql/archive/%f'

#------------------------------------------------------------------------------
# REPLICATION
#------------------------------------------------------------------------------

# Replication settings (for future scaling)
max_wal_senders = 3
wal_keep_size = 128MB

#------------------------------------------------------------------------------
# QUERY TUNING
#------------------------------------------------------------------------------

# Query Planning
random_page_cost = 1.1                  # SSD optimization
effective_io_concurrency = 200          # SSD concurrent I/O
seq_page_cost = 1.0

# Query Execution
default_statistics_target = 100
constraint_exclusion = partition
cursor_tuple_fraction = 0.1

#------------------------------------------------------------------------------
# ERROR REPORTING AND LOGGING
#------------------------------------------------------------------------------

# Logging Configuration
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_file_mode = 0600
log_truncate_on_rotation = on
log_rotation_age = 1d
log_rotation_size = 100MB

# What to Log
log_min_messages = warning
log_min_error_statement = error
log_min_duration_statement = 1000       # Log slow queries (1 second)

# Log Content
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_statement = 'mod'                   # Log modifications
log_temp_files = 10MB
log_autovacuum_min_duration = 0

#------------------------------------------------------------------------------
# RUNTIME STATISTICS
#------------------------------------------------------------------------------

# Statistics Collection
track_activities = on
track_counts = on
track_io_timing = on
track_functions = pl
stats_temp_directory = '/var/run/postgresql/stats_temp'

#------------------------------------------------------------------------------
# AUTOVACUUM PARAMETERS
#------------------------------------------------------------------------------

# Autovacuum Settings (optimized for multi-tenant)
autovacuum = on
log_autovacuum_min_duration = 0
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1
autovacuum_freeze_max_age = 200000000
autovacuum_multixact_freeze_max_age = 400000000
autovacuum_vacuum_cost_delay = 20ms
autovacuum_vacuum_cost_limit = 200

#------------------------------------------------------------------------------
# CLIENT CONNECTION DEFAULTS
#------------------------------------------------------------------------------

# Locale and Formatting
datestyle = 'iso, mdy'
timezone = 'UTC'
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'

# Default Text Search Configuration
default_text_search_config = 'pg_catalog.english'

# Connection Defaults
default_transaction_isolation = 'read committed'
statement_timeout = 30s                 # Prevent runaway queries
lock_timeout = 10s                      # Prevent lock waits
idle_in_transaction_session_timeout = 60s

#------------------------------------------------------------------------------
# MANUFACTURING ERP SPECIFIC SETTINGS
#------------------------------------------------------------------------------

# Multi-tenant optimization
enable_partitionwise_join = on
enable_partitionwise_aggregate = on

# JSON optimization (for contract templates and flexible data)
gin_pending_list_limit = 4MB

# Full-text search optimization (for product/customer search)
default_text_search_config = 'pg_catalog.english'

#------------------------------------------------------------------------------
# EXTENSIONS
#------------------------------------------------------------------------------

# Preload libraries for performance
shared_preload_libraries = 'pg_stat_statements'

# Extension settings
pg_stat_statements.max = 10000
pg_stat_statements.track = all
