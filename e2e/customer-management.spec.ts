// E2E tests for customer management workflow

import { test, expect } from '@playwright/test'

test.describe('Customer Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to customers page
    await page.goto('/customers')
    
    // Wait for page to load
    await page.waitForLoadState('networkidle')
  })

  test('should display customers list', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Customers/)

    // Check main heading
    await expect(page.locator('h1')).toContainText('Customers')

    // Check if table is present
    await expect(page.locator('[data-testid="customers-table"]')).toBeVisible()

    // Check if add customer button is present
    await expect(page.locator('[data-testid="add-customer-btn"]')).toBeVisible()
  })

  test('should create a new customer', async ({ page }) => {
    // Click add customer button
    await page.click('[data-testid="add-customer-btn"]')

    // Wait for form to appear
    await expect(page.locator('[data-testid="customer-form"]')).toBeVisible()

    // Fill in customer details
    await page.fill('[data-testid="customer-name"]', 'Test Customer E2E')
    await page.fill('[data-testid="customer-contact-name"]', 'John Doe')
    await page.fill('[data-testid="customer-contact-email"]', '<EMAIL>')
    await page.fill('[data-testid="customer-contact-phone"]', '+1234567890')
    await page.fill('[data-testid="customer-address"]', '123 Test Street, Test City')

    // Submit form
    await page.click('[data-testid="submit-customer-btn"]')

    // Wait for success message
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('[data-testid="success-toast"]')).toContainText('Customer created successfully')

    // Verify customer appears in list
    await expect(page.locator('[data-testid="customers-table"]')).toContainText('Test Customer E2E')
  })

  test('should validate required fields', async ({ page }) => {
    // Click add customer button
    await page.click('[data-testid="add-customer-btn"]')

    // Try to submit without filling required fields
    await page.click('[data-testid="submit-customer-btn"]')

    // Check for validation errors
    await expect(page.locator('[data-testid="name-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="name-error"]')).toContainText('Name is required')
  })

  test('should validate email format', async ({ page }) => {
    // Click add customer button
    await page.click('[data-testid="add-customer-btn"]')

    // Fill in invalid email
    await page.fill('[data-testid="customer-name"]', 'Test Customer')
    await page.fill('[data-testid="customer-contact-email"]', 'invalid-email')

    // Submit form
    await page.click('[data-testid="submit-customer-btn"]')

    // Check for email validation error
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="email-error"]')).toContainText('Invalid email format')
  })

  test('should edit existing customer', async ({ page }) => {
    // Assume there's at least one customer in the list
    // Click edit button for first customer
    await page.click('[data-testid="edit-customer-btn"]:first-child')

    // Wait for edit form
    await expect(page.locator('[data-testid="customer-form"]')).toBeVisible()

    // Update customer name
    await page.fill('[data-testid="customer-name"]', 'Updated Customer Name')

    // Submit form
    await page.click('[data-testid="submit-customer-btn"]')

    // Wait for success message
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('[data-testid="success-toast"]')).toContainText('Customer updated successfully')

    // Verify updated name appears in list
    await expect(page.locator('[data-testid="customers-table"]')).toContainText('Updated Customer Name')
  })

  test('should delete customer with confirmation', async ({ page }) => {
    // Click delete button for first customer
    await page.click('[data-testid="delete-customer-btn"]:first-child')

    // Wait for confirmation dialog
    await expect(page.locator('[data-testid="delete-confirmation"]')).toBeVisible()
    await expect(page.locator('[data-testid="delete-confirmation"]')).toContainText('Are you sure you want to delete this customer?')

    // Confirm deletion
    await page.click('[data-testid="confirm-delete-btn"]')

    // Wait for success message
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('[data-testid="success-toast"]')).toContainText('Customer deleted successfully')
  })

  test('should cancel deletion', async ({ page }) => {
    // Get initial customer count
    const initialCount = await page.locator('[data-testid="customer-row"]').count()

    // Click delete button for first customer
    await page.click('[data-testid="delete-customer-btn"]:first-child')

    // Wait for confirmation dialog
    await expect(page.locator('[data-testid="delete-confirmation"]')).toBeVisible()

    // Cancel deletion
    await page.click('[data-testid="cancel-delete-btn"]')

    // Verify dialog is closed
    await expect(page.locator('[data-testid="delete-confirmation"]')).not.toBeVisible()

    // Verify customer count unchanged
    const finalCount = await page.locator('[data-testid="customer-row"]').count()
    expect(finalCount).toBe(initialCount)
  })

  test('should search customers', async ({ page }) => {
    // Type in search box
    await page.fill('[data-testid="search-input"]', 'Test Customer')

    // Wait for search results
    await page.waitForTimeout(500) // Debounce delay

    // Verify filtered results
    const visibleRows = page.locator('[data-testid="customer-row"]:visible')
    await expect(visibleRows.first()).toContainText('Test Customer')
  })

  test('should filter customers by status', async ({ page }) => {
    // Click status filter dropdown
    await page.click('[data-testid="status-filter"]')

    // Select "Active" status
    await page.click('[data-testid="status-option-active"]')

    // Verify filtered results
    const statusCells = page.locator('[data-testid="customer-status"]')
    const count = await statusCells.count()
    
    for (let i = 0; i < count; i++) {
      await expect(statusCells.nth(i)).toContainText('Active')
    }
  })

  test('should handle bulk operations', async ({ page }) => {
    // Select multiple customers
    await page.check('[data-testid="select-customer"]:first-child')
    await page.check('[data-testid="select-customer"]:nth-child(2)')

    // Verify bulk actions toolbar appears
    await expect(page.locator('[data-testid="bulk-actions-toolbar"]')).toBeVisible()
    await expect(page.locator('[data-testid="selected-count"]')).toContainText('2 customers selected')

    // Test bulk delete
    await page.click('[data-testid="bulk-delete-btn"]')

    // Confirm bulk deletion
    await expect(page.locator('[data-testid="bulk-delete-confirmation"]')).toBeVisible()
    await page.click('[data-testid="confirm-bulk-delete-btn"]')

    // Wait for success message
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('[data-testid="success-toast"]')).toContainText('2 customers deleted successfully')
  })

  test('should export customers data', async ({ page }) => {
    // Start download promise before clicking
    const downloadPromise = page.waitForEvent('download')

    // Click export button
    await page.click('[data-testid="export-customers-btn"]')

    // Wait for download
    const download = await downloadPromise

    // Verify download
    expect(download.suggestedFilename()).toMatch(/customers.*\.csv/)
  })

  test('should handle pagination', async ({ page }) => {
    // Check if pagination is present (assuming there are enough customers)
    const pagination = page.locator('[data-testid="pagination"]')
    
    if (await pagination.isVisible()) {
      // Click next page
      await page.click('[data-testid="next-page-btn"]')

      // Verify page changed
      await expect(page.locator('[data-testid="current-page"]')).toContainText('2')

      // Click previous page
      await page.click('[data-testid="prev-page-btn"]')

      // Verify back to first page
      await expect(page.locator('[data-testid="current-page"]')).toContainText('1')
    }
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Verify mobile layout
    await expect(page.locator('[data-testid="mobile-customer-card"]')).toBeVisible()
    
    // Verify mobile menu works
    await page.click('[data-testid="mobile-menu-btn"]')
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
  })

  test('should handle keyboard shortcuts', async ({ page }) => {
    // Test Ctrl+N for new customer
    await page.keyboard.press('Control+n')
    await expect(page.locator('[data-testid="customer-form"]')).toBeVisible()

    // Close form
    await page.keyboard.press('Escape')
    await expect(page.locator('[data-testid="customer-form"]')).not.toBeVisible()

    // Test Ctrl+F for search focus
    await page.keyboard.press('Control+f')
    await expect(page.locator('[data-testid="search-input"]')).toBeFocused()
  })

  test('should handle network errors gracefully', async ({ page }) => {
    // Intercept API calls and simulate network error
    await page.route('/api/customers', route => {
      route.abort('failed')
    })

    // Try to load customers
    await page.reload()

    // Verify error message is displayed
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Failed to load customers')

    // Verify retry button is available
    await expect(page.locator('[data-testid="retry-btn"]')).toBeVisible()
  })

  test('should maintain form state during navigation', async ({ page }) => {
    // Start creating a customer
    await page.click('[data-testid="add-customer-btn"]')
    await page.fill('[data-testid="customer-name"]', 'Partial Customer')

    // Navigate away and back
    await page.goto('/products')
    await page.goto('/customers')

    // Verify form state is preserved (if implemented)
    // This would depend on your state management implementation
  })
})
