// Playwright global setup

import { FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('Setting up E2E test environment...')
  
  // Set up test database
  process.env.NODE_ENV = 'test'
  process.env.DATABASE_URL = 'file:./e2e-test.db'
  
  // You can add any global setup logic here
  // For example:
  // - Setting up test database
  // - Starting external services
  // - Creating test users
  
  console.log('E2E test environment setup complete')
}

export default globalSetup
