# Manufacturing ERP Environment Setup Guide

## 🎯 Overview

This guide ensures consistent environment configuration between local development and production deployment, preventing environment-specific issues.

---

## **🔧 LOCAL DEVELOPMENT ENVIRONMENT**

### **Prerequisites**
- **Node.js**: v18.0.0 or higher
- **npm**: v8.0.0 or higher
- **Git**: Latest version
- **PostgreSQL**: v15+ (local instance)
- **VS Code**: Recommended IDE

### **Required VS Code Extensions**
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag"
  ]
}
```

### **Local Environment Variables (.env.local)**
```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/manufacturing_erp

# Auth0 Configuration
AUTH0_SECRET=your-auth0-secret-key-32-characters-minimum
AUTH0_BASE_URL=http://localhost:3000
AUTH0_ISSUER_BASE_URL=https://your-domain.auth0.com
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_CLIENT_SECRET=your-auth0-client-secret

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Optional: Development Tools
NEXT_PUBLIC_ENABLE_DEBUG=true
```

### **Local Database Setup**
```bash
# Install PostgreSQL (macOS)
brew install postgresql
brew services start postgresql

# Create database
createdb manufacturing_erp

# Create user (optional)
psql -c "CREATE USER erp_user WITH PASSWORD 'your_password';"
psql -c "GRANT ALL PRIVILEGES ON DATABASE manufacturing_erp TO erp_user;"
```

---

## **🚀 PRODUCTION ENVIRONMENT (VERCEL)**

### **Environment Variables (Vercel Dashboard)**
```bash
# Database Configuration
DATABASE_URL=postgresql://user:pass@host:port/database

# Auth0 Configuration
AUTH0_SECRET=production-auth0-secret-key-32-characters-minimum
AUTH0_BASE_URL=https://your-domain.vercel.app
AUTH0_ISSUER_BASE_URL=https://your-domain.auth0.com
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_CLIENT_SECRET=your-auth0-client-secret

# Application Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NODE_ENV=production

# Optional: Production Monitoring
SENTRY_DSN=your-sentry-dsn
NEXT_PUBLIC_ENABLE_DEBUG=false
```

### **Vercel Configuration (vercel.json)**
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "regions": ["iad1"],
  "env": {
    "NODE_ENV": "production"
  }
}
```

---

## **🔐 AUTH0 CONFIGURATION**

### **Local Development Settings**
```json
{
  "name": "Manufacturing ERP (Development)",
  "domain": "your-domain.auth0.com",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "audience": "https://your-domain.auth0.com/api/v2/",
  "scope": "openid profile email",
  "redirectUri": "http://localhost:3000/api/auth/callback",
  "postLogoutRedirectUri": "http://localhost:3000",
  "session": {
    "cookieSecret": "32-character-secret-key",
    "cookieDomain": "localhost",
    "cookieSecure": false
  }
}
```

### **Production Settings**
```json
{
  "name": "Manufacturing ERP (Production)",
  "domain": "your-domain.auth0.com",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "audience": "https://your-domain.auth0.com/api/v2/",
  "scope": "openid profile email",
  "redirectUri": "https://your-domain.vercel.app/api/auth/callback",
  "postLogoutRedirectUri": "https://your-domain.vercel.app",
  "session": {
    "cookieSecret": "32-character-secret-key",
    "cookieDomain": "your-domain.vercel.app",
    "cookieSecure": true
  }
}
```

---

## **💾 DATABASE CONFIGURATION**

### **Local PostgreSQL Setup**
```sql
-- Create database
CREATE DATABASE manufacturing_erp;

-- Create user
CREATE USER erp_user WITH PASSWORD 'secure_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE manufacturing_erp TO erp_user;
GRANT ALL ON SCHEMA public TO erp_user;
```

### **Production Database (Supabase)**
```bash
# Connection string format
postgresql://postgres:[password]@[host]:5432/postgres

# SSL Configuration
?sslmode=require

# Connection pooling
?pgbouncer=true&connection_limit=10
```

---

## **📦 PACKAGE CONFIGURATION**

### **package.json Scripts**
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "db:generate": "drizzle-kit generate:pg",
    "db:migrate": "drizzle-kit push:pg",
    "db:studio": "drizzle-kit studio",
    "db:check": "node scripts/check-db.js"
  }
}
```

### **TypeScript Configuration (tsconfig.json)**
```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

---

## **🔍 ENVIRONMENT VALIDATION**

### **Environment Check Script (scripts/check-env.js)**
```javascript
const requiredEnvVars = [
  'DATABASE_URL',
  'AUTH0_SECRET',
  'AUTH0_BASE_URL',
  'AUTH0_ISSUER_BASE_URL',
  'AUTH0_CLIENT_ID',
  'AUTH0_CLIENT_SECRET'
];

console.log('🔍 Checking environment variables...');

const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missing.length > 0) {
  console.error('❌ Missing environment variables:');
  missing.forEach(envVar => console.error(`  - ${envVar}`));
  process.exit(1);
} else {
  console.log('✅ All required environment variables are set');
}
```

### **Database Connection Check (scripts/check-db.js)**
```javascript
const { Pool } = require('pg');

async function checkDatabase() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    console.log('✅ Database connection successful');
    console.log(`🕐 Database time: ${result.rows[0].now}`);
    client.release();
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

checkDatabase();
```

---

## **🚀 DEPLOYMENT CHECKLIST**

### **Pre-Deployment Verification**
- [ ] All environment variables set in Vercel
- [ ] Database connection string updated
- [ ] Auth0 callback URLs configured
- [ ] SSL certificates valid
- [ ] Domain configuration correct

### **Post-Deployment Verification**
- [ ] Application loads successfully
- [ ] Authentication flow works
- [ ] Database operations functional
- [ ] API endpoints responding
- [ ] Error monitoring active

---

## **🛠️ TROUBLESHOOTING**

### **Common Environment Issues**

1. **Database Connection Errors**
   ```bash
   # Check connection string format
   # Verify database credentials
   # Ensure database server is running
   # Check firewall settings
   ```

2. **Auth0 Authentication Issues**
   ```bash
   # Verify callback URLs match exactly
   # Check client ID and secret
   # Ensure domain configuration is correct
   # Validate session secret length (32+ chars)
   ```

3. **Build/Deployment Failures**
   ```bash
   # Check TypeScript compilation
   # Verify all dependencies installed
   # Ensure environment variables are set
   # Check for syntax errors
   ```

### **Environment Parity Checklist**
- [ ] Node.js versions match
- [ ] npm versions compatible
- [ ] Environment variables identical (except URLs)
- [ ] Database schemas synchronized
- [ ] Auth0 configurations aligned
- [ ] SSL/TLS settings appropriate

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-20  
**Next Review**: 2025-02-20
