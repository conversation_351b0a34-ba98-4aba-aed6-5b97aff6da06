version: '3.8'

services:
  # PostgreSQL 15 Database for Manufacturing ERP
  postgres:
    image: postgres:15-alpine
    container_name: manufacturing-erp-postgres
    restart: unless-stopped
    environment:
      # Database Configuration
      POSTGRES_DB: manufacturing_erp
      POSTGRES_USER: erp_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-SecureERP2024!}
      
      # Performance Tuning
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      
    ports:
      - "5432:5432"
    
    volumes:
      # Persistent data storage
      - postgres_data:/var/lib/postgresql/data
      
      # Custom PostgreSQL configuration
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      
      # Initialization scripts
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c log_statement=all
      -c log_destination=stderr
      -c logging_collector=on
      -c log_directory=/var/log/postgresql
      -c log_filename=postgresql-%Y-%m-%d.log
    
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp_user -d manufacturing_erp"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    
    networks:
      - erp_network

  # pgAdmin for Database Management (Development Only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: manufacturing-erp-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-AdminERP2024!}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    
    ports:
      - "5050:80"
    
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./docker/pgadmin/servers.json:/pgadmin4/servers.json
    
    depends_on:
      postgres:
        condition: service_healthy
    
    networks:
      - erp_network

  # Redis for Session Storage and Caching (Future Enhancement)
  redis:
    image: redis:7-alpine
    container_name: manufacturing-erp-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-RedisERP2024!}
    
    ports:
      - "6379:6379"
    
    volumes:
      - redis_data:/data
    
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    
    networks:
      - erp_network

volumes:
  # Persistent storage volumes
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./docker/volumes/postgres
  
  pgadmin_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./docker/volumes/pgadmin
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./docker/volumes/redis

networks:
  erp_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
