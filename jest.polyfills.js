// Jest polyfills for Node.js environment

// Polyfill for TextEncoder/TextDecoder
const { TextEncoder, TextDecoder } = require('util')

global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Polyfill for fetch (if not using node-fetch)
if (!global.fetch) {
  global.fetch = require('node-fetch')
}

// Polyfill for URL and URLSearchParams
if (!global.URL) {
  global.URL = require('url').URL
}

if (!global.URLSearchParams) {
  global.URLSearchParams = require('url').URLSearchParams
}

// Polyfill for crypto
if (!global.crypto) {
  global.crypto = require('crypto').webcrypto
}

// Polyfill for performance
if (!global.performance) {
  global.performance = require('perf_hooks').performance
}

// Polyfill for AbortController
if (!global.AbortController) {
  global.AbortController = require('abort-controller').AbortController
}

// Polyfill for FormData
if (!global.FormData) {
  global.FormData = require('form-data')
}

// Polyfill for Headers
if (!global.Headers) {
  global.Headers = require('node-fetch').Headers
}

// Polyfill for Request
if (!global.Request) {
  global.Request = require('node-fetch').Request
}

// Polyfill for Response
if (!global.Response) {
  global.Response = require('node-fetch').Response
}
