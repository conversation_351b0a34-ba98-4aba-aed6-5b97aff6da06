{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/next"}], "env": {"DATABASE_URL": "postgresql://postgres.jwdryepnhrigricdxenc:<EMAIL>:6543/postgres", "DATABASE_URL_POSTGRESQL": "postgresql://postgres.jwdryepnhrigricdxenc:<EMAIL>:6543/postgres", "USE_POSTGRESQL": "true", "MIGRATION_PHASE": "production", "MIGRATION_DEBUG": "false", "DEPLOYMENT_TIMESTAMP": "2025-08-19T10:50:00Z", "ENFORCE_TENANT_ISOLATION": "true", "ENABLE_AUDIT_LOGGING": "true", "LOG_LEVEL": "info", "NODE_ENV": "production", "AUTH0_SECRET": "****************************************************************", "AUTH0_BASE_URL": "https://silk-road-john.vercel.app", "AUTH0_ISSUER_BASE_URL": "https://dev-tejx02ztaj7oufoc.us.auth0.com", "AUTH0_CLIENT_ID": "To2oAQX05DtstsgKXdLvsUYAaRO23QCK", "AUTH0_CLIENT_SECRET": "****************************************************************", "SENTRY_ORG": "dassodev", "SENTRY_PROJECT": "manufacturing-erp", "SENTRY_DSN": "https://<EMAIL>/4509869772374016", "NEXT_PUBLIC_SENTRY_DSN": "https://<EMAIL>/4509869772374016"}}