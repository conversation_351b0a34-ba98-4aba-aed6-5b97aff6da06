import { NextRequest } from "next/server"
import { createSystemError, <PERSON>rrorC<PERSON> } from "./errors"

// Simple CSRF token implementation
// In production, consider using a more robust library like 'csrf'

/**
 * Generate a CSRF token
 */
export function generateCSRFToken(): string {
  // Generate a random token (in production, use crypto.randomBytes)
  const timestamp = Date.now().toString()
  const random = Math.random().toString(36).substring(2)
  return Buffer.from(`${timestamp}-${random}`).toString('base64')
}

/**
 * Verify CSRF token
 */
export function verifyCSRFToken(token: string, maxAge = 3600000): boolean {
  try {
    const decoded = Buffer.from(token, 'base64').toString()
    const [timestamp] = decoded.split('-')
    
    const tokenTime = parseInt(timestamp, 10)
    const now = Date.now()
    
    // Check if token is within valid time window (default 1 hour)
    return (now - tokenTime) <= maxAge
  } catch {
    return false
  }
}

/**
 * Extract CSRF token from request
 */
export function getCSRFTokenFromRequest(request: NextRequest): string | null {
  // Check X-CSRF-Token header
  const headerToken = request.headers.get('x-csrf-token')
  if (headerToken) {
    return headerToken
  }
  
  // Check form data for POST requests
  if (request.method === 'POST') {
    // Note: This would need to be implemented with form parsing
    // For now, we'll rely on header-based tokens
  }
  
  return null
}

/**
 * Validate CSRF token for state-changing operations
 */
export function validateCSRFToken(request: NextRequest): void {
  // Skip CSRF validation for GET, HEAD, OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
    return
  }
  
  // Skip for API routes that use Bearer token authentication
  const authHeader = request.headers.get('authorization')
  if (authHeader?.startsWith('Bearer ')) {
    return
  }
  
  const token = getCSRFTokenFromRequest(request)
  
  if (!token) {
    throw createSystemError(
      ErrorCode.AUTHORIZATION_ERROR,
      "CSRF token is required for this operation"
    )
  }
  
  if (!verifyCSRFToken(token)) {
    throw createSystemError(
      ErrorCode.AUTHORIZATION_ERROR,
      "Invalid or expired CSRF token"
    )
  }
}

/**
 * Middleware wrapper to add CSRF protection
 */
export function withCSRFProtection<T extends any[]>(
  handler: (...args: T) => Promise<Response>
) {
  return async (request: NextRequest, ...args: T): Promise<Response> => {
    try {
      validateCSRFToken(request)
      return await handler(request, ...args)
    } catch (error) {
      const { createErrorResponse } = await import("./api-helpers")
      return createErrorResponse(error)
    }
  }
}

/**
 * Generate CSRF token for client-side use
 */
export function getCSRFTokenForClient(): string {
  if (typeof window === 'undefined') {
    return ''
  }
  
  // Check if token already exists in sessionStorage
  let token = sessionStorage.getItem('csrf-token')
  
  if (!token || !verifyCSRFToken(token)) {
    // Generate new token
    token = generateCSRFToken()
    sessionStorage.setItem('csrf-token', token)
  }
  
  return token
}

/**
 * Add CSRF token to fetch requests
 */
export function addCSRFTokenToHeaders(headers: HeadersInit = {}): HeadersInit {
  const token = getCSRFTokenForClient()
  
  return {
    ...headers,
    'X-CSRF-Token': token
  }
}

/**
 * Fetch wrapper with automatic CSRF token inclusion
 */
export async function fetchWithCSRF(
  url: string, 
  options: RequestInit = {}
): Promise<Response> {
  const headers = addCSRFTokenToHeaders(options.headers)
  
  return fetch(url, {
    ...options,
    headers
  })
}

/**
 * React hook for CSRF token management
 */
export function useCSRFToken() {
  if (typeof window === 'undefined') {
    return { token: '', addToHeaders: (h: HeadersInit) => h }
  }
  
  const token = getCSRFTokenForClient()
  
  const addToHeaders = (headers: HeadersInit = {}): HeadersInit => {
    return addCSRFTokenToHeaders(headers)
  }
  
  return { token, addToHeaders }
}
