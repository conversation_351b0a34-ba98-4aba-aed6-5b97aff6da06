export type ID = string

export type Sample = {
  id: ID
  code: string
  name: string
  image?: string
  specification?: string
  date?: string // ISO
  moq?: number
  availableFromStock?: boolean
  adminNotes?: string
}

export type Contact = {
  name: string
  phone?: string
  email?: string
}

export type Customer = {
  id: ID
  name: string
  contact?: Contact
  address?: string
  taxId?: string
  bank?: string
  incoterm?: string
  paymentTerm?: string
  status?: "active" | "inactive"
}

export type Supplier = {
  id: ID
  name: string
  contact?: Contact
  address?: string
  taxId?: string
  bank?: string
  status?: "active" | "inactive"
}

export type Product = {
  id: ID
  sku: string
  name: string
  unit: string
  hsCode?: string
  origin?: string
  package?: string
  image?: string
}

export type ContractTemplate = {
  id: ID
  type: "sales" | "purchase"
  name: string
  language: "en" | "zh"
  body: string
  version: number
}

export type SalesContract = {
  id: ID
  number: string
  customerId: ID
  date: string
  templateId?: ID
  status: "draft" | "review" | "approved" | "active"
  currency: string
  items: Array<{ productId: ID; qty: number; price: number }>
}

export type PurchaseContract = {
  id: ID
  number: string
  supplierId: ID
  date: string
  templateId?: ID
  status: "draft" | "review" | "approved" | "active"
  currency: string
  items: Array<{ productId: ID; qty: number; price: number }>
}

export type WorkOrder = {
  id: ID
  number: string
  salesContractId: ID
  productId: ID
  qty: number
  operations: Array<{
    name: string
    status: "not-started" | "in-progress" | "done"
    startedAt?: string
    completedAt?: string
  }>
  qc?: {
    status: "pending" | "pass" | "fail"
    reportUrl?: string
    notes?: string
  }
}

export type StockLot = {
  id: ID
  productId: ID
  qty: number
  location: string
  note?: string
  createdAt: string
}

export type StockTxn = {
  id: ID
  type: "inbound" | "outbound"
  productId: ID
  qty: number
  location: string
  ref?: string
  createdAt: string
}

export type Declaration = {
  id: ID
  number: string
  date: string
  status: "draft" | "submitted" | "cleared" | "exception"
  items: Array<{ productId: ID; qty: number; hsCode?: string }>
  packingPhotoUrls?: string[]
  taxRefundInfo?: string
}

export type ARInvoice = {
  id: ID
  number: string
  customerId: ID
  date: string
  currency: string
  amount: number
  received: number
}

export type APInvoice = {
  id: ID
  number: string
  supplierId: ID
  date: string
  currency: string
  amount: number
  paid: number
}
