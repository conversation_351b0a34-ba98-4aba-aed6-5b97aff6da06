/**
 * Frontend Security Utilities
 * 
 * Additional security measures for the frontend to handle edge cases
 * and provide better user experience during authentication transitions.
 */

import { useUser } from '@auth0/nextjs-auth0/client'

/**
 * Hook to validate user session and company context
 */
export function useSecureSession() {
  const { user, error, isLoading } = useUser()
  
  return {
    user,
    error,
    isLoading,
    isAuthenticated: !!user && !error,
    userId: user?.sub,
    userEmail: user?.email,
  }
}

/**
 * Clear all local storage and session storage
 * Useful when switching between users or debugging
 */
export function clearAllBrowserData() {
  if (typeof window !== 'undefined') {
    // Clear local storage
    localStorage.clear()
    
    // Clear session storage
    sessionStorage.clear()
    
    // Clear any cached data
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name)
        })
      })
    }
    
    console.log('🧹 Browser data cleared for fresh session')
  }
}

/**
 * Validate API response contains only single company data
 */
export function validateTenantIsolation(data: any[], fieldName = 'company_id'): {
  isValid: boolean
  companyId: string | null
  issues: string[]
} {
  const issues: string[] = []
  
  if (!Array.isArray(data)) {
    return { isValid: true, companyId: null, issues: [] }
  }
  
  if (data.length === 0) {
    return { isValid: true, companyId: null, issues: [] }
  }
  
  // Check all records have company_id
  const recordsWithoutCompanyId = data.filter(record => !record[fieldName])
  if (recordsWithoutCompanyId.length > 0) {
    issues.push(`${recordsWithoutCompanyId.length} records missing ${fieldName}`)
  }
  
  // Check all records have same company_id
  const companyIds = [...new Set(data.map(record => record[fieldName]).filter(Boolean))]
  if (companyIds.length > 1) {
    issues.push(`Multiple company IDs detected: ${companyIds.join(', ')}`)
  }
  
  const isValid = issues.length === 0
  const companyId = companyIds.length === 1 ? companyIds[0] : null
  
  if (!isValid) {
    console.error('🚨 Tenant isolation violation detected:', issues)
  }
  
  return { isValid, companyId, issues }
}

/**
 * Enhanced fetch wrapper with tenant validation
 */
export async function secureFetch(url: string, options: RequestInit = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    })
    
    if (!response.ok) {
      if (response.status === 401) {
        console.warn('🔐 Authentication required for:', url)
        // Could redirect to login or show auth prompt
        throw new Error('Authentication required')
      }
      
      if (response.status === 403) {
        console.warn('🚫 Access forbidden for:', url)
        throw new Error('Access forbidden')
      }
      
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    
    // Validate tenant isolation for array responses
    if (Array.isArray(data)) {
      const validation = validateTenantIsolation(data)
      if (!validation.isValid) {
        console.error('🚨 Tenant isolation violation in API response:', validation.issues)
        // In production, you might want to clear the data or show an error
      }
    }
    
    return data
  } catch (error) {
    console.error('🔥 Secure fetch error:', error)
    throw error
  }
}

/**
 * Debug helper to log current session state
 */
export function debugSessionState() {
  if (typeof window === 'undefined') return
  
  console.log('🔍 SESSION DEBUG INFO:')
  console.log('======================')
  console.log('URL:', window.location.href)
  console.log('Local Storage keys:', Object.keys(localStorage))
  console.log('Session Storage keys:', Object.keys(sessionStorage))
  console.log('Cookies:', document.cookie)
  
  // Check for Auth0 session
  const auth0Keys = Object.keys(localStorage).filter(key => key.includes('auth0'))
  if (auth0Keys.length > 0) {
    console.log('Auth0 storage keys:', auth0Keys)
  }
}

/**
 * Component wrapper for secure data loading
 */
export function withTenantValidation<T>(
  Component: React.ComponentType<T>
): React.ComponentType<T> {
  return function TenantValidatedComponent(props: T) {
    const { isAuthenticated, isLoading } = useSecureSession()
    
    if (isLoading) {
      return <div>Loading...</div>
    }
    
    if (!isAuthenticated) {
      return <div>Please log in to access this content.</div>
    }
    
    return <Component {...props} />
  }
}
