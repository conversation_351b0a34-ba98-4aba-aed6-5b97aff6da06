// Error type definitions for the ERP system

export enum ErrorCode {
  // Validation errors
  VALIDATION_ERROR = "VALIDATION_ERROR",
  REQUIRED_FIELD = "REQUIRED_FIELD",
  INVALID_FORMAT = "INVALID_FORMAT",

  // Business logic errors
  INSUFFICIENT_STOCK = "INSUFFICIENT_STOCK",
  DUPLICATE_SKU = "DUPLICATE_SKU",
  INVALID_CONTRACT_STATUS = "INVALID_CONTRACT_STATUS",
  INVALID_WORKFLOW_TRANSITION = "INVALID_WORKFLOW_TRANSITION",
  REFERENCE_NOT_FOUND = "REFERENCE_NOT_FOUND",

  // Quality Control specific errors
  INSPECTION_NOT_FOUND = "INSPECTION_NOT_FOUND",
  DEFECT_NOT_FOUND = "DEFECT_NOT_FOUND",
  STANDARD_NOT_FOUND = "STANDARD_NOT_FOUND",
  CERTIFICATE_NOT_FOUND = "CERTIFICATE_NOT_FOUND",
  INVALID_INSPECTION_STATUS = "INVALID_INSPECTION_STATUS",
  INSPECTION_ALREADY_COMPLETED = "INSPECTION_ALREADY_COMPLETED",
  CANNOT_DELETE_COMPLETED_INSPECTION = "CANNOT_DELETE_COMPLETED_INSPECTION",
  DEFECT_ALREADY_RESOLVED = "DEFECT_ALREADY_RESOLVED",
  MISSING_QUALITY_STANDARD = "MISSING_QUALITY_STANDARD",
  INVALID_QUALITY_RESULT = "INVALID_QUALITY_RESULT",

  // System errors
  DATABASE_ERROR = "DATABASE_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
  AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
  AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR",

  // File/Upload errors
  FILE_TOO_LARGE = "FILE_TOO_LARGE",
  INVALID_FILE_TYPE = "INVALID_FILE_TYPE",
  UPLOAD_FAILED = "UPLOAD_FAILED"
}

export interface AppError {
  code: ErrorCode
  message: string
  details?: Record<string, any>
  field?: string
  timestamp: string
}

export class ValidationError extends Error {
  public readonly code = ErrorCode.VALIDATION_ERROR
  public readonly field?: string
  public readonly details?: Record<string, any>

  constructor(message: string, field?: string, details?: Record<string, any>) {
    super(message)
    this.name = "ValidationError"
    this.field = field
    this.details = details
  }
}

export class BusinessLogicError extends Error {
  public readonly code: ErrorCode
  public readonly details?: Record<string, any>

  constructor(code: ErrorCode, message: string, details?: Record<string, any>) {
    super(message)
    this.name = "BusinessLogicError"
    this.code = code
    this.details = details
  }
}

export class SystemError extends Error {
  public readonly code: ErrorCode
  public readonly details?: Record<string, any>

  constructor(code: ErrorCode, message: string, details?: Record<string, any>) {
    super(message)
    this.name = "SystemError"
    this.code = code
    this.details = details
  }
}

// Error message mappings for user-friendly display
export const ERROR_MESSAGES: Record<ErrorCode, string> = {
  [ErrorCode.VALIDATION_ERROR]: "Please check your input and try again",
  [ErrorCode.REQUIRED_FIELD]: "This field is required",
  [ErrorCode.INVALID_FORMAT]: "Please enter a valid format",

  [ErrorCode.INSUFFICIENT_STOCK]: "Not enough stock available for this operation",
  [ErrorCode.DUPLICATE_SKU]: "A product with this SKU already exists",
  [ErrorCode.INVALID_CONTRACT_STATUS]: "Cannot perform this action with the current contract status",
  [ErrorCode.INVALID_WORKFLOW_TRANSITION]: "Invalid status transition",
  [ErrorCode.REFERENCE_NOT_FOUND]: "Referenced item not found",

  [ErrorCode.INSPECTION_NOT_FOUND]: "Quality inspection not found",
  [ErrorCode.DEFECT_NOT_FOUND]: "Quality defect not found",
  [ErrorCode.STANDARD_NOT_FOUND]: "Quality standard not found",
  [ErrorCode.CERTIFICATE_NOT_FOUND]: "Quality certificate not found",
  [ErrorCode.INVALID_INSPECTION_STATUS]: "Invalid inspection status transition",
  [ErrorCode.INSPECTION_ALREADY_COMPLETED]: "Inspection has already been completed",
  [ErrorCode.CANNOT_DELETE_COMPLETED_INSPECTION]: "Cannot delete a completed inspection",
  [ErrorCode.DEFECT_ALREADY_RESOLVED]: "Defect has already been resolved",
  [ErrorCode.MISSING_QUALITY_STANDARD]: "No quality standard defined for this product",
  [ErrorCode.INVALID_QUALITY_RESULT]: "Invalid quality inspection result",

  [ErrorCode.DATABASE_ERROR]: "A database error occurred. Please try again",
  [ErrorCode.NETWORK_ERROR]: "Network error. Please check your connection",
  [ErrorCode.AUTHENTICATION_ERROR]: "Please log in to continue",
  [ErrorCode.AUTHORIZATION_ERROR]: "You don't have permission to perform this action",

  [ErrorCode.FILE_TOO_LARGE]: "File size is too large. Maximum size is 10MB",
  [ErrorCode.INVALID_FILE_TYPE]: "Invalid file type. Please upload a supported format",
  [ErrorCode.UPLOAD_FAILED]: "File upload failed. Please try again"
}

// Utility functions for error handling
export function createAppError(
  code: ErrorCode,
  message?: string,
  details?: Record<string, any>,
  field?: string
): AppError {
  return {
    code,
    message: message || ERROR_MESSAGES[code],
    details,
    field,
    timestamp: new Date().toISOString()
  }
}

export function isAppError(error: any): error is AppError {
  return error && typeof error === 'object' && 'code' in error && 'message' in error
}

export function formatErrorForUser(error: unknown): string {
  if (isAppError(error)) {
    return error.message
  }

  if (error instanceof ValidationError) {
    return error.message
  }

  if (error instanceof BusinessLogicError) {
    return ERROR_MESSAGES[error.code] || error.message
  }

  if (error instanceof SystemError) {
    return ERROR_MESSAGES[error.code] || "An unexpected error occurred"
  }

  if (error instanceof Error) {
    return error.message
  }

  return "An unexpected error occurred"
}

// Business logic error creators
export function createInsufficientStockError(productName: string, available: number, requested: number) {
  return new BusinessLogicError(
    ErrorCode.INSUFFICIENT_STOCK,
    `Insufficient stock for ${productName}. Available: ${available}, Requested: ${requested}`,
    { productName, available, requested }
  )
}

export function createDuplicateSKUError(sku: string) {
  return new BusinessLogicError(
    ErrorCode.DUPLICATE_SKU,
    `Product with SKU "${sku}" already exists`,
    { sku }
  )
}

export function createInvalidStatusTransitionError(from: string, to: string) {
  return new BusinessLogicError(
    ErrorCode.INVALID_WORKFLOW_TRANSITION,
    `Cannot change status from "${from}" to "${to}"`,
    { from, to }
  )
}

export function createReferenceNotFoundError(type: string, id: string) {
  return new BusinessLogicError(
    ErrorCode.REFERENCE_NOT_FOUND,
    `${type} with ID "${id}" not found`,
    { type, id }
  )
}

// System error creators
export function createDatabaseError(operation: string, details?: any) {
  return new SystemError(
    ErrorCode.DATABASE_ERROR,
    `Database error during ${operation}`,
    { operation, details }
  )
}

export function createAuthenticationError(message = "Authentication required") {
  return new SystemError(ErrorCode.AUTHENTICATION_ERROR, message)
}

export function createAuthorizationError(action: string) {
  return new SystemError(
    ErrorCode.AUTHORIZATION_ERROR,
    `Not authorized to ${action}`,
    { action }
  )
}

// Quality Control error creators
export function createInspectionNotFoundError(id: string) {
  return new BusinessLogicError(
    ErrorCode.INSPECTION_NOT_FOUND,
    `Quality inspection with ID "${id}" not found`,
    { inspectionId: id }
  )
}

export function createDefectNotFoundError(id: string) {
  return new BusinessLogicError(
    ErrorCode.DEFECT_NOT_FOUND,
    `Quality defect with ID "${id}" not found`,
    { defectId: id }
  )
}

export function createStandardNotFoundError(id: string) {
  return new BusinessLogicError(
    ErrorCode.STANDARD_NOT_FOUND,
    `Quality standard with ID "${id}" not found`,
    { standardId: id }
  )
}

export function createCertificateNotFoundError(id: string) {
  return new BusinessLogicError(
    ErrorCode.CERTIFICATE_NOT_FOUND,
    `Quality certificate with ID "${id}" not found`,
    { certificateId: id }
  )
}

export function createInvalidInspectionStatusError(currentStatus: string, targetStatus: string) {
  return new BusinessLogicError(
    ErrorCode.INVALID_INSPECTION_STATUS,
    `Cannot change inspection status from "${currentStatus}" to "${targetStatus}"`,
    { currentStatus, targetStatus }
  )
}

export function createInspectionAlreadyCompletedError(id: string) {
  return new BusinessLogicError(
    ErrorCode.INSPECTION_ALREADY_COMPLETED,
    `Inspection "${id}" has already been completed`,
    { inspectionId: id }
  )
}

export function createCannotDeleteCompletedInspectionError(id: string) {
  return new BusinessLogicError(
    ErrorCode.CANNOT_DELETE_COMPLETED_INSPECTION,
    `Cannot delete completed inspection "${id}"`,
    { inspectionId: id }
  )
}

export function createDefectAlreadyResolvedError(id: string) {
  return new BusinessLogicError(
    ErrorCode.DEFECT_ALREADY_RESOLVED,
    `Defect "${id}" has already been resolved`,
    { defectId: id }
  )
}

export function createMissingQualityStandardError(productId: string) {
  return new BusinessLogicError(
    ErrorCode.MISSING_QUALITY_STANDARD,
    `No quality standard defined for product "${productId}"`,
    { productId }
  )
}

export function createInvalidQualityResultError(result: string) {
  return new BusinessLogicError(
    ErrorCode.INVALID_QUALITY_RESULT,
    `Invalid quality result: "${result}". Must be "pass", "fail", or "conditional"`,
    { result }
  )
}

export function createInvalidWorkflowTransitionError(message: string) {
  return new BusinessLogicError(
    ErrorCode.INVALID_WORKFLOW_TRANSITION,
    message
  )
}
