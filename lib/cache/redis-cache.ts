// Redis caching implementation for performance optimization
// Note: This is a mock implementation for development
// In production, you would use actual Redis with ioredis or node-redis

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

class MockRedisCache {
  private cache: Map<string, CacheEntry<any>> = new Map()
  private defaultTTL = 300 // 5 minutes in seconds

  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }

    // Check if entry has expired
    const now = Date.now()
    if (now - entry.timestamp > entry.ttl * 1000) {
      this.cache.delete(key)
      return null
    }

    return entry.data as T
  }

  async set<T>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    const ttl = ttlSeconds || this.defaultTTL
    const entry: CacheEntry<T> = {
      data: value,
      timestamp: Date.now(),
      ttl
    }
    
    this.cache.set(key, entry)
  }

  async del(key: string): Promise<void> {
    this.cache.delete(key)
  }

  async exists(key: string): Promise<boolean> {
    const entry = this.cache.get(key)
    if (!entry) return false

    // Check if expired
    const now = Date.now()
    if (now - entry.timestamp > entry.ttl * 1000) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  async flush(): Promise<void> {
    this.cache.clear()
  }

  async keys(pattern: string): Promise<string[]> {
    const regex = new RegExp(pattern.replace('*', '.*'))
    return Array.from(this.cache.keys()).filter(key => regex.test(key))
  }

  // Get cache statistics
  getStats(): {
    size: number
    keys: string[]
    memoryUsage: string
  } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      memoryUsage: `${Math.round(JSON.stringify(Array.from(this.cache.entries())).length / 1024)}KB`
    }
  }
}

// Cache service with business logic
export class CacheService {
  private redis: MockRedisCache
  private keyPrefix: string

  constructor(keyPrefix = "erp:") {
    this.redis = new MockRedisCache()
    this.keyPrefix = keyPrefix
  }

  private buildKey(key: string): string {
    return `${this.keyPrefix}${key}`
  }

  // Generic cache methods
  async get<T>(key: string): Promise<T | null> {
    try {
      return await this.redis.get<T>(this.buildKey(key))
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error)
      return null
    }
  }

  async set<T>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    try {
      await this.redis.set(this.buildKey(key), value, ttlSeconds)
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error)
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redis.del(this.buildKey(key))
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error)
    }
  }

  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(this.buildKey(pattern))
      for (const key of keys) {
        await this.redis.del(key)
      }
    } catch (error) {
      console.error(`Cache invalidate pattern error for ${pattern}:`, error)
    }
  }

  // Business-specific cache methods
  
  // Customer caching
  async getCachedCustomers(): Promise<any[] | null> {
    return this.get<any[]>("customers:all")
  }

  async setCachedCustomers(customers: any[], ttl = 300): Promise<void> {
    await this.set("customers:all", customers, ttl)
  }

  async getCachedCustomer(id: string): Promise<any | null> {
    return this.get<any>(`customer:${id}`)
  }

  async setCachedCustomer(id: string, customer: any, ttl = 600): Promise<void> {
    await this.set(`customer:${id}`, customer, ttl)
  }

  async invalidateCustomerCache(id?: string): Promise<void> {
    if (id) {
      await this.del(`customer:${id}`)
    }
    await this.del("customers:all")
    await this.invalidatePattern("customers:*")
  }

  // Product caching
  async getCachedProducts(): Promise<any[] | null> {
    return this.get<any[]>("products:all")
  }

  async setCachedProducts(products: any[], ttl = 600): Promise<void> {
    await this.set("products:all", products, ttl)
  }

  async getCachedProduct(id: string): Promise<any | null> {
    return this.get<any>(`product:${id}`)
  }

  async setCachedProduct(id: string, product: any, ttl = 1200): Promise<void> {
    await this.set(`product:${id}`, product, ttl)
  }

  async invalidateProductCache(id?: string): Promise<void> {
    if (id) {
      await this.del(`product:${id}`)
    }
    await this.del("products:all")
    await this.invalidatePattern("products:*")
  }

  // Contract caching
  async getCachedContracts(type: "sales" | "purchase"): Promise<any[] | null> {
    return this.get<any[]>(`contracts:${type}:all`)
  }

  async setCachedContracts(type: "sales" | "purchase", contracts: any[], ttl = 300): Promise<void> {
    await this.set(`contracts:${type}:all`, contracts, ttl)
  }

  async getCachedContract(type: "sales" | "purchase", id: string): Promise<any | null> {
    return this.get<any>(`contract:${type}:${id}`)
  }

  async setCachedContract(type: "sales" | "purchase", id: string, contract: any, ttl = 600): Promise<void> {
    await this.set(`contract:${type}:${id}`, contract, ttl)
  }

  async invalidateContractCache(type?: "sales" | "purchase", id?: string): Promise<void> {
    if (type && id) {
      await this.del(`contract:${type}:${id}`)
    }
    if (type) {
      await this.del(`contracts:${type}:all`)
      await this.invalidatePattern(`contracts:${type}:*`)
    } else {
      await this.invalidatePattern("contracts:*")
    }
  }

  // Inventory caching
  async getCachedStockLevel(productId: string, location?: string): Promise<any | null> {
    const key = location ? `stock:${productId}:${location}` : `stock:${productId}`
    return this.get<any>(key)
  }

  async setCachedStockLevel(productId: string, stockData: any, location?: string, ttl = 60): Promise<void> {
    const key = location ? `stock:${productId}:${location}` : `stock:${productId}`
    await this.set(key, stockData, ttl)
  }

  async invalidateStockCache(productId?: string, location?: string): Promise<void> {
    if (productId && location) {
      await this.del(`stock:${productId}:${location}`)
    } else if (productId) {
      await this.invalidatePattern(`stock:${productId}*`)
    } else {
      await this.invalidatePattern("stock:*")
    }
  }

  // Dashboard caching
  async getCachedDashboardData(): Promise<any | null> {
    return this.get<any>("dashboard:data")
  }

  async setCachedDashboardData(data: any, ttl = 120): Promise<void> {
    await this.set("dashboard:data", data, ttl)
  }

  async invalidateDashboardCache(): Promise<void> {
    await this.del("dashboard:data")
    await this.invalidatePattern("dashboard:*")
  }

  // Search result caching
  async getCachedSearchResults(query: string, filters: string): Promise<any | null> {
    const key = `search:${Buffer.from(query + filters).toString('base64')}`
    return this.get<any>(key)
  }

  async setCachedSearchResults(query: string, filters: string, results: any, ttl = 180): Promise<void> {
    const key = `search:${Buffer.from(query + filters).toString('base64')}`
    await this.set(key, results, ttl)
  }

  // Statistics caching
  async getCachedStats(type: string): Promise<any | null> {
    return this.get<any>(`stats:${type}`)
  }

  async setCachedStats(type: string, stats: any, ttl = 300): Promise<void> {
    await this.set(`stats:${type}`, stats, ttl)
  }

  async invalidateStatsCache(type?: string): Promise<void> {
    if (type) {
      await this.del(`stats:${type}`)
    } else {
      await this.invalidatePattern("stats:*")
    }
  }

  // Cache warming
  async warmCache(): Promise<void> {
    console.log("Warming cache...")
    
    // This would typically pre-load frequently accessed data
    // For now, we'll just log the action
    console.log("Cache warming completed")
  }

  // Cache health check
  async healthCheck(): Promise<{
    status: "healthy" | "unhealthy"
    stats: any
    errors: string[]
  }> {
    const errors: string[] = []
    let status: "healthy" | "unhealthy" = "healthy"

    try {
      // Test basic operations
      await this.set("health:test", "ok", 10)
      const testValue = await this.get("health:test")
      
      if (testValue !== "ok") {
        errors.push("Cache read/write test failed")
        status = "unhealthy"
      }

      await this.del("health:test")

      const stats = this.redis.getStats()

      return {
        status,
        stats,
        errors
      }
    } catch (error) {
      errors.push(`Cache health check failed: ${error}`)
      return {
        status: "unhealthy",
        stats: { size: 0, keys: [], memoryUsage: "0KB" },
        errors
      }
    }
  }
}

// Export singleton instance
export const cacheService = new CacheService()

// Cache decorator for service methods
export function cached(ttl: number = 300) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`
      
      // Try to get from cache first
      const cached = await cacheService.get(cacheKey)
      if (cached !== null) {
        return cached
      }

      // Execute original method
      const result = await method.apply(this, args)
      
      // Cache the result
      await cacheService.set(cacheKey, result, ttl)
      
      return result
    }

    return descriptor
  }
}
