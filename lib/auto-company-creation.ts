/**
 * Auto Company Creation Middleware
 * 
 * This middleware automatically creates a company record when a new user
 * logs in for the first time, ensuring seamless multi-tenant isolation.
 */

import { db, uid } from './db'
import { companies } from './schema'
import { eq } from 'drizzle-orm'
import { getSession } from '@auth0/nextjs-auth0'

interface Auth0User {
  sub: string
  email?: string
  name?: string
  given_name?: string
  family_name?: string
  nickname?: string
}

/**
 * Automatically create a company record for new users
 */
export async function ensureCompanyExists(auth0User: Auth0User): Promise<string | null> {
  try {
    console.log(`🔍 Checking company for user: ${auth0User.email}`)
    
    // Check if company already exists
    const existingCompany = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, auth0User.sub)
    })
    
    if (existingCompany) {
      console.log(`✅ Company exists: ${existingCompany.name} (${existingCompany.id})`)
      return existingCompany.id
    }
    
    // Create new company automatically
    console.log(`🏢 Creating new company for user: ${auth0User.email}`)
    
    const companyId = uid('company')
    const companyName = generateCompanyName(auth0User)
    
    const newCompany = {
      id: companyId,
      auth0_user_id: auth0User.sub,
      name: companyName,
      display_name: companyName,
      email: auth0User.email || '',
      onboarding_completed: 'false', // User will complete onboarding later
      onboarding_step: 'basic_info',
      status: 'active' as const,
      created_at: Math.floor(Date.now() / 1000),
      updated_at: Math.floor(Date.now() / 1000)
    }
    
    await db.insert(companies).values(newCompany)
    
    console.log(`✅ Created company: ${companyName} (${companyId})`)
    return companyId
    
  } catch (error) {
    console.error('❌ Error ensuring company exists:', error)
    return null
  }
}

/**
 * Generate a default company name from user info
 */
function generateCompanyName(user: Auth0User): string {
  // Try to use user's name
  if (user.name) {
    return `${user.name}'s Company`
  }
  
  // Try to use given name
  if (user.given_name) {
    return `${user.given_name}'s Company`
  }
  
  // Try to use nickname
  if (user.nickname) {
    return `${user.nickname}'s Company`
  }
  
  // Use email prefix as fallback
  if (user.email) {
    const emailPrefix = user.email.split('@')[0]
    return `${emailPrefix}'s Company`
  }
  
  // Ultimate fallback
  return 'My Company'
}

/**
 * Middleware to ensure company exists for authenticated users
 */
export async function withAutoCompanyCreation<T extends any[]>(
  handler: (companyId: string, ...args: T) => Promise<Response>
) {
  return async (...args: T): Promise<Response> => {
    try {
      // Get current session
      const session = await getSession()
      
      if (!session?.user) {
        return new Response(
          JSON.stringify({ error: 'Unauthorized' }), 
          { status: 401, headers: { 'Content-Type': 'application/json' } }
        )
      }
      
      // Ensure company exists
      const companyId = await ensureCompanyExists(session.user as Auth0User)
      
      if (!companyId) {
        return new Response(
          JSON.stringify({ error: 'Failed to create company profile' }), 
          { status: 500, headers: { 'Content-Type': 'application/json' } }
        )
      }
      
      // Call the original handler with company ID
      return await handler(companyId, ...args)
      
    } catch (error) {
      console.error('Auto company creation middleware error:', error)
      return new Response(
        JSON.stringify({ error: 'Internal server error' }), 
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }
  }
}

/**
 * Check if user needs onboarding
 */
export async function checkOnboardingStatus(auth0UserId: string): Promise<{
  needsOnboarding: boolean
  company: any | null
  onboardingStep: string
}> {
  try {
    const company = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, auth0UserId)
    })
    
    if (!company) {
      return {
        needsOnboarding: true,
        company: null,
        onboardingStep: 'basic_info'
      }
    }
    
    const needsOnboarding = company.onboarding_completed !== 'true'
    
    return {
      needsOnboarding,
      company,
      onboardingStep: company.onboarding_step || 'basic_info'
    }
    
  } catch (error) {
    console.error('Error checking onboarding status:', error)
    return {
      needsOnboarding: true,
      company: null,
      onboardingStep: 'basic_info'
    }
  }
}

/**
 * Enhanced tenant auth that includes auto company creation
 */
export async function withEnhancedTenantAuth<T extends any[]>(
  handler: (request: Request, context: { companyId: string; userId: string; company: any }, ...args: T) => Promise<Response>
) {
  return async (request: Request, ...args: T): Promise<Response> => {
    try {
      const session = await getSession()
      
      if (!session?.user) {
        return new Response(
          JSON.stringify({ 
            error: 'Unauthorized', 
            message: 'Access denied. Please ensure you are logged in and have proper permissions.' 
          }), 
          { status: 401, headers: { 'Content-Type': 'application/json' } }
        )
      }
      
      // Ensure company exists (auto-create if needed)
      const companyId = await ensureCompanyExists(session.user as Auth0User)
      
      if (!companyId) {
        return new Response(
          JSON.stringify({ 
            error: 'Company Setup Required', 
            message: 'Failed to create company profile. Please try again.' 
          }), 
          { status: 500, headers: { 'Content-Type': 'application/json' } }
        )
      }
      
      // Get company details
      const company = await db.query.companies.findFirst({
        where: eq(companies.id, companyId)
      })
      
      if (!company) {
        return new Response(
          JSON.stringify({ 
            error: 'Company Not Found', 
            message: 'Company profile not found.' 
          }), 
          { status: 404, headers: { 'Content-Type': 'application/json' } }
        )
      }
      
      // Create context
      const context = {
        companyId: company.id,
        userId: session.user.sub,
        company
      }
      
      // Call original handler
      return await handler(request, context, ...args)
      
    } catch (error) {
      console.error('Enhanced tenant auth error:', error)
      return new Response(
        JSON.stringify({ 
          error: 'Internal Server Error', 
          message: 'An unexpected error occurred.' 
        }), 
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }
  }
}
