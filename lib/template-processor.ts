import { db } from "@/lib/db"
import { customers, suppliers, products, companies } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"

// Template variable types
export interface TemplateVariables {
  // Contract details
  contract_number?: string
  contract_date?: string
  contract_status?: string

  // Company details (seller/buyer)
  company_name?: string
  company_legal_name?: string
  company_address?: string
  company_full_address?: string
  company_email?: string
  company_phone?: string
  company_website?: string
  company_tax_id?: string
  company_vat_number?: string
  company_registration_number?: string
  company_bank_name?: string
  company_bank_account?: string
  company_bank_swift?: string
  company_bank_address?: string

  // Customer/Supplier details
  customer_name?: string
  customer_email?: string
  customer_address?: string
  supplier_name?: string
  supplier_email?: string
  supplier_address?: string

  // Product details
  product_list?: string
  total_amount?: string
  currency?: string

  // Terms
  payment_terms?: string
  delivery_terms?: string

  // System details
  current_date?: string
  current_year?: string
}

// Contract item interface for product list generation
export interface ContractItem {
  product_id: string
  qty: string
  price: string
  product?: {
    name: string
    description?: string
  }
}

/**
 * Generate company variables from company profile data
 */
export async function generateCompanyVariables(companyId: string): Promise<Partial<TemplateVariables>> {
  const company = await db.query.companies.findFirst({
    where: eq(companies.id, companyId),
  })

  if (!company) {
    return {
      company_name: '[Your Company Name]',
      company_legal_name: '[Your Legal Company Name]',
      company_address: '[Your Company Address]',
      company_full_address: '[Your Company Address]',
      company_email: '[Your Email]',
      company_phone: '[Your Phone]',
      company_website: '[Your Website]',
      company_tax_id: '[Your Tax ID]',
      company_vat_number: '[Your VAT Number]',
      company_registration_number: '[Your Registration Number]',
      company_bank_name: '[Your Bank Name]',
      company_bank_account: '[Your Bank Account]',
      company_bank_swift: '[Your Bank SWIFT]',
      company_bank_address: '[Your Bank Address]',
    }
  }

  // Format full address
  const addressParts = [
    company.address_line1,
    company.address_line2,
    company.city,
    company.state_province,
    company.postal_code,
    company.country
  ].filter(Boolean)

  const fullAddress = addressParts.length > 0 ? addressParts.join(', ') : '[Your Company Address]'
  const shortAddress = company.address_line1 || '[Your Company Address]'

  return {
    company_name: company.name || '[Your Company Name]',
    company_legal_name: company.legal_name || company.name || '[Your Legal Company Name]',
    company_address: shortAddress,
    company_full_address: fullAddress,
    company_email: company.email || '[Your Email]',
    company_phone: company.phone || '[Your Phone]',
    company_website: company.website || '[Your Website]',
    company_tax_id: company.tax_id || '[Your Tax ID]',
    company_vat_number: company.vat_number || '[Your VAT Number]',
    company_registration_number: company.registration_number || '[Your Registration Number]',
    company_bank_name: company.bank_name || '[Your Bank Name]',
    company_bank_account: company.bank_account || '[Your Bank Account]',
    company_bank_swift: company.bank_swift || '[Your Bank SWIFT]',
    company_bank_address: company.bank_address || '[Your Bank Address]',
  }
}

/**
 * Process template content by replacing variables with actual data
 */
export async function processTemplateContent(
  templateContent: string,
  variables: TemplateVariables
): Promise<string> {
  let processedContent = templateContent

  // Replace all variables in the template
  for (const [key, value] of Object.entries(variables)) {
    if (value !== undefined && value !== null) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
      processedContent = processedContent.replace(regex, String(value))
    }
  }

  // Remove any unreplaced variables (optional - could also leave them as is)
  processedContent = processedContent.replace(/{{[^}]+}}/g, '[Variable not available]')

  return processedContent
}

/**
 * Generate variables for sales contract
 */
export async function generateSalesContractVariables(contractData: {
  number: string
  customer_id: string
  date: string
  status: string
  currency?: string
  payment_terms?: string
  delivery_terms?: string
  items: ContractItem[]
  company_id?: string // Add company_id for multi-tenant support
}): Promise<TemplateVariables> {
  // Get customer details
  const customer = await db.query.customers.findFirst({
    where: eq(customers.id, contractData.customer_id),
  })

  // Get company details if company_id is provided
  let companyVariables: Partial<TemplateVariables> = {}
  if (contractData.company_id) {
    companyVariables = await generateCompanyVariables(contractData.company_id)
  }

  // Generate product list
  const productList = await generateProductList(contractData.items)
  const totalAmount = calculateTotalAmount(contractData.items, contractData.currency)

  return {
    // Company variables (seller information)
    ...companyVariables,

    // Contract details
    contract_number: contractData.number,
    contract_date: contractData.date,
    contract_status: contractData.status,

    // Customer details (buyer information)
    customer_name: customer?.name || '[Customer Name]',
    customer_email: customer?.contact_email || '[Customer Email]',
    customer_address: customer?.address || '[Customer Address]',

    // Product and financial details
    product_list: productList,
    total_amount: totalAmount,
    currency: contractData.currency || 'USD',
    payment_terms: contractData.payment_terms || '[Payment Terms]',
    delivery_terms: contractData.delivery_terms || '[Delivery Terms]',

    // System details
    current_date: new Date().toLocaleDateString(),
    current_year: new Date().getFullYear().toString(),
  }
}

/**
 * Generate variables for purchase contract
 */
export async function generatePurchaseContractVariables(contractData: {
  number: string
  supplier_id: string
  date: string
  status: string
  currency?: string
  payment_terms?: string
  delivery_terms?: string
  items: ContractItem[]
  company_id?: string // Add company_id for multi-tenant support
}): Promise<TemplateVariables> {
  // Get supplier details
  const supplier = await db.query.suppliers.findFirst({
    where: eq(suppliers.id, contractData.supplier_id),
  })

  // Get company details if company_id is provided
  let companyVariables: Partial<TemplateVariables> = {}
  if (contractData.company_id) {
    companyVariables = await generateCompanyVariables(contractData.company_id)
  }

  // Generate product list
  const productList = await generateProductList(contractData.items)
  const totalAmount = calculateTotalAmount(contractData.items, contractData.currency)

  return {
    // Company variables (buyer information)
    ...companyVariables,

    // Contract details
    contract_number: contractData.number,
    contract_date: contractData.date,
    contract_status: contractData.status,

    // Supplier details (seller information)
    supplier_name: supplier?.name || '[Supplier Name]',
    supplier_email: supplier?.contact_email || '[Supplier Email]',
    supplier_address: supplier?.address || '[Supplier Address]',

    // Product and financial details
    product_list: productList,
    total_amount: totalAmount,
    currency: contractData.currency || 'USD',
    payment_terms: contractData.payment_terms || '[Payment Terms]',
    delivery_terms: contractData.delivery_terms || '[Delivery Terms]',

    // System details
    current_date: new Date().toLocaleDateString(),
    current_year: new Date().getFullYear().toString(),
  }
}

/**
 * Generate formatted product list from contract items
 */
async function generateProductList(items: ContractItem[]): Promise<string> {
  const productLines = await Promise.all(
    items.map(async (item) => {
      let productName = '[Product Name]'

      if (item.product?.name) {
        productName = item.product.name
      } else if (item.product_id) {
        // Fetch product details if not provided
        const product = await db.query.products.findFirst({
          where: eq(products.id, item.product_id),
        })
        productName = product?.name || '[Product Name]'
      }

      const qty = parseFloat(item.qty) || 0
      const price = parseFloat(item.price) || 0
      const lineTotal = qty * price

      return `${productName} - Qty: ${qty}, Price: $${price.toFixed(2)}, Total: $${lineTotal.toFixed(2)}`
    })
  )

  return productLines.join('\n')
}

/**
 * Calculate total amount from contract items
 */
function calculateTotalAmount(items: ContractItem[], currency = 'USD'): string {
  const total = items.reduce((sum, item) => {
    const qty = parseFloat(item.qty) || 0
    const price = parseFloat(item.price) || 0
    return sum + (qty * price)
  }, 0)

  return `${currency} ${total.toFixed(2)}`
}

/**
 * Sanitize template content to prevent XSS
 */
export function sanitizeTemplateContent(content: string): string {
  // Basic HTML sanitization - remove script tags and dangerous attributes
  return content
    .replace(/<script[^>]*>.*?<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '')
}

/**
 * Validate template variables syntax
 */
export function validateTemplateVariables(content: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  const variableRegex = /{{([^}]+)}}/g
  const matches = content.match(variableRegex)

  if (matches) {
    for (const match of matches) {
      const variableName = match.replace(/[{}]/g, '').trim()

      // Check for valid variable names (alphanumeric and underscore only)
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(variableName)) {
        errors.push(`Invalid variable name: ${variableName}`)
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Get list of available template variables
 */
export function getAvailableVariables(): { [key: string]: string } {
  return {
    // Contract details
    contract_number: 'Contract number (e.g., SC-2025-001)',
    contract_date: 'Contract date',
    contract_status: 'Contract status',

    // Company details (your company)
    company_name: 'Your company name',
    company_legal_name: 'Your legal company name',
    company_address: 'Your company address (short)',
    company_full_address: 'Your company full address',
    company_email: 'Your company email',
    company_phone: 'Your company phone',
    company_website: 'Your company website',
    company_tax_id: 'Your company tax ID',
    company_vat_number: 'Your company VAT number',
    company_registration_number: 'Your company registration number',
    company_bank_name: 'Your bank name',
    company_bank_account: 'Your bank account',
    company_bank_swift: 'Your bank SWIFT code',
    company_bank_address: 'Your bank address',

    // Customer/Supplier details
    customer_name: 'Customer name (sales contracts)',
    customer_email: 'Customer email (sales contracts)',
    customer_address: 'Customer address (sales contracts)',
    supplier_name: 'Supplier name (purchase contracts)',
    supplier_email: 'Supplier email (purchase contracts)',
    supplier_address: 'Supplier address (purchase contracts)',

    // Product and financial details
    product_list: 'Formatted list of products with quantities and prices',
    total_amount: 'Total contract amount with currency',
    currency: 'Contract currency',
    payment_terms: 'Payment terms',
    delivery_terms: 'Delivery terms',

    // System details
    current_date: 'Current date',
    current_year: 'Current year',
  }
}
