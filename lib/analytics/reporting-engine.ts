// Advanced reporting and analytics engine

import { db } from "@/lib/db"
import { cacheService } from "@/lib/cache/redis-cache"

// Report types and interfaces
export interface ReportFilter {
  dateRange?: {
    start: string
    end: string
  }
  customerId?: string
  supplierId?: string
  productId?: string
  status?: string
  location?: string
}

export interface ReportMetric {
  name: string
  value: number
  change?: number
  changeType?: "increase" | "decrease" | "neutral"
  format?: "currency" | "percentage" | "number" | "decimal"
}

export interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string
    borderColor?: string
  }>
}

export interface ReportResult {
  title: string
  description: string
  metrics: ReportMetric[]
  charts: Array<{
    type: "line" | "bar" | "pie" | "doughnut"
    title: string
    data: ChartData
  }>
  tables: Array<{
    title: string
    headers: string[]
    rows: any[][]
  }>
  generatedAt: string
}

// Advanced reporting engine
export class ReportingEngine {
  
  // Sales performance report
  async generateSalesReport(filters: ReportFilter): Promise<ReportResult> {
    const cacheKey = `report:sales:${JSON.stringify(filters)}`
    const cached = await cacheService.get<ReportResult>(cacheKey)
    if (cached) return cached

    try {
      // Get sales contracts data
      const contracts = await this.getSalesContracts(filters)
      const previousPeriodContracts = await this.getPreviousPeriodSalesContracts(filters)

      // Calculate metrics
      const totalRevenue = contracts.reduce((sum, contract) => sum + contract.totalValue, 0)
      const previousRevenue = previousPeriodContracts.reduce((sum, contract) => sum + contract.totalValue, 0)
      const revenueChange = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0

      const totalContracts = contracts.length
      const previousContracts = previousPeriodContracts.length
      const contractsChange = previousContracts > 0 ? ((totalContracts - previousContracts) / previousContracts) * 100 : 0

      const avgContractValue = totalContracts > 0 ? totalRevenue / totalContracts : 0
      const previousAvgValue = previousContracts > 0 ? previousRevenue / previousContracts : 0
      const avgValueChange = previousAvgValue > 0 ? ((avgContractValue - previousAvgValue) / previousAvgValue) * 100 : 0

      // Generate charts
      const monthlyRevenue = this.generateMonthlyRevenueChart(contracts)
      const customerDistribution = this.generateCustomerDistributionChart(contracts)
      const productPerformance = this.generateProductPerformanceChart(contracts)

      // Generate tables
      const topCustomers = this.generateTopCustomersTable(contracts)
      const contractStatus = this.generateContractStatusTable(contracts)

      const report: ReportResult = {
        title: "Sales Performance Report",
        description: "Comprehensive analysis of sales performance and trends",
        metrics: [
          {
            name: "Total Revenue",
            value: totalRevenue,
            change: revenueChange,
            changeType: revenueChange >= 0 ? "increase" : "decrease",
            format: "currency"
          },
          {
            name: "Total Contracts",
            value: totalContracts,
            change: contractsChange,
            changeType: contractsChange >= 0 ? "increase" : "decrease",
            format: "number"
          },
          {
            name: "Average Contract Value",
            value: avgContractValue,
            change: avgValueChange,
            changeType: avgValueChange >= 0 ? "increase" : "decrease",
            format: "currency"
          }
        ],
        charts: [
          {
            type: "line",
            title: "Monthly Revenue Trend",
            data: monthlyRevenue
          },
          {
            type: "pie",
            title: "Revenue by Customer",
            data: customerDistribution
          },
          {
            type: "bar",
            title: "Product Performance",
            data: productPerformance
          }
        ],
        tables: [
          {
            title: "Top Customers",
            headers: ["Customer", "Revenue", "Contracts", "Avg Value"],
            rows: topCustomers
          },
          {
            title: "Contract Status",
            headers: ["Status", "Count", "Total Value", "Percentage"],
            rows: contractStatus
          }
        ],
        generatedAt: new Date().toISOString()
      }

      // Cache for 30 minutes
      await cacheService.set(cacheKey, report, 1800)
      return report

    } catch (error) {
      console.error("Error generating sales report:", error)
      throw error
    }
  }

  // Inventory analysis report
  async generateInventoryReport(filters: ReportFilter): Promise<ReportResult> {
    const cacheKey = `report:inventory:${JSON.stringify(filters)}`
    const cached = await cacheService.get<ReportResult>(cacheKey)
    if (cached) return cached

    try {
      // Get inventory data
      const stockLots = await this.getStockLots(filters)
      const stockTransactions = await this.getStockTransactions(filters)

      // Calculate metrics
      const totalProducts = new Set(stockLots.map(lot => lot.productId)).size
      const totalStock = stockLots.reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
      const lowStockProducts = stockLots.filter(lot => parseFloat(lot.qty) < 10).length

      const inboundTransactions = stockTransactions.filter(txn => txn.type === "inbound")
      const outboundTransactions = stockTransactions.filter(txn => txn.type === "outbound")
      
      const totalInbound = inboundTransactions.reduce((sum, txn) => sum + parseFloat(txn.qty), 0)
      const totalOutbound = outboundTransactions.reduce((sum, txn) => sum + parseFloat(txn.qty), 0)
      const turnoverRate = totalOutbound > 0 ? (totalOutbound / totalStock) * 100 : 0

      // Generate charts
      const stockByLocation = this.generateStockByLocationChart(stockLots)
      const inventoryMovement = this.generateInventoryMovementChart(stockTransactions)
      const lowStockAlert = this.generateLowStockChart(stockLots)

      // Generate tables
      const stockSummary = this.generateStockSummaryTable(stockLots)
      const recentTransactions = this.generateRecentTransactionsTable(stockTransactions)

      const report: ReportResult = {
        title: "Inventory Analysis Report",
        description: "Comprehensive inventory status and movement analysis",
        metrics: [
          {
            name: "Total Products",
            value: totalProducts,
            format: "number"
          },
          {
            name: "Total Stock Units",
            value: totalStock,
            format: "number"
          },
          {
            name: "Low Stock Products",
            value: lowStockProducts,
            format: "number"
          },
          {
            name: "Inventory Turnover",
            value: turnoverRate,
            format: "percentage"
          }
        ],
        charts: [
          {
            type: "bar",
            title: "Stock by Location",
            data: stockByLocation
          },
          {
            type: "line",
            title: "Inventory Movement",
            data: inventoryMovement
          },
          {
            type: "doughnut",
            title: "Low Stock Alert",
            data: lowStockAlert
          }
        ],
        tables: [
          {
            title: "Stock Summary by Product",
            headers: ["Product", "SKU", "Total Stock", "Locations", "Status"],
            rows: stockSummary
          },
          {
            title: "Recent Transactions",
            headers: ["Date", "Type", "Product", "Quantity", "Location"],
            rows: recentTransactions
          }
        ],
        generatedAt: new Date().toISOString()
      }

      await cacheService.set(cacheKey, report, 1800)
      return report

    } catch (error) {
      console.error("Error generating inventory report:", error)
      throw error
    }
  }

  // Production efficiency report
  async generateProductionReport(filters: ReportFilter): Promise<ReportResult> {
    const cacheKey = `report:production:${JSON.stringify(filters)}`
    const cached = await cacheService.get<ReportResult>(cacheKey)
    if (cached) return cached

    try {
      // Get production data
      const workOrders = await this.getWorkOrders(filters)
      
      // Calculate metrics
      const totalOrders = workOrders.length
      const completedOrders = workOrders.filter(wo => wo.status === "completed").length
      const inProgressOrders = workOrders.filter(wo => wo.status === "in_progress").length
      const completionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0

      // Generate charts and tables
      const productionTrend = this.generateProductionTrendChart(workOrders)
      const statusDistribution = this.generateProductionStatusChart(workOrders)
      const ordersSummary = this.generateWorkOrdersSummaryTable(workOrders)

      const report: ReportResult = {
        title: "Production Efficiency Report",
        description: "Analysis of production performance and work order status",
        metrics: [
          {
            name: "Total Work Orders",
            value: totalOrders,
            format: "number"
          },
          {
            name: "Completed Orders",
            value: completedOrders,
            format: "number"
          },
          {
            name: "In Progress",
            value: inProgressOrders,
            format: "number"
          },
          {
            name: "Completion Rate",
            value: completionRate,
            format: "percentage"
          }
        ],
        charts: [
          {
            type: "line",
            title: "Production Trend",
            data: productionTrend
          },
          {
            type: "pie",
            title: "Work Order Status",
            data: statusDistribution
          }
        ],
        tables: [
          {
            title: "Work Orders Summary",
            headers: ["Order Number", "Product", "Status", "Created", "Completed"],
            rows: ordersSummary
          }
        ],
        generatedAt: new Date().toISOString()
      }

      await cacheService.set(cacheKey, report, 1800)
      return report

    } catch (error) {
      console.error("Error generating production report:", error)
      throw error
    }
  }

  // Financial summary report
  async generateFinancialReport(filters: ReportFilter): Promise<ReportResult> {
    const cacheKey = `report:financial:${JSON.stringify(filters)}`
    const cached = await cacheService.get<ReportResult>(cacheKey)
    if (cached) return cached

    try {
      // Get financial data
      const arInvoices = await this.getARInvoices(filters)
      const apInvoices = await this.getAPInvoices(filters)

      // Calculate metrics
      const totalReceivables = arInvoices.reduce((sum, inv) => sum + parseFloat(inv.amount), 0)
      const totalPayables = apInvoices.reduce((sum, inv) => sum + parseFloat(inv.amount), 0)
      const netPosition = totalReceivables - totalPayables

      const paidReceivables = arInvoices.filter(inv => inv.status === "paid")
        .reduce((sum, inv) => sum + parseFloat(inv.amount), 0)
      const paidPayables = apInvoices.filter(inv => inv.status === "paid")
        .reduce((sum, inv) => sum + parseFloat(inv.amount), 0)

      const report: ReportResult = {
        title: "Financial Summary Report",
        description: "Overview of accounts receivable and payable",
        metrics: [
          {
            name: "Total Receivables",
            value: totalReceivables,
            format: "currency"
          },
          {
            name: "Total Payables",
            value: totalPayables,
            format: "currency"
          },
          {
            name: "Net Position",
            value: netPosition,
            format: "currency",
            changeType: netPosition >= 0 ? "increase" : "decrease"
          },
          {
            name: "Collection Rate",
            value: totalReceivables > 0 ? (paidReceivables / totalReceivables) * 100 : 0,
            format: "percentage"
          }
        ],
        charts: [],
        tables: [],
        generatedAt: new Date().toISOString()
      }

      await cacheService.set(cacheKey, report, 1800)
      return report

    } catch (error) {
      console.error("Error generating financial report:", error)
      throw error
    }
  }

  // Helper methods for data retrieval (mock implementations)
  private async getSalesContracts(filters: ReportFilter): Promise<any[]> {
    // Mock implementation - would query actual database
    return []
  }

  private async getPreviousPeriodSalesContracts(filters: ReportFilter): Promise<any[]> {
    // Mock implementation - would query previous period data
    return []
  }

  private async getStockLots(filters: ReportFilter): Promise<any[]> {
    // Mock implementation
    return []
  }

  private async getStockTransactions(filters: ReportFilter): Promise<any[]> {
    // Mock implementation
    return []
  }

  private async getWorkOrders(filters: ReportFilter): Promise<any[]> {
    // Mock implementation
    return []
  }

  private async getARInvoices(filters: ReportFilter): Promise<any[]> {
    // Mock implementation
    return []
  }

  private async getAPInvoices(filters: ReportFilter): Promise<any[]> {
    // Mock implementation
    return []
  }

  // Chart generation helpers (mock implementations)
  private generateMonthlyRevenueChart(contracts: any[]): ChartData {
    return {
      labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
      datasets: [{
        label: "Revenue",
        data: [10000, 15000, 12000, 18000, 16000, 20000],
        borderColor: "#3b82f6"
      }]
    }
  }

  private generateCustomerDistributionChart(contracts: any[]): ChartData {
    return {
      labels: ["Customer A", "Customer B", "Customer C"],
      datasets: [{
        label: "Revenue",
        data: [30, 25, 45],
        backgroundColor: ["#3b82f6", "#10b981", "#f59e0b"]
      }]
    }
  }

  private generateProductPerformanceChart(contracts: any[]): ChartData {
    return {
      labels: ["Product 1", "Product 2", "Product 3"],
      datasets: [{
        label: "Sales",
        data: [100, 150, 120],
        backgroundColor: "#3b82f6"
      }]
    }
  }

  private generateStockByLocationChart(stockLots: any[]): ChartData {
    return {
      labels: ["Warehouse A", "Warehouse B", "Warehouse C"],
      datasets: [{
        label: "Stock Units",
        data: [500, 300, 200],
        backgroundColor: "#10b981"
      }]
    }
  }

  private generateInventoryMovementChart(transactions: any[]): ChartData {
    return {
      labels: ["Week 1", "Week 2", "Week 3", "Week 4"],
      datasets: [
        {
          label: "Inbound",
          data: [100, 120, 80, 150],
          borderColor: "#10b981"
        },
        {
          label: "Outbound",
          data: [80, 90, 110, 100],
          borderColor: "#ef4444"
        }
      ]
    }
  }

  private generateLowStockChart(stockLots: any[]): ChartData {
    return {
      labels: ["Normal Stock", "Low Stock", "Out of Stock"],
      datasets: [{
        label: "Products",
        data: [80, 15, 5],
        backgroundColor: ["#10b981", "#f59e0b", "#ef4444"]
      }]
    }
  }

  private generateProductionTrendChart(workOrders: any[]): ChartData {
    return {
      labels: ["Week 1", "Week 2", "Week 3", "Week 4"],
      datasets: [{
        label: "Completed Orders",
        data: [10, 15, 12, 18],
        borderColor: "#3b82f6"
      }]
    }
  }

  private generateProductionStatusChart(workOrders: any[]): ChartData {
    return {
      labels: ["Pending", "In Progress", "Completed"],
      datasets: [{
        label: "Work Orders",
        data: [20, 30, 50],
        backgroundColor: ["#f59e0b", "#3b82f6", "#10b981"]
      }]
    }
  }

  // Table generation helpers (mock implementations)
  private generateTopCustomersTable(contracts: any[]): any[][] {
    return [
      ["Customer A", "$50,000", "5", "$10,000"],
      ["Customer B", "$35,000", "3", "$11,667"],
      ["Customer C", "$25,000", "2", "$12,500"]
    ]
  }

  private generateContractStatusTable(contracts: any[]): any[][] {
    return [
      ["Active", "15", "$150,000", "60%"],
      ["Completed", "8", "$80,000", "32%"],
      ["Draft", "2", "$20,000", "8%"]
    ]
  }

  private generateStockSummaryTable(stockLots: any[]): any[][] {
    return [
      ["Product A", "SKU001", "500", "3", "Normal"],
      ["Product B", "SKU002", "50", "2", "Low"],
      ["Product C", "SKU003", "200", "1", "Normal"]
    ]
  }

  private generateRecentTransactionsTable(transactions: any[]): any[][] {
    return [
      ["2024-01-15", "Inbound", "Product A", "100", "Warehouse A"],
      ["2024-01-14", "Outbound", "Product B", "50", "Warehouse B"],
      ["2024-01-13", "Inbound", "Product C", "75", "Warehouse A"]
    ]
  }

  private generateWorkOrdersSummaryTable(workOrders: any[]): any[][] {
    return [
      ["WO001", "Product A", "Completed", "2024-01-10", "2024-01-15"],
      ["WO002", "Product B", "In Progress", "2024-01-12", "-"],
      ["WO003", "Product C", "Pending", "2024-01-14", "-"]
    ]
  }
}

// Export singleton instance
export const reportingEngine = new ReportingEngine()
