import { NextRequest } from "next/server"
import { createAuthenticationError, createAuthorizationError } from "./errors"

// User roles for role-based access control
export enum UserRole {
  ADMIN = "admin",
  MANAGER = "manager", 
  OPERATOR = "operator"
}

export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  permissions: string[]
}

// Mock user data for development (replace with database in production)
const MOCK_USERS: User[] = [
  {
    id: "admin-1",
    email: "<EMAIL>",
    name: "System Administrator",
    role: UserRole.ADMIN,
    permissions: ["*"] // Admin has all permissions
  },
  {
    id: "manager-1", 
    email: "<EMAIL>",
    name: "Production Manager",
    role: UserRole.MANAGER,
    permissions: [
      "customers:read", "customers:write",
      "products:read", "products:write",
      "contracts:read", "contracts:write",
      "production:read", "production:write",
      "inventory:read", "inventory:write",
      "export:read", "export:write",
      "finance:read"
    ]
  },
  {
    id: "operator-1",
    email: "<EMAIL>", 
    name: "Production Operator",
    role: UserRole.OPERATOR,
    permissions: [
      "products:read",
      "production:read", "production:write",
      "inventory:read", "inventory:write",
      "samples:read"
    ]
  }
]

// Simple JWT-like token structure (in production, use proper JWT)
interface AuthToken {
  userId: string
  email: string
  role: UserRole
  exp: number // expiration timestamp
}

// Mock authentication functions (replace with real implementation)
export function authenticateUser(email: string, password: string): User | null {
  // In production, verify password hash against database
  const user = MOCK_USERS.find(u => u.email === email)
  
  // For demo purposes, accept any password for existing users
  if (user && password.length > 0) {
    return user
  }
  
  return null
}

export function generateAuthToken(user: User): string {
  const token: AuthToken = {
    userId: user.id,
    email: user.email,
    role: user.role,
    exp: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
  }
  
  // In production, use proper JWT signing
  return Buffer.from(JSON.stringify(token)).toString('base64')
}

export function verifyAuthToken(token: string): AuthToken | null {
  try {
    // In production, verify JWT signature
    const decoded = JSON.parse(Buffer.from(token, 'base64').toString())
    
    if (decoded.exp < Date.now()) {
      return null // Token expired
    }
    
    return decoded as AuthToken
  } catch {
    return null
  }
}

export function getUserFromToken(token: string): User | null {
  const authToken = verifyAuthToken(token)
  if (!authToken) return null
  
  return MOCK_USERS.find(u => u.id === authToken.userId) || null
}

// Extract auth token from request
export function getAuthTokenFromRequest(request: NextRequest): string | null {
  // Check Authorization header
  const authHeader = request.headers.get('authorization')
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }
  
  // Check cookie (for browser requests)
  const cookieToken = request.cookies.get('auth-token')?.value
  if (cookieToken) {
    return cookieToken
  }
  
  return null
}

// Get current user from request
export function getCurrentUser(request: NextRequest): User | null {
  const token = getAuthTokenFromRequest(request)
  if (!token) return null
  
  return getUserFromToken(token)
}

// Permission checking
export function hasPermission(user: User, permission: string): boolean {
  // Admin has all permissions
  if (user.permissions.includes("*")) {
    return true
  }
  
  // Check specific permission
  return user.permissions.includes(permission)
}

export function requirePermission(user: User | null, permission: string): void {
  if (!user) {
    throw createAuthenticationError()
  }
  
  if (!hasPermission(user, permission)) {
    throw createAuthorizationError(`access ${permission}`)
  }
}

// Middleware for protecting API routes
export function requireAuth(request: NextRequest): User {
  const user = getCurrentUser(request)
  if (!user) {
    throw createAuthenticationError()
  }
  return user
}

export function requireRole(request: NextRequest, allowedRoles: UserRole[]): User {
  const user = requireAuth(request)
  
  if (!allowedRoles.includes(user.role)) {
    throw createAuthorizationError(`access with role ${user.role}`)
  }
  
  return user
}

// Higher-order function for protecting API handlers
export function withAuth<T extends any[]>(
  handler: (user: User, ...args: T) => Promise<Response>,
  requiredPermission?: string
) {
  return async (request: NextRequest, ...args: T): Promise<Response> => {
    try {
      const user = requireAuth(request)
      
      if (requiredPermission) {
        requirePermission(user, requiredPermission)
      }
      
      return await handler(user, ...args)
    } catch (error) {
      const { createErrorResponse } = await import("./api-helpers")
      return createErrorResponse(error)
    }
  }
}

// Role-based route protection
export function withRole<T extends any[]>(
  handler: (user: User, ...args: T) => Promise<Response>,
  allowedRoles: UserRole[]
) {
  return async (request: NextRequest, ...args: T): Promise<Response> => {
    try {
      const user = requireRole(request, allowedRoles)
      return await handler(user, ...args)
    } catch (error) {
      const { createErrorResponse } = await import("./api-helpers")
      return createErrorResponse(error)
    }
  }
}

// Utility functions for client-side
export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') return false
  
  const token = localStorage.getItem('auth-token')
  if (!token) return false
  
  const authToken = verifyAuthToken(token)
  return authToken !== null
}

export function getStoredUser(): User | null {
  if (typeof window === 'undefined') return null
  
  const token = localStorage.getItem('auth-token')
  if (!token) return null
  
  return getUserFromToken(token)
}

export function logout(): void {
  if (typeof window === 'undefined') return
  
  localStorage.removeItem('auth-token')
  // Redirect to login page
  window.location.href = '/login'
}
