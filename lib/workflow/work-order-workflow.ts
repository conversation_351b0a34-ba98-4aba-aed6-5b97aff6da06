import { User } from "@/lib/auth"
import { workflowService } from "./state-machine"
import { BusinessLogicError, ErrorCode } from "@/lib/errors"
import { logSuccess, AuditAction, AuditResource } from "@/lib/audit"

// Work order workflow data
export interface WorkOrderOperation {
  id: string
  name: string
  status: "not-started" | "in-progress" | "completed"
  startedAt?: string
  completedAt?: string
  assignedTo?: string
  estimatedDuration?: number // in minutes
  actualDuration?: number // in minutes
}

export interface WorkOrderWorkflowData {
  workOrderId: string
  workOrderNumber: string
  salesContractId: string
  productId: string
  qty: number
  operations: WorkOrderOperation[]
  allOperationsCompleted?: boolean
}

export class WorkOrderWorkflowService {
  async startProduction(
    workOrderId: string,
    workOrderData: WorkOrderWorkflowData,
    user: User
  ): Promise<string> {
    // Validate that work order is ready to start
    if (!workOrderData.operations || workOrderData.operations.length === 0) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Work order must have operations defined before starting production"
      )
    }

    // Check if materials are available
    const materialsAvailable = await this.checkMaterialsAvailability(workOrderData)
    if (!materialsAvailable) {
      throw new BusinessLogicError(
        ErrorCode.INSUFFICIENT_STOCK,
        "Insufficient materials available to start production"
      )
    }

    const newState = await workflowService.transitionState(
      "work_order",
      workOrderId,
      "pending",
      "in_progress",
      user,
      workOrderData
    )

    // Start the first operation
    await this.startFirstOperation(workOrderData, user)

    // Log the action
    logSuccess(
      user,
      AuditAction.UPDATE,
      AuditResource.WORK_ORDER,
      workOrderId,
      { 
        workOrderNumber: workOrderData.workOrderNumber,
        status: newState,
        action: "start_production"
      }
    )

    return newState
  }

  async completeProduction(
    workOrderId: string,
    workOrderData: WorkOrderWorkflowData,
    user: User
  ): Promise<string> {
    // Check if all operations are completed
    const allOperationsCompleted = workOrderData.operations.every(
      op => op.status === "completed"
    )

    if (!allOperationsCompleted) {
      throw new BusinessLogicError(
        ErrorCode.INVALID_WORKFLOW_TRANSITION,
        "Cannot complete work order until all operations are finished"
      )
    }

    const newState = await workflowService.transitionState(
      "work_order",
      workOrderId,
      "in_progress",
      "completed",
      user,
      { ...workOrderData, allOperationsCompleted: true }
    )

    // Update inventory with completed products
    await this.updateInventoryOnCompletion(workOrderData, user)

    // Log the completion
    logSuccess(
      user,
      AuditAction.UPDATE,
      AuditResource.WORK_ORDER,
      workOrderId,
      { 
        workOrderNumber: workOrderData.workOrderNumber,
        status: newState,
        action: "complete_production"
      }
    )

    return newState
  }

  async cancelWorkOrder(
    workOrderId: string,
    workOrderData: WorkOrderWorkflowData,
    user: User,
    reason?: string
  ): Promise<string> {
    // Determine current state
    const currentState = this.getCurrentState(workOrderData)
    
    const newState = await workflowService.transitionState(
      "work_order",
      workOrderId,
      currentState,
      "cancelled",
      user,
      workOrderData
    )

    // Release any reserved materials
    await this.releaseMaterials(workOrderData, user)

    // Log the cancellation
    logSuccess(
      user,
      AuditAction.UPDATE,
      AuditResource.WORK_ORDER,
      workOrderId,
      { 
        workOrderNumber: workOrderData.workOrderNumber,
        status: newState,
        action: "cancel",
        reason
      }
    )

    return newState
  }

  // Operation management
  async startOperation(
    workOrderId: string,
    operationId: string,
    workOrderData: WorkOrderWorkflowData,
    user: User,
    assignedTo?: string
  ): Promise<WorkOrderOperation> {
    const operation = workOrderData.operations.find(op => op.id === operationId)
    if (!operation) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Operation not found"
      )
    }

    if (operation.status !== "not-started") {
      throw new BusinessLogicError(
        ErrorCode.INVALID_WORKFLOW_TRANSITION,
        "Operation has already been started"
      )
    }

    // Check if previous operations are completed (if required)
    const operationIndex = workOrderData.operations.findIndex(op => op.id === operationId)
    if (operationIndex > 0) {
      const previousOperation = workOrderData.operations[operationIndex - 1]
      if (previousOperation.status !== "completed") {
        throw new BusinessLogicError(
          ErrorCode.INVALID_WORKFLOW_TRANSITION,
          "Previous operation must be completed first"
        )
      }
    }

    // Update operation status
    const updatedOperation: WorkOrderOperation = {
      ...operation,
      status: "in-progress",
      startedAt: new Date().toISOString(),
      assignedTo: assignedTo || user.id
    }

    // Log the operation start
    logSuccess(
      user,
      AuditAction.UPDATE,
      AuditResource.WORK_ORDER,
      workOrderId,
      { 
        workOrderNumber: workOrderData.workOrderNumber,
        operationId,
        operationName: operation.name,
        action: "start_operation",
        assignedTo
      }
    )

    return updatedOperation
  }

  async completeOperation(
    workOrderId: string,
    operationId: string,
    workOrderData: WorkOrderWorkflowData,
    user: User,
    qualityCheck?: {
      passed: boolean
      notes?: string
    }
  ): Promise<WorkOrderOperation> {
    const operation = workOrderData.operations.find(op => op.id === operationId)
    if (!operation) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Operation not found"
      )
    }

    if (operation.status !== "in-progress") {
      throw new BusinessLogicError(
        ErrorCode.INVALID_WORKFLOW_TRANSITION,
        "Operation must be in progress to complete"
      )
    }

    // Calculate actual duration
    const startTime = operation.startedAt ? new Date(operation.startedAt) : new Date()
    const endTime = new Date()
    const actualDuration = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60))

    // Update operation status
    const updatedOperation: WorkOrderOperation = {
      ...operation,
      status: "completed",
      completedAt: endTime.toISOString(),
      actualDuration
    }

    // Log the operation completion
    logSuccess(
      user,
      AuditAction.UPDATE,
      AuditResource.WORK_ORDER,
      workOrderId,
      { 
        workOrderNumber: workOrderData.workOrderNumber,
        operationId,
        operationName: operation.name,
        action: "complete_operation",
        actualDuration,
        qualityCheck
      }
    )

    return updatedOperation
  }

  // Helper methods
  private getCurrentState(workOrderData: WorkOrderWorkflowData): string {
    // Determine current state based on operations
    const hasStartedOperations = workOrderData.operations.some(op => 
      op.status === "in-progress" || op.status === "completed"
    )
    
    const allOperationsCompleted = workOrderData.operations.every(op => 
      op.status === "completed"
    )

    if (allOperationsCompleted) {
      return "completed"
    } else if (hasStartedOperations) {
      return "in_progress"
    } else {
      return "pending"
    }
  }

  private async checkMaterialsAvailability(workOrderData: WorkOrderWorkflowData): Promise<boolean> {
    // This would check if required materials are available in inventory
    // For now, return true (mock implementation)
    return true
  }

  private async startFirstOperation(workOrderData: WorkOrderWorkflowData, user: User): Promise<void> {
    // Auto-start the first operation if configured to do so
    if (workOrderData.operations.length > 0) {
      const firstOperation = workOrderData.operations[0]
      if (firstOperation.status === "not-started") {
        // In a real implementation, this would update the database
        console.log(`Auto-starting first operation: ${firstOperation.name}`)
      }
    }
  }

  private async updateInventoryOnCompletion(
    workOrderData: WorkOrderWorkflowData,
    user: User
  ): Promise<void> {
    // This would add completed products to inventory
    // Implementation would involve:
    // 1. Create inbound stock transaction
    // 2. Create stock lot for completed products
    // 3. Update product availability
    console.log(`Adding ${workOrderData.qty} units of product ${workOrderData.productId} to inventory`)
  }

  private async releaseMaterials(workOrderData: WorkOrderWorkflowData, user: User): Promise<void> {
    // This would release any reserved materials back to available inventory
    console.log(`Releasing materials for cancelled work order ${workOrderData.workOrderNumber}`)
  }

  // Analytics and reporting
  getOperationEfficiency(operation: WorkOrderOperation): {
    efficiency: number
    status: "on-time" | "delayed" | "early"
  } {
    if (!operation.estimatedDuration || !operation.actualDuration) {
      return { efficiency: 0, status: "on-time" }
    }

    const efficiency = (operation.estimatedDuration / operation.actualDuration) * 100
    
    let status: "on-time" | "delayed" | "early"
    if (efficiency > 110) {
      status = "early"
    } else if (efficiency < 90) {
      status = "delayed"
    } else {
      status = "on-time"
    }

    return { efficiency: Math.round(efficiency), status }
  }

  getWorkOrderProgress(workOrderData: WorkOrderWorkflowData): {
    completedOperations: number
    totalOperations: number
    progressPercentage: number
    estimatedCompletion?: string
  } {
    const completedOperations = workOrderData.operations.filter(op => 
      op.status === "completed"
    ).length
    
    const totalOperations = workOrderData.operations.length
    const progressPercentage = totalOperations > 0 
      ? Math.round((completedOperations / totalOperations) * 100)
      : 0

    // Calculate estimated completion time
    let estimatedCompletion: string | undefined
    if (progressPercentage > 0 && progressPercentage < 100) {
      const remainingOperations = workOrderData.operations.filter(op => 
        op.status === "not-started"
      )
      
      const totalEstimatedTime = remainingOperations.reduce((sum, op) => 
        sum + (op.estimatedDuration || 60), 0
      )
      
      const estimatedCompletionDate = new Date()
      estimatedCompletionDate.setMinutes(estimatedCompletionDate.getMinutes() + totalEstimatedTime)
      estimatedCompletion = estimatedCompletionDate.toISOString()
    }

    return {
      completedOperations,
      totalOperations,
      progressPercentage,
      estimatedCompletion
    }
  }
}

// Export singleton instance
export const workOrderWorkflowService = new WorkOrderWorkflowService()
