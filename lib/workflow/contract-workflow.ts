import { User } from "@/lib/auth"
import { workflowService } from "./state-machine"
import { BusinessLogicError, ErrorCode } from "@/lib/errors"
import { logContractOperation, AuditAction, AuditResource } from "@/lib/audit"

// Contract workflow actions
export interface ContractWorkflowData {
  contractId: string
  contractNumber: string
  customerId: string
  items: Array<{
    productId: string
    qty: number
    price: number
  }>
  totalValue: number
  allWorkOrdersCompleted?: boolean
}

export class ContractWorkflowService {
  async submitForReview(
    contractId: string,
    contractData: ContractWorkflowData,
    user: User
  ): Promise<string> {
    // Validate contract has required data
    if (!contractData.items || contractData.items.length === 0) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Contract must have at least one item before submitting for review"
      )
    }

    if (!contractData.customerId) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Contract must have a customer before submitting for review"
      )
    }

    // Calculate total value
    const totalValue = contractData.items.reduce((sum, item) => {
      return sum + (item.qty * item.price)
    }, 0)

    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "draft",
      "review",
      user,
      { ...contractData, totalValue }
    )

    // Log the action
    logContractOperation(
      user,
      AuditAction.UPDATE,
      AuditResource.SALES_CONTRACT,
      contractId,
      contractData.contractNumber,
      newState
    )

    return newState
  }

  async approveContract(
    contractId: string,
    contractData: ContractWorkflowData,
    user: User
  ): Promise<string> {
    // Check if user has permission to approve
    if (!["manager", "admin"].includes(user.role)) {
      throw new BusinessLogicError(
        ErrorCode.AUTHORIZATION_ERROR,
        "Only managers and administrators can approve contracts"
      )
    }

    // Check contract value limits based on user role
    const maxApprovalAmount = user.role === "manager" ? 100000 : Infinity
    if (contractData.totalValue > maxApprovalAmount) {
      throw new BusinessLogicError(
        ErrorCode.AUTHORIZATION_ERROR,
        `Contract value exceeds your approval limit of $${maxApprovalAmount.toLocaleString()}`
      )
    }

    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "review",
      "approved",
      user,
      contractData
    )

    // Log the approval
    logContractOperation(
      user,
      AuditAction.APPROVE,
      AuditResource.SALES_CONTRACT,
      contractId,
      contractData.contractNumber,
      newState
    )

    return newState
  }

  async rejectContract(
    contractId: string,
    contractData: ContractWorkflowData,
    user: User,
    reason?: string
  ): Promise<string> {
    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "review",
      "draft",
      user,
      contractData
    )

    // Log the rejection
    logContractOperation(
      user,
      AuditAction.REJECT,
      AuditResource.SALES_CONTRACT,
      contractId,
      contractData.contractNumber,
      newState
    )

    // In a real system, you might want to store the rejection reason
    // and notify the contract creator

    return newState
  }

  async activateContract(
    contractId: string,
    contractData: ContractWorkflowData,
    user: User
  ): Promise<{
    newState: string
    workOrdersCreated: string[]
  }> {
    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "approved",
      "active",
      user,
      contractData
    )

    // Auto-generate work orders for each contract item
    const workOrdersCreated = await this.generateWorkOrders(contractId, contractData, user)

    // Log the activation
    logContractOperation(
      user,
      AuditAction.UPDATE,
      AuditResource.SALES_CONTRACT,
      contractId,
      contractData.contractNumber,
      newState
    )

    return {
      newState,
      workOrdersCreated
    }
  }

  async completeContract(
    contractId: string,
    contractData: ContractWorkflowData,
    user: User
  ): Promise<string> {
    // Check if all work orders are completed
    const allWorkOrdersCompleted = await this.checkAllWorkOrdersCompleted(contractId)
    
    if (!allWorkOrdersCompleted) {
      throw new BusinessLogicError(
        ErrorCode.INVALID_WORKFLOW_TRANSITION,
        "Cannot complete contract until all work orders are completed"
      )
    }

    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "active",
      "completed",
      user,
      { ...contractData, allWorkOrdersCompleted: true }
    )

    // Log the completion
    logContractOperation(
      user,
      AuditAction.UPDATE,
      AuditResource.SALES_CONTRACT,
      contractId,
      contractData.contractNumber,
      newState
    )

    return newState
  }

  async cancelContract(
    contractId: string,
    contractData: ContractWorkflowData,
    user: User,
    reason?: string
  ): Promise<string> {
    // Determine current state and validate cancellation
    const currentState = contractData.contractNumber // This would come from the database
    
    // Contracts can be cancelled from most states except completed
    const validCancellationStates = ["draft", "review", "approved", "active"]
    
    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      currentState,
      "cancelled",
      user,
      contractData
    )

    // If contract was active, cancel associated work orders
    if (currentState === "active") {
      await this.cancelAssociatedWorkOrders(contractId, user)
    }

    // Log the cancellation
    logContractOperation(
      user,
      AuditAction.UPDATE,
      AuditResource.SALES_CONTRACT,
      contractId,
      contractData.contractNumber,
      newState
    )

    return newState
  }

  getAvailableActions(
    currentState: string,
    contractData: ContractWorkflowData,
    user: User
  ): Array<{
    action: string
    targetState: string
    canExecute: boolean
    reason?: string
  }> {
    return workflowService.getAvailableActions(
      "contract",
      currentState,
      user,
      contractData
    )
  }

  // Helper methods
  private async generateWorkOrders(
    contractId: string,
    contractData: ContractWorkflowData,
    user: User
  ): Promise<string[]> {
    // This would integrate with the WorkOrderService
    // For now, return mock work order IDs
    const workOrderIds: string[] = []

    for (const item of contractData.items) {
      // Create a work order for each contract item
      const workOrderId = `wo_${Date.now()}_${Math.random().toString(36).substring(2)}`
      workOrderIds.push(workOrderId)

      // In a real implementation, you would:
      // 1. Create work order record in database
      // 2. Set up production operations
      // 3. Allocate resources
      // 4. Schedule production
    }

    return workOrderIds
  }

  private async checkAllWorkOrdersCompleted(contractId: string): Promise<boolean> {
    // This would query the database to check work order statuses
    // For now, return a mock value
    return Math.random() > 0.5 // 50% chance for demo purposes
  }

  private async cancelAssociatedWorkOrders(contractId: string, user: User): Promise<void> {
    // This would find and cancel all work orders associated with the contract
    // Implementation would involve:
    // 1. Find all work orders for this contract
    // 2. Cancel each work order that's not already completed
    // 3. Update inventory reservations
    // 4. Notify production team
  }

  // Business rules validation
  async validateContractForApproval(contractData: ContractWorkflowData): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    // Check minimum contract value
    if (contractData.totalValue < 1000) {
      warnings.push("Contract value is below recommended minimum of $1,000")
    }

    // Check maximum contract value
    if (contractData.totalValue > 1000000) {
      errors.push("Contract value exceeds maximum limit of $1,000,000")
    }

    // Check item quantities
    for (const item of contractData.items) {
      if (item.qty <= 0) {
        errors.push(`Invalid quantity for product ${item.productId}`)
      }
      if (item.price <= 0) {
        errors.push(`Invalid price for product ${item.productId}`)
      }
    }

    // Check for duplicate products
    const productIds = contractData.items.map(item => item.productId)
    const uniqueProductIds = new Set(productIds)
    if (productIds.length !== uniqueProductIds.size) {
      warnings.push("Contract contains duplicate products")
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// Export singleton instance
export const contractWorkflowService = new ContractWorkflowService()
