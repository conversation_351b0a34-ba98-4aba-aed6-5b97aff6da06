import { BusinessLogicError, ErrorCode, createInvalidStatusTransitionError } from "@/lib/errors"
import { User } from "@/lib/auth"

// State machine types
export interface StateTransition {
  from: string
  to: string
  action: string
  condition?: (context: any) => boolean
  effect?: (context: any) => Promise<void>
}

export interface StateMachine {
  name: string
  initialState: string
  states: string[]
  transitions: StateTransition[]
  finalStates: string[]
}

export interface WorkflowContext {
  entityId: string
  entityType: string
  currentState: string
  user: User
  data?: any
}

// Base workflow engine
export class WorkflowEngine {
  private stateMachines: Map<string, StateMachine> = new Map()

  registerStateMachine(stateMachine: StateMachine): void {
    this.stateMachines.set(stateMachine.name, stateMachine)
  }

  getStateMachine(name: string): StateMachine | undefined {
    return this.stateMachines.get(name)
  }

  async canTransition(
    stateMachineName: string,
    context: WorkflowContext,
    targetState: string
  ): Promise<boolean> {
    const stateMachine = this.getStateMachine(stateMachineName)
    if (!stateMachine) {
      throw new BusinessLogicError(
        ErrorCode.INVALID_WORKFLOW_TRANSITION,
        `State machine "${stateMachineName}" not found`
      )
    }

    const transition = stateMachine.transitions.find(
      t => t.from === context.currentState && t.to === targetState
    )

    if (!transition) {
      return false
    }

    // Check condition if present
    if (transition.condition) {
      return transition.condition(context)
    }

    return true
  }

  async executeTransition(
    stateMachineName: string,
    context: WorkflowContext,
    targetState: string
  ): Promise<WorkflowContext> {
    const canTransition = await this.canTransition(stateMachineName, context, targetState)
    
    if (!canTransition) {
      throw createInvalidStatusTransitionError(context.currentState, targetState)
    }

    const stateMachine = this.getStateMachine(stateMachineName)!
    const transition = stateMachine.transitions.find(
      t => t.from === context.currentState && t.to === targetState
    )!

    // Execute side effects
    if (transition.effect) {
      await transition.effect(context)
    }

    // Return updated context
    return {
      ...context,
      currentState: targetState
    }
  }

  getAvailableTransitions(
    stateMachineName: string,
    currentState: string
  ): StateTransition[] {
    const stateMachine = this.getStateMachine(stateMachineName)
    if (!stateMachine) {
      return []
    }

    return stateMachine.transitions.filter(t => t.from === currentState)
  }

  isValidState(stateMachineName: string, state: string): boolean {
    const stateMachine = this.getStateMachine(stateMachineName)
    return stateMachine ? stateMachine.states.includes(state) : false
  }

  isFinalState(stateMachineName: string, state: string): boolean {
    const stateMachine = this.getStateMachine(stateMachineName)
    return stateMachine ? stateMachine.finalStates.includes(state) : false
  }
}

// Contract workflow state machine
export const contractStateMachine: StateMachine = {
  name: "contract",
  initialState: "draft",
  states: ["draft", "review", "approved", "active", "completed", "cancelled"],
  finalStates: ["completed", "cancelled"],
  transitions: [
    {
      from: "draft",
      to: "review",
      action: "submit_for_review",
      condition: (context) => {
        // Contract must have items and customer
        return context.data?.items?.length > 0 && context.data?.customerId
      }
    },
    {
      from: "draft",
      to: "cancelled",
      action: "cancel"
    },
    {
      from: "review",
      to: "approved",
      action: "approve",
      condition: (context) => {
        // Only managers and admins can approve
        return ["manager", "admin"].includes(context.user.role)
      }
    },
    {
      from: "review",
      to: "draft",
      action: "reject"
    },
    {
      from: "review",
      to: "cancelled",
      action: "cancel"
    },
    {
      from: "approved",
      to: "active",
      action: "activate",
      effect: async (context) => {
        // Auto-generate work orders when contract becomes active
        // This would be implemented in the service layer
        console.log(`Activating contract ${context.entityId}`)
      }
    },
    {
      from: "approved",
      to: "cancelled",
      action: "cancel"
    },
    {
      from: "active",
      to: "completed",
      action: "complete",
      condition: (context) => {
        // All work orders must be completed
        return context.data?.allWorkOrdersCompleted === true
      }
    },
    {
      from: "active",
      to: "cancelled",
      action: "cancel"
    }
  ]
}

// Work order workflow state machine
export const workOrderStateMachine: StateMachine = {
  name: "work_order",
  initialState: "pending",
  states: ["pending", "in_progress", "completed", "cancelled"],
  finalStates: ["completed", "cancelled"],
  transitions: [
    {
      from: "pending",
      to: "in_progress",
      action: "start_production"
    },
    {
      from: "pending",
      to: "cancelled",
      action: "cancel"
    },
    {
      from: "in_progress",
      to: "completed",
      action: "complete_production",
      condition: (context) => {
        // All operations must be completed
        return context.data?.allOperationsCompleted === true
      }
    },
    {
      from: "in_progress",
      to: "cancelled",
      action: "cancel"
    }
  ]
}

// Declaration workflow state machine
export const declarationStateMachine: StateMachine = {
  name: "declaration",
  initialState: "draft",
  states: ["draft", "submitted", "cleared", "exception"],
  finalStates: ["cleared", "exception"],
  transitions: [
    {
      from: "draft",
      to: "submitted",
      action: "submit",
      condition: (context) => {
        // Declaration must have items and required documents
        return context.data?.items?.length > 0 && context.data?.hasRequiredDocuments
      }
    },
    {
      from: "submitted",
      to: "cleared",
      action: "clear_customs"
    },
    {
      from: "submitted",
      to: "exception",
      action: "flag_exception"
    },
    {
      from: "exception",
      to: "submitted",
      action: "resolve_exception"
    }
  ]
}

// Global workflow engine instance
export const workflowEngine = new WorkflowEngine()

// Register all state machines
workflowEngine.registerStateMachine(contractStateMachine)
workflowEngine.registerStateMachine(workOrderStateMachine)
workflowEngine.registerStateMachine(declarationStateMachine)

// Workflow service for managing entity states
export class WorkflowService {
  constructor(private engine: WorkflowEngine) {}

  async transitionState(
    entityType: string,
    entityId: string,
    currentState: string,
    targetState: string,
    user: User,
    data?: any
  ): Promise<string> {
    const context: WorkflowContext = {
      entityId,
      entityType,
      currentState,
      user,
      data
    }

    const updatedContext = await this.engine.executeTransition(
      entityType,
      context,
      targetState
    )

    return updatedContext.currentState
  }

  getAvailableActions(
    entityType: string,
    currentState: string,
    user: User,
    data?: any
  ): Array<{
    action: string
    targetState: string
    canExecute: boolean
    reason?: string
  }> {
    const transitions = this.engine.getAvailableTransitions(entityType, currentState)
    
    return transitions.map(transition => {
      let canExecute = true
      let reason: string | undefined

      // Check condition
      if (transition.condition) {
        const context: WorkflowContext = {
          entityId: "",
          entityType,
          currentState,
          user,
          data
        }
        
        try {
          canExecute = transition.condition(context)
          if (!canExecute) {
            reason = "Condition not met"
          }
        } catch (error) {
          canExecute = false
          reason = error instanceof Error ? error.message : "Unknown error"
        }
      }

      return {
        action: transition.action,
        targetState: transition.to,
        canExecute,
        reason
      }
    })
  }

  isValidTransition(
    entityType: string,
    currentState: string,
    targetState: string
  ): boolean {
    const transitions = this.engine.getAvailableTransitions(entityType, currentState)
    return transitions.some(t => t.to === targetState)
  }

  isFinalState(entityType: string, state: string): boolean {
    return this.engine.isFinalState(entityType, state)
  }
}

// Export singleton instance
export const workflowService = new WorkflowService(workflowEngine)
