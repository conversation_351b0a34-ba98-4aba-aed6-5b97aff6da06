import { zodResolver } from "@hookform/resolvers/zod"
import { useForm, UseFormReturn } from "react-hook-form"
import { z } from "zod"
import { 
  customerSchema, 
  supplierSchema, 
  productSchema, 
  sampleSchema,
  salesContractSchema,
  purchaseContractSchema,
  workOrderSchema,
  declarationSchema,
  arInvoiceSchema,
  apInvoiceSchema,
  inboundSchema,
  outboundSchema
} from "./validations"

// Type inference for form data
export type CustomerFormData = z.infer<typeof customerSchema>
export type SupplierFormData = z.infer<typeof supplierSchema>
export type ProductFormData = z.infer<typeof productSchema>
export type SampleFormData = z.infer<typeof sampleSchema>
export type SalesContractFormData = z.infer<typeof salesContractSchema>
export type PurchaseContractFormData = z.infer<typeof purchaseContractSchema>
export type WorkOrderFormData = z.infer<typeof workOrderSchema>
export type DeclarationFormData = z.infer<typeof declarationSchema>
export type ARInvoiceFormData = z.infer<typeof arInvoiceSchema>
export type APInvoiceFormData = z.infer<typeof apInvoiceSchema>
export type InboundFormData = z.infer<typeof inboundSchema>
export type OutboundFormData = z.infer<typeof outboundSchema>

// Custom hooks for each form type
export function useCustomerForm(defaultValues?: Partial<CustomerFormData>): UseFormReturn<CustomerFormData> {
  return useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      status: "active",
      ...defaultValues
    }
  })
}

export function useSupplierForm(defaultValues?: Partial<SupplierFormData>): UseFormReturn<SupplierFormData> {
  return useForm<SupplierFormData>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      status: "active",
      ...defaultValues
    }
  })
}

export function useProductForm(defaultValues?: Partial<ProductFormData>): UseFormReturn<ProductFormData> {
  return useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues
  })
}

export function useSampleForm(defaultValues?: Partial<SampleFormData>): UseFormReturn<SampleFormData> {
  return useForm<SampleFormData>({
    resolver: zodResolver(sampleSchema),
    defaultValues: {
      availableFromStock: false,
      ...defaultValues
    }
  })
}

export function useSalesContractForm(defaultValues?: Partial<SalesContractFormData>): UseFormReturn<SalesContractFormData> {
  return useForm<SalesContractFormData>({
    resolver: zodResolver(salesContractSchema),
    defaultValues: {
      status: "draft",
      items: [],
      ...defaultValues
    }
  })
}

export function usePurchaseContractForm(defaultValues?: Partial<PurchaseContractFormData>): UseFormReturn<PurchaseContractFormData> {
  return useForm<PurchaseContractFormData>({
    resolver: zodResolver(purchaseContractSchema),
    defaultValues: {
      status: "draft",
      items: [],
      ...defaultValues
    }
  })
}

export function useWorkOrderForm(defaultValues?: Partial<WorkOrderFormData>): UseFormReturn<WorkOrderFormData> {
  return useForm<WorkOrderFormData>({
    resolver: zodResolver(workOrderSchema),
    defaultValues
  })
}

export function useDeclarationForm(defaultValues?: Partial<DeclarationFormData>): UseFormReturn<DeclarationFormData> {
  return useForm<DeclarationFormData>({
    resolver: zodResolver(declarationSchema),
    defaultValues: {
      status: "draft",
      items: [],
      ...defaultValues
    }
  })
}

export function useARInvoiceForm(defaultValues?: Partial<ARInvoiceFormData>): UseFormReturn<ARInvoiceFormData> {
  return useForm<ARInvoiceFormData>({
    resolver: zodResolver(arInvoiceSchema),
    defaultValues: {
      received: 0,
      ...defaultValues
    }
  })
}

export function useAPInvoiceForm(defaultValues?: Partial<APInvoiceFormData>): UseFormReturn<APInvoiceFormData> {
  return useForm<APInvoiceFormData>({
    resolver: zodResolver(apInvoiceSchema),
    defaultValues: {
      paid: 0,
      ...defaultValues
    }
  })
}

export function useInboundForm(defaultValues?: Partial<InboundFormData>): UseFormReturn<InboundFormData> {
  return useForm<InboundFormData>({
    resolver: zodResolver(inboundSchema),
    defaultValues: {
      location: "Main",
      qty: 1,
      ...defaultValues
    }
  })
}

export function useOutboundForm(defaultValues?: Partial<OutboundFormData>): UseFormReturn<OutboundFormData> {
  return useForm<OutboundFormData>({
    resolver: zodResolver(outboundSchema),
    defaultValues: {
      location: "Main",
      qty: 1,
      ...defaultValues
    }
  })
}

// Generic form validation hook
export function useValidatedForm<T>(
  schema: z.ZodSchema<T>,
  defaultValues?: Partial<T>
): UseFormReturn<T> {
  return useForm<T>({
    resolver: zodResolver(schema),
    defaultValues
  })
}

// Utility function to extract form errors
export function getFormErrors(errors: any): Record<string, string> {
  const formErrors: Record<string, string> = {}
  
  Object.keys(errors).forEach(key => {
    if (errors[key]?.message) {
      formErrors[key] = errors[key].message
    }
  })
  
  return formErrors
}

// Utility function to check if form has errors
export function hasFormErrors(errors: any): boolean {
  return Object.keys(errors).length > 0
}
