"use client"

import { useState, useCallback, useRef } from "react"
import { toast } from "./toast"

// Types for optimistic updates
export interface OptimisticAction<T> {
  type: 'add' | 'update' | 'delete'
  id: string
  data?: T
  tempId?: string
}

export interface OptimisticState<T> {
  items: T[]
  pendingActions: Map<string, OptimisticAction<T>>
}

// Hook for managing optimistic updates
export function useOptimisticUpdates<T extends { id: string }>(
  initialItems: T[] = []
) {
  const [state, setState] = useState<OptimisticState<T>>({
    items: initialItems,
    pendingActions: new Map()
  })
  
  const rollbackTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map())

  // Apply optimistic update
  const applyOptimisticUpdate = useCallback((action: OptimisticAction<T>) => {
    setState(prevState => {
      const newPendingActions = new Map(prevState.pendingActions)
      newPendingActions.set(action.id, action)
      
      let newItems = [...prevState.items]
      
      switch (action.type) {
        case 'add':
          if (action.data) {
            newItems.push(action.data)
          }
          break
          
        case 'update':
          if (action.data) {
            const index = newItems.findIndex(item => item.id === action.id)
            if (index !== -1) {
              newItems[index] = { ...newItems[index], ...action.data }
            }
          }
          break
          
        case 'delete':
          newItems = newItems.filter(item => item.id !== action.id)
          break
      }
      
      return {
        items: newItems,
        pendingActions: newPendingActions
      }
    })
  }, [])

  // Confirm optimistic update (remove from pending)
  const confirmOptimisticUpdate = useCallback((actionId: string, actualData?: T) => {
    setState(prevState => {
      const newPendingActions = new Map(prevState.pendingActions)
      const action = newPendingActions.get(actionId)
      newPendingActions.delete(actionId)
      
      let newItems = [...prevState.items]
      
      // If we have actual data from server, update with that
      if (actualData && action) {
        switch (action.type) {
          case 'add':
            // Replace temp item with actual item
            const tempIndex = newItems.findIndex(item => item.id === action.tempId || item.id === actionId)
            if (tempIndex !== -1) {
              newItems[tempIndex] = actualData
            }
            break
            
          case 'update':
            const updateIndex = newItems.findIndex(item => item.id === action.id)
            if (updateIndex !== -1) {
              newItems[updateIndex] = actualData
            }
            break
        }
      }
      
      return {
        items: newItems,
        pendingActions: newPendingActions
      }
    })
    
    // Clear any rollback timeout
    const timeout = rollbackTimeouts.current.get(actionId)
    if (timeout) {
      clearTimeout(timeout)
      rollbackTimeouts.current.delete(actionId)
    }
  }, [])

  // Rollback optimistic update
  const rollbackOptimisticUpdate = useCallback((actionId: string, errorMessage?: string) => {
    setState(prevState => {
      const newPendingActions = new Map(prevState.pendingActions)
      const action = newPendingActions.get(actionId)
      newPendingActions.delete(actionId)
      
      if (!action) return prevState
      
      let newItems = [...prevState.items]
      
      // Reverse the optimistic action
      switch (action.type) {
        case 'add':
          // Remove the optimistically added item
          newItems = newItems.filter(item => 
            item.id !== action.id && item.id !== action.tempId
          )
          break
          
        case 'update':
          // This is tricky - we'd need to store original data
          // For now, just remove from pending and let the UI refresh
          break
          
        case 'delete':
          // Re-add the deleted item (if we stored it)
          if (action.data) {
            newItems.push(action.data)
          }
          break
      }
      
      return {
        items: newItems,
        pendingActions: newPendingActions
      }
    })
    
    // Show error message
    if (errorMessage) {
      toast.error(errorMessage)
    }
    
    // Clear rollback timeout
    const timeout = rollbackTimeouts.current.get(actionId)
    if (timeout) {
      clearTimeout(timeout)
      rollbackTimeouts.current.delete(actionId)
    }
  }, [])

  // Optimistic add
  const optimisticAdd = useCallback(async <TResult extends T>(
    tempItem: T,
    apiCall: () => Promise<TResult>,
    options?: {
      rollbackDelay?: number
      successMessage?: string
      errorMessage?: string
    }
  ): Promise<TResult | null> => {
    const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2)}`
    const actionId = tempId
    
    const action: OptimisticAction<T> = {
      type: 'add',
      id: actionId,
      data: { ...tempItem, id: tempId },
      tempId
    }
    
    // Apply optimistic update
    applyOptimisticUpdate(action)
    
    // Set rollback timeout if specified
    if (options?.rollbackDelay) {
      const timeout = setTimeout(() => {
        rollbackOptimisticUpdate(actionId, "Operation timed out")
      }, options.rollbackDelay)
      rollbackTimeouts.current.set(actionId, timeout)
    }
    
    try {
      const result = await apiCall()
      confirmOptimisticUpdate(actionId, result)
      
      if (options?.successMessage) {
        toast.success(options.successMessage)
      }
      
      return result
    } catch (error) {
      rollbackOptimisticUpdate(actionId, options?.errorMessage)
      return null
    }
  }, [applyOptimisticUpdate, confirmOptimisticUpdate, rollbackOptimisticUpdate])

  // Optimistic update
  const optimisticUpdate = useCallback(async <TResult extends T>(
    id: string,
    updates: Partial<T>,
    apiCall: () => Promise<TResult>,
    options?: {
      rollbackDelay?: number
      successMessage?: string
      errorMessage?: string
    }
  ): Promise<TResult | null> => {
    const actionId = `update_${id}_${Date.now()}`
    
    const action: OptimisticAction<T> = {
      type: 'update',
      id,
      data: updates as T
    }
    
    // Store original data for potential rollback
    const originalItem = state.items.find(item => item.id === id)
    if (originalItem) {
      action.data = { ...originalItem, ...updates }
    }
    
    applyOptimisticUpdate(action)
    
    if (options?.rollbackDelay) {
      const timeout = setTimeout(() => {
        rollbackOptimisticUpdate(actionId, "Operation timed out")
      }, options.rollbackDelay)
      rollbackTimeouts.current.set(actionId, timeout)
    }
    
    try {
      const result = await apiCall()
      confirmOptimisticUpdate(actionId, result)
      
      if (options?.successMessage) {
        toast.success(options.successMessage)
      }
      
      return result
    } catch (error) {
      rollbackOptimisticUpdate(actionId, options?.errorMessage)
      return null
    }
  }, [state.items, applyOptimisticUpdate, confirmOptimisticUpdate, rollbackOptimisticUpdate])

  // Optimistic delete
  const optimisticDelete = useCallback(async (
    id: string,
    apiCall: () => Promise<void>,
    options?: {
      rollbackDelay?: number
      successMessage?: string
      errorMessage?: string
    }
  ): Promise<boolean> => {
    const actionId = `delete_${id}_${Date.now()}`
    
    // Store original item for potential rollback
    const originalItem = state.items.find(item => item.id === id)
    
    const action: OptimisticAction<T> = {
      type: 'delete',
      id,
      data: originalItem
    }
    
    applyOptimisticUpdate(action)
    
    if (options?.rollbackDelay) {
      const timeout = setTimeout(() => {
        rollbackOptimisticUpdate(actionId, "Operation timed out")
      }, options.rollbackDelay)
      rollbackTimeouts.current.set(actionId, timeout)
    }
    
    try {
      await apiCall()
      confirmOptimisticUpdate(actionId)
      
      if (options?.successMessage) {
        toast.success(options.successMessage)
      }
      
      return true
    } catch (error) {
      rollbackOptimisticUpdate(actionId, options?.errorMessage)
      return false
    }
  }, [state.items, applyOptimisticUpdate, confirmOptimisticUpdate, rollbackOptimisticUpdate])

  // Get items with pending state
  const getItemsWithPendingState = useCallback(() => {
    return state.items.map(item => ({
      ...item,
      isPending: Array.from(state.pendingActions.values()).some(
        action => action.id === item.id || action.tempId === item.id
      )
    }))
  }, [state.items, state.pendingActions])

  return {
    items: state.items,
    pendingActions: state.pendingActions,
    optimisticAdd,
    optimisticUpdate,
    optimisticDelete,
    getItemsWithPendingState,
    hasPendingActions: state.pendingActions.size > 0
  }
}
