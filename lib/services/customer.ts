import { BaseService, ServiceContext } from "./base"
import { customers } from "@/lib/schema-postgres"
import { uid } from "@/lib/db"
import { AuditResource } from "@/lib/audit"
import { customerSchema } from "@/lib/validations"
import { sanitizeFormData } from "@/lib/sanitization"
import { BusinessLogicError, ErrorCode } from "@/lib/errors"

// Customer types
export interface Customer {
  id: string
  name: string
  contact_name?: string
  contact_phone?: string
  contact_email?: string
  address?: string
  tax_id?: string
  bank?: string
  incoterm?: string
  payment_term?: string
  status: string
  created_at: string
  updated_at: string
}

export interface CreateCustomerData {
  name: string
  contact_name?: string
  contact_phone?: string
  contact_email?: string
  address?: string
  tax_id?: string
  bank?: string
  incoterm?: string
  payment_term?: string
  status?: string
}

export interface UpdateCustomerData {
  name?: string
  contact_name?: string
  contact_phone?: string
  contact_email?: string
  address?: string
  tax_id?: string
  bank?: string
  incoterm?: string
  payment_term?: string
  status?: string
}

export class CustomerService extends BaseService<Customer, CreateCustomerData, UpdateCustomerData> {
  protected tableName = "customers"
  protected auditResource = AuditResource.CUSTOMER

  protected getTable() {
    return customers
  }

  protected async validateCreate(data: CreateCustomerData): Promise<void> {
    // Sanitize input data
    const sanitized = sanitizeFormData(data)
    
    // Validate with Zod schema
    const validation = customerSchema.safeParse(sanitized)
    if (!validation.success) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Invalid customer data",
        { issues: validation.error.issues }
      )
    }

    // Check for duplicate customer name
    await this.checkDuplicate(
      "name",
      data.name,
      undefined,
      `Customer with name "${data.name}" already exists`
    )

    // Validate email format if provided
    if (data.contact_email && data.contact_email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.contact_email)) {
        throw new BusinessLogicError(
          ErrorCode.INVALID_FORMAT,
          "Invalid email format"
        )
      }
    }
  }

  protected async validateUpdate(id: string, data: UpdateCustomerData): Promise<void> {
    // Sanitize input data
    const sanitized = sanitizeFormData(data)
    
    // Validate with Zod schema (partial)
    const validation = customerSchema.partial().safeParse(sanitized)
    if (!validation.success) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Invalid customer data",
        { issues: validation.error.issues }
      )
    }

    // Check for duplicate name if name is being updated
    if (data.name) {
      await this.checkDuplicate(
        "name",
        data.name,
        id,
        `Customer with name "${data.name}" already exists`
      )
    }

    // Validate email format if provided
    if (data.contact_email && data.contact_email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.contact_email)) {
        throw new BusinessLogicError(
          ErrorCode.INVALID_FORMAT,
          "Invalid email format"
        )
      }
    }
  }

  protected async validateDelete(id: string): Promise<void> {
    // Check if customer has any sales contracts
    const salesContracts = await this.context.db.query.salesContracts.findFirst({
      where: (contracts: any, { eq }: any) => eq(contracts.customerId, id)
    })

    if (salesContracts) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Cannot delete customer with existing sales contracts"
      )
    }

    // Check if customer has any AR invoices
    const arInvoices = await this.context.db.query.arInvoices.findFirst({
      where: (invoices: any, { eq }: any) => eq(invoices.customerId, id)
    })

    if (arInvoices) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Cannot delete customer with existing invoices"
      )
    }
  }

  protected mapToEntity(data: any): Customer {
    return {
      id: data.id,
      name: data.name,
      contact_name: data.contact_name,
      contact_phone: data.contact_phone,
      contact_email: data.contact_email,
      address: data.address,
      tax_id: data.tax_id,
      bank: data.bank,
      incoterm: data.incoterm,
      payment_term: data.payment_term,
      status: data.status || "active",
      created_at: data.created_at,
      updated_at: data.updated_at
    }
  }

  protected mapCreateData(data: CreateCustomerData): any {
    return {
      id: uid("cust"),
      name: data.name,
      contact_name: data.contact_name,
      contact_phone: data.contact_phone,
      contact_email: data.contact_email,
      address: data.address,
      tax_id: data.tax_id,
      bank: data.bank,
      incoterm: data.incoterm,
      payment_term: data.payment_term,
      status: data.status || "active"
    }
  }

  protected mapUpdateData(data: UpdateCustomerData): any {
    const updateData: any = {}
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.contact_name !== undefined) updateData.contact_name = data.contact_name
    if (data.contact_phone !== undefined) updateData.contact_phone = data.contact_phone
    if (data.contact_email !== undefined) updateData.contact_email = data.contact_email
    if (data.address !== undefined) updateData.address = data.address
    if (data.tax_id !== undefined) updateData.tax_id = data.tax_id
    if (data.bank !== undefined) updateData.bank = data.bank
    if (data.incoterm !== undefined) updateData.incoterm = data.incoterm
    if (data.payment_term !== undefined) updateData.payment_term = data.payment_term
    if (data.status !== undefined) updateData.status = data.status

    return updateData
  }

  // Additional business methods
  async findByStatus(status: string): Promise<Customer[]> {
    try {
      const rows = await this.context.db.query.customers.findMany({
        where: (customers: any, { eq }: any) => eq(customers.status, status),
        orderBy: (customers: any, { desc }: any) => [desc(customers.created_at)]
      })

      return rows.map(row => this.mapToEntity(row))
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to fetch customers by status"
      )
    }
  }

  async searchByName(searchTerm: string): Promise<Customer[]> {
    try {
      const rows = await this.context.db.query.customers.findMany({
        where: (customers: any, { like }: any) => like(customers.name, `%${searchTerm}%`),
        orderBy: (customers: any, { asc }: any) => [asc(customers.name)]
      })

      return rows.map(row => this.mapToEntity(row))
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to search customers"
      )
    }
  }

  async updateStatus(id: string, status: string, user: any): Promise<Customer> {
    const validStatuses = ["active", "inactive"]
    if (!validStatuses.includes(status)) {
      throw new BusinessLogicError(
        ErrorCode.INVALID_FORMAT,
        "Invalid status value"
      )
    }

    return this.update(id, { status }, user)
  }

  async getCustomerStats(): Promise<{
    total: number
    active: number
    inactive: number
  }> {
    try {
      const allCustomers = await this.context.db.query.customers.findMany()
      
      const stats = {
        total: allCustomers.length,
        active: allCustomers.filter(c => c.status === "active").length,
        inactive: allCustomers.filter(c => c.status === "inactive").length
      }

      return stats
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to get customer statistics"
      )
    }
  }
}
