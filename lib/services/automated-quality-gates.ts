import { db, uid } from "@/lib/db"
import { 
  qualityInspections, 
  workOrders, 
  stockLots, 
  products,
  qualityStandards,
  declarationItems 
} from "@/lib/schema-postgres"
import { eq, and, gte, lte, count } from "drizzle-orm"
import { qualityWorkflowService } from "./quality-workflow"
import { certificateGenerationService } from "./certificate-generation"
import { 
  createInvalidWorkflowTransitionError,
  createMissingQualityStandardError 
} from "@/lib/errors"

export interface QualityGateRule {
  id: string
  name: string
  trigger: "work_order_start" | "work_order_complete" | "stock_outbound" | "export_declaration"
  conditions: QualityGateCondition[]
  actions: QualityGateAction[]
  priority: number
  enabled: boolean
}

export interface QualityGateCondition {
  type: "product_inspection_required" | "quality_status" | "certificate_valid" | "defect_severity" | "inspection_age"
  operator: "equals" | "not_equals" | "greater_than" | "less_than" | "exists" | "not_exists"
  value: string | number
  field?: string
}

export interface QualityGateAction {
  type: "block_operation" | "create_inspection" | "generate_certificate" | "send_notification" | "update_status"
  parameters: Record<string, any>
}

export class AutomatedQualityGatesService {
  
  private qualityGateRules: QualityGateRule[] = [
    {
      id: "production_start_gate",
      name: "Production Start Quality Gate",
      trigger: "work_order_start",
      conditions: [
        {
          type: "product_inspection_required",
          operator: "equals",
          value: "true"
        },
        {
          type: "quality_status",
          operator: "not_equals",
          value: "approved"
        }
      ],
      actions: [
        {
          type: "block_operation",
          parameters: {
            message: "Cannot start production: Quality approval required"
          }
        },
        {
          type: "create_inspection",
          parameters: {
            inspectionType: "pre-production",
            priority: "high"
          }
        }
      ],
      priority: 1,
      enabled: true
    },
    {
      id: "production_complete_gate",
      name: "Production Completion Quality Gate",
      trigger: "work_order_complete",
      conditions: [
        {
          type: "quality_status",
          operator: "not_equals",
          value: "approved"
        }
      ],
      actions: [
        {
          type: "block_operation",
          parameters: {
            message: "Cannot complete production: Final quality inspection required"
          }
        },
        {
          type: "create_inspection",
          parameters: {
            inspectionType: "final",
            priority: "high"
          }
        }
      ],
      priority: 1,
      enabled: true
    },
    {
      id: "stock_outbound_gate",
      name: "Stock Outbound Quality Gate",
      trigger: "stock_outbound",
      conditions: [
        {
          type: "quality_status",
          operator: "not_equals",
          value: "approved"
        }
      ],
      actions: [
        {
          type: "block_operation",
          parameters: {
            message: "Cannot ship stock: Quality approval required"
          }
        }
      ],
      priority: 1,
      enabled: true
    },
    {
      id: "export_declaration_gate",
      name: "Export Declaration Quality Gate",
      trigger: "export_declaration",
      conditions: [
        {
          type: "certificate_valid",
          operator: "not_exists",
          value: "true"
        }
      ],
      actions: [
        {
          type: "block_operation",
          parameters: {
            message: "Cannot export: Valid quality certificates required"
          }
        },
        {
          type: "generate_certificate",
          parameters: {
            certificateType: "COC",
            autoAssign: true
          }
        }
      ],
      priority: 1,
      enabled: true
    },
    {
      id: "auto_inspection_scheduling",
      name: "Automatic Inspection Scheduling",
      trigger: "work_order_complete",
      conditions: [
        {
          type: "product_inspection_required",
          operator: "equals",
          value: "true"
        }
      ],
      actions: [
        {
          type: "create_inspection",
          parameters: {
            inspectionType: "final",
            scheduleDelay: 0,
            autoAssignInspector: true
          }
        }
      ],
      priority: 2,
      enabled: true
    }
  ]

  /**
   * Evaluate quality gates for a specific trigger
   */
  async evaluateQualityGates(
    trigger: string, 
    entityId: string, 
    context: Record<string, any> = {}
  ): Promise<{
    allowed: boolean
    blockedBy: string[]
    actionsExecuted: string[]
    warnings: string[]
  }> {
    const result = {
      allowed: true,
      blockedBy: [],
      actionsExecuted: [],
      warnings: []
    }

    // Get applicable rules for this trigger
    const applicableRules = this.qualityGateRules
      .filter(rule => rule.trigger === trigger && rule.enabled)
      .sort((a, b) => a.priority - b.priority)

    for (const rule of applicableRules) {
      try {
        // Evaluate conditions
        const conditionsMet = await this.evaluateConditions(rule.conditions, entityId, context)
        
        if (conditionsMet) {
          // Execute actions
          const actionResults = await this.executeActions(rule.actions, entityId, context)
          
          // Check if any action blocks the operation
          const blockingActions = actionResults.filter(action => action.type === "block_operation")
          if (blockingActions.length > 0) {
            result.allowed = false
            result.blockedBy.push(rule.name)
          }
          
          result.actionsExecuted.push(...actionResults.map(a => `${rule.name}: ${a.type}`))
        }
      } catch (error) {
        result.warnings.push(`Rule ${rule.name} failed: ${error.message}`)
      }
    }

    return result
  }

  /**
   * Auto-schedule inspections based on product requirements and workflow state
   */
  async autoScheduleInspections(): Promise<{
    scheduled: number
    errors: string[]
  }> {
    const result = {
      scheduled: 0,
      errors: []
    }

    try {
      // Find work orders that need inspections
      const workOrdersNeedingInspection = await db.query.workOrders.findMany({
        where: and(
          eq(workOrders.status, "completed"),
          eq(workOrders.quality_status, "pending")
        ),
        with: {
          product: true,
          qualityInspections: true,
        },
      })

      for (const workOrder of workOrdersNeedingInspection) {
        try {
          // Check if final inspection already exists
          const hasFinalInspection = workOrder.qualityInspections?.some(
            insp => insp.inspection_type === "final"
          )

          if (!hasFinalInspection && workOrder.product.inspection_required === "true") {
            // Auto-schedule final inspection
            const inspectionIds = await qualityWorkflowService.triggerQualityInspections({
              triggerType: "work_order_completed",
              entityId: workOrder.id,
              productId: workOrder.product_id,
              quantity: workOrder.qty,
              notes: "Auto-scheduled by quality gates"
            })

            result.scheduled += inspectionIds.length
          }
        } catch (error) {
          result.errors.push(`Work Order ${workOrder.number}: ${error.message}`)
        }
      }

      // Find stock lots that need incoming inspections
      const stockLotsNeedingInspection = await db.query.stockLots.findMany({
        where: and(
          eq(stockLots.quality_status, "pending"),
          eq(stockLots.inspection_id, null)
        ),
        with: {
          product: true,
        },
      })

      for (const stockLot of stockLotsNeedingInspection) {
        try {
          if (stockLot.product.inspection_required === "true") {
            // Auto-schedule incoming inspection
            const inspectionIds = await qualityWorkflowService.triggerQualityInspections({
              triggerType: "stock_received",
              entityId: stockLot.id,
              productId: stockLot.product_id,
              quantity: stockLot.qty,
              notes: "Auto-scheduled by quality gates"
            })

            result.scheduled += inspectionIds.length
          }
        } catch (error) {
          result.errors.push(`Stock Lot ${stockLot.id}: ${error.message}`)
        }
      }

    } catch (error) {
      result.errors.push(`Auto-scheduling failed: ${error.message}`)
    }

    return result
  }

  /**
   * Validate quality requirements for export declarations
   */
  async validateExportQuality(declarationId: string): Promise<{
    canExport: boolean
    issues: string[]
    certificatesGenerated: number
  }> {
    const result = {
      canExport: true,
      issues: [],
      certificatesGenerated: 0
    }

    try {
      // Get declaration items
      const declarationItems = await db.query.declarationItems.findMany({
        where: eq(declarationItems.declaration_id, declarationId),
        with: {
          product: true,
          qualityCertificate: true,
        },
      })

      for (const item of declarationItems) {
        // Check if product requires quality certification for export
        if (item.product.inspection_required === "true") {
          if (!item.quality_certificate_id) {
            // Try to auto-generate certificate
            try {
              const certificateId = await this.autoGenerateExportCertificate(item.product_id)
              if (certificateId) {
                // Link certificate to declaration item
                await db
                  .update(declarationItems)
                  .set({ 
                    quality_certificate_id: certificateId,
                    quality_status: "approved" 
                  })
                  .where(eq(declarationItems.id, item.id))
                
                result.certificatesGenerated++
              } else {
                result.canExport = false
                result.issues.push(`Product ${item.product.name}: No valid quality certificate available`)
              }
            } catch (error) {
              result.canExport = false
              result.issues.push(`Product ${item.product.name}: Certificate generation failed`)
            }
          } else if (item.qualityCertificate) {
            // Check certificate validity
            const today = new Date().toISOString().split('T')[0]
            if (item.qualityCertificate.valid_until && item.qualityCertificate.valid_until < today) {
              result.canExport = false
              result.issues.push(`Product ${item.product.name}: Certificate expired`)
            }
          }
        }
      }

    } catch (error) {
      result.canExport = false
      result.issues.push(`Export validation failed: ${error.message}`)
    }

    return result
  }

  private async evaluateConditions(
    conditions: QualityGateCondition[], 
    entityId: string, 
    context: Record<string, any>
  ): Promise<boolean> {
    for (const condition of conditions) {
      const conditionMet = await this.evaluateCondition(condition, entityId, context)
      if (!conditionMet) {
        return false
      }
    }
    return true
  }

  private async evaluateCondition(
    condition: QualityGateCondition, 
    entityId: string, 
    context: Record<string, any>
  ): Promise<boolean> {
    switch (condition.type) {
      case "product_inspection_required":
        const product = await this.getProductForEntity(entityId, context)
        return this.compareValues(product?.inspection_required, condition.operator, condition.value)
        
      case "quality_status":
        const qualityStatus = await this.getQualityStatusForEntity(entityId, context)
        return this.compareValues(qualityStatus, condition.operator, condition.value)
        
      case "certificate_valid":
        const hasValidCertificate = await this.hasValidCertificate(entityId, context)
        return this.compareValues(hasValidCertificate, condition.operator, condition.value)
        
      default:
        return true
    }
  }

  private async executeActions(
    actions: QualityGateAction[], 
    entityId: string, 
    context: Record<string, any>
  ): Promise<Array<{type: string, success: boolean, message?: string}>> {
    const results = []

    for (const action of actions) {
      try {
        const result = await this.executeAction(action, entityId, context)
        results.push(result)
      } catch (error) {
        results.push({
          type: action.type,
          success: false,
          message: error.message
        })
      }
    }

    return results
  }

  private async executeAction(
    action: QualityGateAction, 
    entityId: string, 
    context: Record<string, any>
  ): Promise<{type: string, success: boolean, message?: string}> {
    switch (action.type) {
      case "block_operation":
        throw createInvalidWorkflowTransitionError(action.parameters.message || "Operation blocked by quality gate")
        
      case "create_inspection":
        // Implementation would create inspection
        return { type: action.type, success: true, message: "Inspection scheduled" }
        
      case "generate_certificate":
        // Implementation would generate certificate
        return { type: action.type, success: true, message: "Certificate generated" }
        
      default:
        return { type: action.type, success: true }
    }
  }

  private compareValues(actual: any, operator: string, expected: any): boolean {
    switch (operator) {
      case "equals": return actual === expected
      case "not_equals": return actual !== expected
      case "greater_than": return actual > expected
      case "less_than": return actual < expected
      case "exists": return actual != null
      case "not_exists": return actual == null
      default: return false
    }
  }

  private async getProductForEntity(entityId: string, context: Record<string, any>) {
    // Implementation would fetch product based on entity type
    return context.product || null
  }

  private async getQualityStatusForEntity(entityId: string, context: Record<string, any>) {
    // Implementation would fetch quality status based on entity type
    return context.qualityStatus || "pending"
  }

  private async hasValidCertificate(entityId: string, context: Record<string, any>): Promise<boolean> {
    // Implementation would check for valid certificates
    return false
  }

  private async autoGenerateExportCertificate(productId: string): Promise<string | null> {
    // Find a completed inspection for this product that can generate a COC
    const inspection = await db.query.qualityInspections.findFirst({
      where: and(
        eq(qualityInspections.status, "completed")
      ),
      with: {
        workOrder: {
          with: {
            product: true,
          },
        },
      },
    })

    if (inspection && inspection.workOrder?.product?.id === productId) {
      try {
        return await certificateGenerationService.generateCertificate({
          inspectionId: inspection.id,
          certificateType: "COC",
          validityDays: 180,
        })
      } catch (error) {
        return null
      }
    }

    return null
  }
}

// Export singleton instance
export const automatedQualityGatesService = new AutomatedQualityGatesService()
