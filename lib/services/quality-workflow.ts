import { db, uid } from "@/lib/db"
import { qualityInspections, stockLots, workOrders, products } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import {
  createReferenceNotFoundError,
  createInvalidQualityResultError
} from "@/lib/errors"
import { certificateGenerationService } from "./certificate-generation"

export interface QualityWorkflowTrigger {
  triggerType: "work_order_completed" | "stock_received" | "pre_shipment"
  entityId: string
  productId: string
  quantity: string
  notes?: string
}

export class QualityWorkflowService {
  
  /**
   * Automatically create quality inspections based on workflow triggers
   */
  async triggerQualityInspections(trigger: QualityWorkflowTrigger): Promise<string[]> {
    const inspectionIds: string[] = []

    try {
      // Get product quality requirements
      const product = await db.query.products.findFirst({
        where: eq(products.id, trigger.productId),
        with: {
          qualityStandards: true,
        },
      })

      if (!product) {
        throw createReferenceNotFoundError("Product", trigger.productId)
      }

      // Determine what inspections are needed based on trigger type and product requirements
      const requiredInspections = this.determineRequiredInspections(trigger, product)

      // Create the inspections
      for (const inspectionConfig of requiredInspections) {
        const inspectionId = uid("qi")
        
        const newInspection = {
          id: inspectionId,
          work_order_id: trigger.triggerType === "work_order_completed" ? trigger.entityId : null,
          inspection_type: inspectionConfig.type,
          inspector: "TBD", // To be assigned
          scheduled_date: this.calculateScheduledDate(inspectionConfig.type),
          status: "scheduled" as const,
          notes: `Auto-created ${inspectionConfig.type} inspection - ${trigger.notes || "Workflow trigger"}`,
        }

        await db.insert(qualityInspections).values(newInspection)
        inspectionIds.push(inspectionId)

        // If this is for stock received, link the inspection to the stock lot
        if (trigger.triggerType === "stock_received") {
          await db
            .update(stockLots)
            .set({ 
              inspection_id: inspectionId,
              quality_notes: `Linked to ${inspectionConfig.type} inspection ${inspectionId}` 
            })
            .where(eq(stockLots.id, trigger.entityId))
        }
      }

      return inspectionIds
    } catch (error) {
      throw error
    }
  }

  /**
   * Update quality status when inspections are completed
   */
  async processInspectionCompletion(inspectionId: string, result: "pass" | "fail" | "conditional"): Promise<void> {
    try {
      const inspection = await db.query.qualityInspections.findFirst({
        where: eq(qualityInspections.id, inspectionId),
        with: {
          workOrder: true,
        },
      })

      if (!inspection) {
        throw createReferenceNotFoundError("Quality Inspection", inspectionId)
      }

      // Update work order quality status if applicable
      if (inspection.work_order_id) {
        let newQualityStatus = "pending"
        
        if (result === "pass") {
          // Check if all inspections for this work order are complete and passed
          const allInspections = await db.query.qualityInspections.findMany({
            where: eq(qualityInspections.work_order_id, inspection.work_order_id),
          })

          const allPassed = allInspections.every(insp => 
            insp.id === inspectionId || 
            (insp.status === "completed" && this.getInspectionResult(insp) === "pass")
          )

          if (allPassed) {
            newQualityStatus = "approved"
          } else {
            newQualityStatus = "in_progress"
          }
        } else if (result === "fail") {
          newQualityStatus = "rejected"
        }

        await db
          .update(workOrders)
          .set({ quality_status: newQualityStatus })
          .where(eq(workOrders.id, inspection.work_order_id))
      }

      // Update stock lot quality status if linked
      const linkedStockLots = await db.query.stockLots.findMany({
        where: eq(stockLots.inspection_id, inspectionId),
      })

      for (const lot of linkedStockLots) {
        let newQualityStatus = "pending"
        
        if (result === "pass") {
          newQualityStatus = "approved"
        } else if (result === "fail") {
          newQualityStatus = "rejected"
        } else {
          newQualityStatus = "quarantined" // Conditional results require review
        }

        await db
          .update(stockLots)
          .set({ 
            quality_status: newQualityStatus,
            quality_notes: `Inspection ${inspectionId} result: ${result}` 
          })
          .where(eq(stockLots.id, lot.id))
      }

      // Auto-generate certificates if inspection passed
      if (result === "pass") {
        try {
          const certificateIds = await certificateGenerationService.autoGenerateCertificate(inspectionId)
          console.log(`Auto-generated ${certificateIds.length} certificates for inspection ${inspectionId}`)
        } catch (certError) {
          console.warn("Certificate generation failed:", certError)
          // Don't fail the entire workflow if certificate generation fails
        }
      }

    } catch (error) {
      throw error
    }
  }

  /**
   * Check if quality approval is required before allowing operations
   */
  async validateQualityGate(workOrderId: string, operation: "start_production" | "complete_production" | "ship_product"): Promise<boolean> {
    try {
      const workOrder = await db.query.workOrders.findFirst({
        where: eq(workOrders.id, workOrderId),
        with: {
          product: true,
          qualityInspections: true,
        },
      })

      if (!workOrder) {
        throw createReferenceNotFoundError("Work Order", workOrderId)
      }

      // Check quality requirements based on operation
      switch (operation) {
        case "start_production":
          // Can start if no quality issues or if incoming inspection passed
          return workOrder.quality_status !== "rejected"
          
        case "complete_production":
          // Can complete if quality is approved or not required
          return workOrder.quality_status === "approved" || workOrder.quality_required === "not_required"
          
        case "ship_product":
          // Must have quality approval for shipping
          return workOrder.quality_status === "approved"
          
        default:
          return true
      }
    } catch (error) {
      throw error
    }
  }

  private determineRequiredInspections(trigger: QualityWorkflowTrigger, product: any): Array<{type: string, priority: number}> {
    const inspections: Array<{type: string, priority: number}> = []

    // Base inspection requirements on product settings and trigger type
    if (product.inspection_required === "false") {
      return [] // No inspections needed
    }

    switch (trigger.triggerType) {
      case "stock_received":
        inspections.push({ type: "incoming", priority: 1 })
        break
        
      case "work_order_completed":
        inspections.push({ type: "final", priority: 1 })
        break
        
      case "pre_shipment":
        inspections.push({ type: "pre-shipment", priority: 1 })
        break
    }

    // Add in-process inspection for complex products
    if (product.quality_tolerance && trigger.triggerType === "work_order_completed") {
      inspections.push({ type: "in-process", priority: 2 })
    }

    return inspections.sort((a, b) => a.priority - b.priority)
  }

  private calculateScheduledDate(inspectionType: string): string {
    const now = new Date()
    let daysToAdd = 1 // Default: next day

    switch (inspectionType) {
      case "incoming":
        daysToAdd = 0 // Same day for incoming
        break
      case "in-process":
        daysToAdd = 1 // Next day
        break
      case "final":
        daysToAdd = 1 // Next day
        break
      case "pre-shipment":
        daysToAdd = 0 // Same day for pre-shipment
        break
    }

    const scheduledDate = new Date(now.getTime() + daysToAdd * 24 * 60 * 60 * 1000)
    return scheduledDate.toISOString().split('T')[0]
  }

  private getInspectionResult(inspection: any): "pass" | "fail" | "conditional" {
    // This would typically look at inspection_results table
    // For now, assume passed if completed without defects
    return "pass" // Simplified logic
  }
}

// Export singleton instance
export const qualityWorkflowService = new QualityWorkflowService()
