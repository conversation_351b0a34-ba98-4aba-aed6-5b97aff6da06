import { db } from "@/lib/db"
import { User } from "@/lib/auth"
import { 
  BusinessLogicError, 
  SystemError, 
  ErrorCode, 
  createDatabaseError,
  createReferenceNotFoundError 
} from "@/lib/errors"
import { 
  logSuccess, 
  logFailure, 
  AuditAction, 
  AuditResource 
} from "@/lib/audit"

// Base service interface
export interface IBaseService<T, TCreate, TUpdate> {
  findAll(filters?: Record<string, any>): Promise<T[]>
  findById(id: string): Promise<T | null>
  create(data: TCreate, user: User): Promise<T>
  update(id: string, data: TUpdate, user: User): Promise<T>
  delete(id: string, user: User): Promise<void>
}

// Service context for dependency injection
export interface ServiceContext {
  db: typeof db
  user?: User
  ipAddress?: string
  userAgent?: string
}

// Base service class with common functionality
export abstract class BaseService<T, TCreate, TUpdate> implements IBaseService<T, TCreate, TUpdate> {
  protected context: ServiceContext
  protected abstract tableName: string
  protected abstract auditResource: AuditResource

  constructor(context: ServiceContext) {
    this.context = context
  }

  // Abstract methods to be implemented by concrete services
  protected abstract getTable(): any
  protected abstract validateCreate(data: TCreate): Promise<void>
  protected abstract validateUpdate(id: string, data: TUpdate): Promise<void>
  protected abstract mapToEntity(data: any): T
  protected abstract mapCreateData(data: TCreate): any
  protected abstract mapUpdateData(data: TUpdate): any

  // Common CRUD operations
  async findAll(filters?: Record<string, any>): Promise<T[]> {
    try {
      const table = this.getTable()
      let query = this.context.db.query[this.tableName].findMany({
        orderBy: (table: any, { desc }: any) => [desc(table.created_at)]
      })

      // Apply filters if provided
      if (filters) {
        // This would be implemented based on specific filter requirements
        // For now, we'll use the basic query
      }

      const rows = await query
      
      // Log data access
      if (this.context.user) {
        logSuccess(
          this.context.user,
          AuditAction.VIEW,
          this.auditResource,
          undefined,
          { filters },
          this.context.ipAddress,
          this.context.userAgent
        )
      }

      return rows.map(row => this.mapToEntity(row))
    } catch (error) {
      if (this.context.user) {
        logFailure(
          this.context.user,
          AuditAction.VIEW,
          this.auditResource,
          error instanceof Error ? error.message : String(error),
          undefined,
          { filters },
          this.context.ipAddress,
          this.context.userAgent
        )
      }
      throw createDatabaseError("findAll", error)
    }
  }

  async findById(id: string): Promise<T | null> {
    try {
      const table = this.getTable()
      const row = await this.context.db.query[this.tableName].findFirst({
        where: (table: any, { eq }: any) => eq(table.id, id)
      })

      if (!row) {
        return null
      }

      // Log data access
      if (this.context.user) {
        logSuccess(
          this.context.user,
          AuditAction.VIEW,
          this.auditResource,
          id,
          undefined,
          this.context.ipAddress,
          this.context.userAgent
        )
      }

      return this.mapToEntity(row)
    } catch (error) {
      if (this.context.user) {
        logFailure(
          this.context.user,
          AuditAction.VIEW,
          this.auditResource,
          error instanceof Error ? error.message : String(error),
          id,
          undefined,
          this.context.ipAddress,
          this.context.userAgent
        )
      }
      throw createDatabaseError("findById", error)
    }
  }

  async create(data: TCreate, user: User): Promise<T> {
    try {
      // Validate input data
      await this.validateCreate(data)

      // Map to database format
      const dbData = this.mapCreateData(data)

      // Insert into database
      const table = this.getTable()
      await this.context.db.insert(table).values(dbData)

      // Fetch the created record
      const created = await this.findById(dbData.id)
      if (!created) {
        throw new SystemError(ErrorCode.DATABASE_ERROR, "Failed to retrieve created record")
      }

      // Log successful creation
      logSuccess(
        user,
        AuditAction.CREATE,
        this.auditResource,
        dbData.id,
        { data: dbData },
        this.context.ipAddress,
        this.context.userAgent
      )

      return created
    } catch (error) {
      // Log failed creation
      logFailure(
        user,
        AuditAction.CREATE,
        this.auditResource,
        error instanceof Error ? error.message : String(error),
        undefined,
        { data },
        this.context.ipAddress,
        this.context.userAgent
      )
      throw error
    }
  }

  async update(id: string, data: TUpdate, user: User): Promise<T> {
    try {
      // Check if record exists
      const existing = await this.findById(id)
      if (!existing) {
        throw createReferenceNotFoundError(this.tableName, id)
      }

      // Validate update data
      await this.validateUpdate(id, data)

      // Map to database format
      const dbData = this.mapUpdateData(data)

      // Update in database
      const table = this.getTable()
      await this.context.db
        .update(table)
        .set(dbData)
        .where((table: any, { eq }: any) => eq(table.id, id))

      // Fetch updated record
      const updated = await this.findById(id)
      if (!updated) {
        throw new SystemError(ErrorCode.DATABASE_ERROR, "Failed to retrieve updated record")
      }

      // Log successful update
      logSuccess(
        user,
        AuditAction.UPDATE,
        this.auditResource,
        id,
        { oldData: existing, newData: updated },
        this.context.ipAddress,
        this.context.userAgent
      )

      return updated
    } catch (error) {
      // Log failed update
      logFailure(
        user,
        AuditAction.UPDATE,
        this.auditResource,
        error instanceof Error ? error.message : String(error),
        id,
        { data },
        this.context.ipAddress,
        this.context.userAgent
      )
      throw error
    }
  }

  async delete(id: string, user: User): Promise<void> {
    try {
      // Check if record exists
      const existing = await this.findById(id)
      if (!existing) {
        throw createReferenceNotFoundError(this.tableName, id)
      }

      // Perform additional validation before deletion
      await this.validateDelete(id)

      // Delete from database
      const table = this.getTable()
      await this.context.db
        .delete(table)
        .where((table: any, { eq }: any) => eq(table.id, id))

      // Log successful deletion
      logSuccess(
        user,
        AuditAction.DELETE,
        this.auditResource,
        id,
        { deletedData: existing },
        this.context.ipAddress,
        this.context.userAgent
      )
    } catch (error) {
      // Log failed deletion
      logFailure(
        user,
        AuditAction.DELETE,
        this.auditResource,
        error instanceof Error ? error.message : String(error),
        id,
        undefined,
        this.context.ipAddress,
        this.context.userAgent
      )
      throw error
    }
  }

  // Optional validation for deletion (can be overridden)
  protected async validateDelete(id: string): Promise<void> {
    // Default implementation - no additional validation
  }

  // Utility method for checking references
  protected async checkReference(
    table: any,
    field: string,
    value: string,
    errorMessage?: string
  ): Promise<void> {
    const exists = await this.context.db.query[table].findFirst({
      where: (t: any, { eq }: any) => eq(t[field], value)
    })

    if (!exists) {
      throw createReferenceNotFoundError(
        table,
        value,
        errorMessage
      )
    }
  }

  // Utility method for checking duplicates
  protected async checkDuplicate(
    field: string,
    value: string,
    excludeId?: string,
    errorMessage?: string
  ): Promise<void> {
    const table = this.getTable()
    const existing = await this.context.db.query[this.tableName].findFirst({
      where: (t: any, { eq, and, ne }: any) => {
        const conditions = [eq(t[field], value)]
        if (excludeId) {
          conditions.push(ne(t.id, excludeId))
        }
        return and(...conditions)
      }
    })

    if (existing) {
      throw new BusinessLogicError(
        ErrorCode.DUPLICATE_SKU, // Generic duplicate error
        errorMessage || `${field} already exists`
      )
    }
  }
}

// Service factory for dependency injection
export class ServiceFactory {
  private context: ServiceContext

  constructor(context: ServiceContext) {
    this.context = context
  }

  // Method to create service instances with shared context
  createService<T extends BaseService<any, any, any>>(
    ServiceClass: new (context: ServiceContext) => T
  ): T {
    return new ServiceClass(this.context)
  }

  // Update context (useful for request-scoped services)
  updateContext(updates: Partial<ServiceContext>): ServiceFactory {
    return new ServiceFactory({ ...this.context, ...updates })
  }
}
