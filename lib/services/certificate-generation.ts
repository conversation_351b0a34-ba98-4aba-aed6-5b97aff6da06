import { db, uid } from "@/lib/db"
import { qualityCertificates, qualityInspections, inspectionResults } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { 
  createReferenceNotFoundError,
  createInvalidQualityResultError 
} from "@/lib/errors"

export interface CertificateGenerationRequest {
  inspectionId: string
  certificateType: "COA" | "COC" | "test_report" | "compliance"
  validityDays?: number
  customData?: Record<string, any>
}

export interface CertificateData {
  certificateNumber: string
  inspectionData: any
  productData: any
  customerData: any
  testResults: any[]
  defects: any[]
  complianceStatus: "compliant" | "non_compliant" | "conditional"
  generatedAt: string
}

export class CertificateGenerationService {
  
  /**
   * Automatically generate certificate when inspection passes
   */
  async autoGenerateCertificate(inspectionId: string): Promise<string[]> {
    const certificateIds: string[] = []

    try {
      // Get inspection details with all related data
      const inspection = await db.query.qualityInspections.findFirst({
        where: eq(qualityInspections.id, inspectionId),
        with: {
          workOrder: {
            with: {
              product: true,
              salesContract: {
                with: {
                  customer: true,
                },
              },
            },
          },
          defects: true,
          results: {
            with: {
              standard: true,
            },
          },
        },
      })

      if (!inspection) {
        throw createReferenceNotFoundError("Quality Inspection", inspectionId)
      }

      // Only generate certificates for completed inspections
      if (inspection.status !== "completed") {
        throw createInvalidQualityResultError("Cannot generate certificate for incomplete inspection")
      }

      // Determine what certificates to generate based on inspection type and results
      const certificatesToGenerate = this.determineCertificateTypes(inspection)

      // Generate each required certificate
      for (const certType of certificatesToGenerate) {
        const certificateId = await this.generateCertificate({
          inspectionId,
          certificateType: certType,
          validityDays: this.getValidityDays(certType),
        })
        certificateIds.push(certificateId)
      }

      return certificateIds
    } catch (error) {
      throw error
    }
  }

  /**
   * Generate a specific certificate
   */
  async generateCertificate(request: CertificateGenerationRequest): Promise<string> {
    try {
      // Validate inspection exists and is completed
      const inspection = await db.query.qualityInspections.findFirst({
        where: eq(qualityInspections.id, request.inspectionId),
        with: {
          workOrder: {
            with: {
              product: true,
              salesContract: {
                with: {
                  customer: true,
                },
              },
            },
          },
          defects: true,
          results: {
            with: {
              standard: true,
            },
          },
        },
      })

      if (!inspection) {
        throw createReferenceNotFoundError("Quality Inspection", request.inspectionId)
      }

      // Generate certificate number
      const certificateNumber = await this.generateCertificateNumber(request.certificateType, inspection)

      // Calculate validity dates
      const issuedDate = new Date().toISOString().split('T')[0]
      const validityDays = request.validityDays || this.getValidityDays(request.certificateType)
      const validUntil = new Date(Date.now() + validityDays * 24 * 60 * 60 * 1000)
        .toISOString().split('T')[0]

      // Create certificate record
      const certificateId = uid("qc")
      const newCertificate = {
        id: certificateId,
        inspection_id: request.inspectionId,
        certificate_number: certificateNumber,
        certificate_type: request.certificateType,
        issued_date: issuedDate,
        valid_until: validUntil,
        file_path: null, // Will be set when PDF is generated
      }

      await db.insert(qualityCertificates).values(newCertificate)

      // Generate certificate data for PDF generation
      const certificateData = await this.prepareCertificateData(certificateId, inspection)

      // In a real implementation, would generate PDF here
      console.log(`Certificate ${certificateNumber} generated for inspection ${request.inspectionId}`)

      return certificateId
    } catch (error) {
      throw error
    }
  }

  /**
   * Prepare certificate data for PDF generation
   */
  async prepareCertificateData(certificateId: string, inspection: any): Promise<CertificateData> {
    // Calculate compliance status based on inspection results
    const complianceStatus = this.calculateComplianceStatus(inspection)

    return {
      certificateNumber: await this.getCertificateNumber(certificateId),
      inspectionData: {
        id: inspection.id,
        type: inspection.inspection_type,
        inspector: inspection.inspector,
        completedDate: inspection.completed_date,
        notes: inspection.notes,
      },
      productData: {
        id: inspection.workOrder?.product?.id,
        sku: inspection.workOrder?.product?.sku,
        name: inspection.workOrder?.product?.name,
        unit: inspection.workOrder?.product?.unit,
        origin: inspection.workOrder?.product?.origin,
      },
      customerData: {
        id: inspection.workOrder?.salesContract?.customer?.id,
        name: inspection.workOrder?.salesContract?.customer?.name,
        address: inspection.workOrder?.salesContract?.customer?.address,
      },
      testResults: inspection.results || [],
      defects: inspection.defects || [],
      complianceStatus,
      generatedAt: new Date().toISOString(),
    }
  }

  private determineCertificateTypes(inspection: any): Array<"COA" | "COC" | "test_report" | "compliance"> {
    const certificates: Array<"COA" | "COC" | "test_report" | "compliance"> = []

    // Determine certificate types based on inspection type and results
    switch (inspection.inspection_type) {
      case "final":
        certificates.push("COA") // Certificate of Analysis for final inspection
        if (this.hasComplexTests(inspection)) {
          certificates.push("test_report")
        }
        break
        
      case "pre-shipment":
        certificates.push("COC") // Certificate of Compliance for shipping
        break
        
      case "incoming":
        if (this.requiresComplianceCert(inspection)) {
          certificates.push("compliance")
        }
        break
    }

    return certificates
  }

  private async generateCertificateNumber(certificateType: string, inspection: any): Promise<string> {
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')
    
    // Get count of certificates of this type this month
    const existingCount = await db.query.qualityCertificates.findMany({
      where: eq(qualityCertificates.certificate_type, certificateType),
    })

    const sequence = String(existingCount.length + 1).padStart(3, '0')
    
    const prefix = {
      'COA': 'COA',
      'COC': 'COC', 
      'test_report': 'TR',
      'compliance': 'COMP'
    }[certificateType] || 'CERT'

    return `${prefix}-${year}${month}-${sequence}`
  }

  private getValidityDays(certificateType: string): number {
    const validityMap = {
      'COA': 365,      // 1 year
      'COC': 180,      // 6 months
      'test_report': 730, // 2 years
      'compliance': 365   // 1 year
    }
    return validityMap[certificateType] || 365
  }

  private calculateComplianceStatus(inspection: any): "compliant" | "non_compliant" | "conditional" {
    // Check if there are any critical defects
    const criticalDefects = inspection.defects?.filter((d: any) => d.severity === "critical") || []
    if (criticalDefects.length > 0) {
      return "non_compliant"
    }

    // Check if there are any failed test results
    const failedResults = inspection.results?.filter((r: any) => r.result === "fail") || []
    if (failedResults.length > 0) {
      return "non_compliant"
    }

    // Check for conditional results
    const conditionalResults = inspection.results?.filter((r: any) => r.result === "conditional") || []
    if (conditionalResults.length > 0) {
      return "conditional"
    }

    return "compliant"
  }

  private hasComplexTests(inspection: any): boolean {
    return inspection.results && inspection.results.length > 3
  }

  private requiresComplianceCert(inspection: any): boolean {
    return inspection.workOrder?.salesContract?.customer?.name?.includes("Export") || false
  }

  private async getCertificateNumber(certificateId: string): Promise<string> {
    const cert = await db.query.qualityCertificates.findFirst({
      where: eq(qualityCertificates.id, certificateId)
    })
    return cert?.certificate_number || ""
  }
}

// Export singleton instance
export const certificateGenerationService = new CertificateGenerationService()
