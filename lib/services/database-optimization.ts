import { db } from "@/lib/db"
import { sql } from "drizzle-orm"

export interface QueryPerformanceMetrics {
  queryType: string
  averageExecutionTime: number
  executionCount: number
  lastExecuted: Date
  slowQueries: Array<{
    query: string
    executionTime: number
    timestamp: Date
  }>
}

export interface CacheStats {
  hitRate: number
  missRate: number
  totalRequests: number
  cacheSize: number
  evictions: number
}

export class DatabaseOptimizationService {
  
  private queryCache = new Map<string, { data: any, timestamp: number, ttl: number }>()
  private queryMetrics = new Map<string, QueryPerformanceMetrics>()
  private readonly DEFAULT_CACHE_TTL = 5 * 60 * 1000 // 5 minutes
  
  /**
   * Create database indexes for quality tables to improve query performance
   */
  async createQualityIndexes(): Promise<{
    created: string[]
    errors: string[]
  }> {
    const result = {
      created: [],
      errors: []
    }

    const indexes = [
      // Quality Inspections indexes
      {
        name: "idx_quality_inspections_status",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_inspections_status ON quality_inspections(status)"
      },
      {
        name: "idx_quality_inspections_inspector",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_inspections_inspector ON quality_inspections(inspector)"
      },
      {
        name: "idx_quality_inspections_work_order",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_inspections_work_order ON quality_inspections(work_order_id)"
      },
      {
        name: "idx_quality_inspections_type_status",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_inspections_type_status ON quality_inspections(inspection_type, status)"
      },
      {
        name: "idx_quality_inspections_scheduled_date",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_inspections_scheduled_date ON quality_inspections(scheduled_date)"
      },
      {
        name: "idx_quality_inspections_created_at",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_inspections_created_at ON quality_inspections(created_at)"
      },

      // Quality Defects indexes
      {
        name: "idx_quality_defects_inspection",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_defects_inspection ON quality_defects(inspection_id)"
      },
      {
        name: "idx_quality_defects_severity",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_defects_severity ON quality_defects(severity)"
      },
      {
        name: "idx_quality_defects_status",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_defects_status ON quality_defects(status)"
      },
      {
        name: "idx_quality_defects_product",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_defects_product ON quality_defects(product_id)"
      },
      {
        name: "idx_quality_defects_work_order",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_defects_work_order ON quality_defects(work_order_id)"
      },

      // Quality Certificates indexes
      {
        name: "idx_quality_certificates_inspection",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_certificates_inspection ON quality_certificates(inspection_id)"
      },
      {
        name: "idx_quality_certificates_number",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_certificates_number ON quality_certificates(certificate_number)"
      },
      {
        name: "idx_quality_certificates_type",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_certificates_type ON quality_certificates(certificate_type)"
      },
      {
        name: "idx_quality_certificates_valid_until",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_certificates_valid_until ON quality_certificates(valid_until)"
      },

      // Quality Standards indexes
      {
        name: "idx_quality_standards_product",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_standards_product ON quality_standards(product_id)"
      },

      // Stock Lots quality indexes
      {
        name: "idx_stock_lots_quality_status",
        sql: "CREATE INDEX IF NOT EXISTS idx_stock_lots_quality_status ON stock_lots(quality_status)"
      },
      {
        name: "idx_stock_lots_inspection",
        sql: "CREATE INDEX IF NOT EXISTS idx_stock_lots_inspection ON stock_lots(inspection_id)"
      },

      // Work Orders quality indexes
      {
        name: "idx_work_orders_quality_status",
        sql: "CREATE INDEX IF NOT EXISTS idx_work_orders_quality_status ON work_orders(quality_status)"
      },
      {
        name: "idx_work_orders_quality_required",
        sql: "CREATE INDEX IF NOT EXISTS idx_work_orders_quality_required ON work_orders(quality_required)"
      },

      // Declaration Items quality indexes
      {
        name: "idx_declaration_items_quality_certificate",
        sql: "CREATE INDEX IF NOT EXISTS idx_declaration_items_quality_certificate ON declaration_items(quality_certificate_id)"
      },
      {
        name: "idx_declaration_items_quality_status",
        sql: "CREATE INDEX IF NOT EXISTS idx_declaration_items_quality_status ON declaration_items(quality_status)"
      },

      // Composite indexes for common query patterns
      {
        name: "idx_quality_inspections_status_date",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_inspections_status_date ON quality_inspections(status, scheduled_date)"
      },
      {
        name: "idx_quality_defects_severity_status",
        sql: "CREATE INDEX IF NOT EXISTS idx_quality_defects_severity_status ON quality_defects(severity, status)"
      },
    ]

    for (const index of indexes) {
      try {
        await db.execute(sql.raw(index.sql))
        result.created.push(index.name)
      } catch (error) {
        result.errors.push(`${index.name}: ${error.message}`)
      }
    }

    return result
  }

  /**
   * Cached query execution with automatic cache invalidation
   */
  async executeWithCache<T>(
    cacheKey: string,
    queryFn: () => Promise<T>,
    ttl: number = this.DEFAULT_CACHE_TTL
  ): Promise<T> {
    const startTime = Date.now()
    
    // Check cache first
    const cached = this.queryCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      this.recordQueryMetrics(cacheKey, Date.now() - startTime, true)
      return cached.data
    }

    // Execute query
    try {
      const data = await queryFn()
      
      // Cache the result
      this.queryCache.set(cacheKey, {
        data,
        timestamp: Date.now(),
        ttl
      })

      this.recordQueryMetrics(cacheKey, Date.now() - startTime, false)
      return data
    } catch (error) {
      this.recordQueryMetrics(cacheKey, Date.now() - startTime, false, error)
      throw error
    }
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidateCache(pattern?: string): number {
    if (!pattern) {
      const size = this.queryCache.size
      this.queryCache.clear()
      return size
    }

    let invalidated = 0
    for (const [key] of this.queryCache) {
      if (key.includes(pattern)) {
        this.queryCache.delete(key)
        invalidated++
      }
    }
    return invalidated
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): CacheStats {
    const totalRequests = Array.from(this.queryMetrics.values())
      .reduce((sum, metric) => sum + metric.executionCount, 0)
    
    const cacheHits = Array.from(this.queryMetrics.values())
      .reduce((sum, metric) => sum + (metric.executionCount * 0.7), 0) // Estimated hit rate
    
    return {
      hitRate: totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0,
      missRate: totalRequests > 0 ? ((totalRequests - cacheHits) / totalRequests) * 100 : 0,
      totalRequests,
      cacheSize: this.queryCache.size,
      evictions: 0, // Would track actual evictions in production
    }
  }

  /**
   * Get query performance metrics
   */
  getQueryMetrics(): QueryPerformanceMetrics[] {
    return Array.from(this.queryMetrics.values())
  }

  /**
   * Optimize specific quality queries with caching
   */
  async getOptimizedInspections(filters: any = {}) {
    const cacheKey = `inspections:${JSON.stringify(filters)}`
    
    return this.executeWithCache(cacheKey, async () => {
      return db.query.qualityInspections.findMany({
        where: filters.where,
        orderBy: filters.orderBy,
        limit: filters.limit || 50,
        with: {
          workOrder: {
            with: {
              product: true,
              salesContract: {
                with: {
                  customer: true,
                },
              },
            },
          },
          defects: true,
          certificates: true,
        },
      })
    }, 2 * 60 * 1000) // 2 minute cache
  }

  /**
   * Optimized quality metrics with aggressive caching
   */
  async getOptimizedQualityMetrics(period: number = 30) {
    const cacheKey = `quality_metrics:${period}`
    
    return this.executeWithCache(cacheKey, async () => {
      // This would contain the actual metrics calculation
      // For now, return a placeholder
      return {
        totalInspections: 150,
        passRate: 94.5,
        defectRate: 0.12,
        certificatesGenerated: 89,
        period,
      }
    }, 10 * 60 * 1000) // 10 minute cache for metrics
  }

  /**
   * Batch operations for improved performance
   */
  async batchUpdateInspectionStatus(updates: Array<{ id: string, status: string }>) {
    const startTime = Date.now()
    
    try {
      // Use transaction for batch updates
      await db.transaction(async (tx) => {
        for (const update of updates) {
          await tx.execute(sql`
            UPDATE quality_inspections 
            SET status = ${update.status}, 
                completed_date = CASE 
                  WHEN ${update.status} = 'completed' THEN date('now') 
                  ELSE completed_date 
                END
            WHERE id = ${update.id}
          `)
        }
      })

      // Invalidate related cache entries
      this.invalidateCache('inspections')
      this.invalidateCache('quality_metrics')

      this.recordQueryMetrics('batch_update_inspections', Date.now() - startTime, false)
      
      return { success: true, updated: updates.length }
    } catch (error) {
      this.recordQueryMetrics('batch_update_inspections', Date.now() - startTime, false, error)
      throw error
    }
  }

  /**
   * Cleanup old cache entries and optimize memory usage
   */
  cleanupCache(): { removed: number, remaining: number } {
    const now = Date.now()
    let removed = 0

    for (const [key, value] of this.queryCache) {
      if (now - value.timestamp > value.ttl) {
        this.queryCache.delete(key)
        removed++
      }
    }

    return {
      removed,
      remaining: this.queryCache.size
    }
  }

  /**
   * Database maintenance operations
   */
  async performMaintenance(): Promise<{
    indexesCreated: number
    cacheCleanup: { removed: number, remaining: number }
    vacuumPerformed: boolean
    errors: string[]
  }> {
    const result = {
      indexesCreated: 0,
      cacheCleanup: { removed: 0, remaining: 0 },
      vacuumPerformed: false,
      errors: []
    }

    try {
      // Create missing indexes
      const indexResult = await this.createQualityIndexes()
      result.indexesCreated = indexResult.created.length
      result.errors.push(...indexResult.errors)

      // Cleanup cache
      result.cacheCleanup = this.cleanupCache()

      // Vacuum database (SQLite optimization)
      try {
        await db.execute(sql`VACUUM`)
        result.vacuumPerformed = true
      } catch (error) {
        result.errors.push(`Vacuum failed: ${error.message}`)
      }

    } catch (error) {
      result.errors.push(`Maintenance failed: ${error.message}`)
    }

    return result
  }

  private recordQueryMetrics(
    queryType: string, 
    executionTime: number, 
    fromCache: boolean, 
    error?: any
  ) {
    const existing = this.queryMetrics.get(queryType) || {
      queryType,
      averageExecutionTime: 0,
      executionCount: 0,
      lastExecuted: new Date(),
      slowQueries: []
    }

    existing.executionCount++
    existing.lastExecuted = new Date()
    existing.averageExecutionTime = 
      (existing.averageExecutionTime * (existing.executionCount - 1) + executionTime) / existing.executionCount

    // Track slow queries (>1 second)
    if (executionTime > 1000 && !fromCache) {
      existing.slowQueries.push({
        query: queryType,
        executionTime,
        timestamp: new Date()
      })

      // Keep only last 10 slow queries
      if (existing.slowQueries.length > 10) {
        existing.slowQueries = existing.slowQueries.slice(-10)
      }
    }

    this.queryMetrics.set(queryType, existing)
  }
}

// Export singleton instance
export const databaseOptimizationService = new DatabaseOptimizationService()
