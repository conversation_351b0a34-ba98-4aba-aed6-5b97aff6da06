import { BaseService, ServiceContext } from "./base"
import { products } from "@/lib/schema-postgres"
import { uid } from "@/lib/db"
import { AuditResource } from "@/lib/audit"
import { productSchema } from "@/lib/validations"
import { sanitizeFormData } from "@/lib/sanitization"
import { BusinessLogicError, ErrorCode, createDuplicateSKUError } from "@/lib/errors"

// Product types
export interface Product {
  id: string
  sku: string
  name: string
  unit: string
  hs_code?: string
  origin?: string
  package?: string
  image?: string
  created_at: string
  updated_at: string
}

export interface CreateProductData {
  sku: string
  name: string
  unit: string
  hs_code?: string
  origin?: string
  package?: string
  image?: string
}

export interface UpdateProductData {
  sku?: string
  name?: string
  unit?: string
  hs_code?: string
  origin?: string
  package?: string
  image?: string
}

export class ProductService extends BaseService<Product, CreateProductData, UpdateProductData> {
  protected tableName = "products"
  protected auditResource = AuditResource.PRODUCT

  protected getTable() {
    return products
  }

  protected async validateCreate(data: CreateProductData): Promise<void> {
    // Sanitize input data
    const sanitized = sanitizeFormData(data)
    
    // Validate with Zod schema
    const validation = productSchema.safeParse(sanitized)
    if (!validation.success) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Invalid product data",
        { issues: validation.error.issues }
      )
    }

    // Check for duplicate SKU
    await this.checkDuplicateSKU(data.sku)

    // Validate HS code format if provided
    if (data.hs_code && data.hs_code.trim()) {
      if (!/^\d{4,10}$/.test(data.hs_code.replace(/\./g, ""))) {
        throw new BusinessLogicError(
          ErrorCode.INVALID_FORMAT,
          "HS code must contain only digits and dots"
        )
      }
    }
  }

  protected async validateUpdate(id: string, data: UpdateProductData): Promise<void> {
    // Sanitize input data
    const sanitized = sanitizeFormData(data)
    
    // Validate with Zod schema (partial)
    const validation = productSchema.partial().safeParse(sanitized)
    if (!validation.success) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Invalid product data",
        { issues: validation.error.issues }
      )
    }

    // Check for duplicate SKU if SKU is being updated
    if (data.sku) {
      await this.checkDuplicateSKU(data.sku, id)
    }

    // Validate HS code format if provided
    if (data.hs_code && data.hs_code.trim()) {
      if (!/^\d{4,10}$/.test(data.hs_code.replace(/\./g, ""))) {
        throw new BusinessLogicError(
          ErrorCode.INVALID_FORMAT,
          "HS code must contain only digits and dots"
        )
      }
    }
  }

  protected async validateDelete(id: string): Promise<void> {
    // Check if product is used in any contract items
    const contractItems = await this.context.db.query.salesContractItems.findFirst({
      where: (items: any, { eq }: any) => eq(items.productId, id)
    })

    if (contractItems) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Cannot delete product that is used in contracts"
      )
    }

    // Check if product has any stock lots
    const stockLots = await this.context.db.query.stockLots.findFirst({
      where: (lots: any, { eq }: any) => eq(lots.productId, id)
    })

    if (stockLots) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Cannot delete product that has stock records"
      )
    }

    // Check if product is used in work orders
    const workOrders = await this.context.db.query.workOrders.findFirst({
      where: (orders: any, { eq }: any) => eq(orders.productId, id)
    })

    if (workOrders) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Cannot delete product that is used in work orders"
      )
    }
  }

  protected mapToEntity(data: any): Product {
    return {
      id: data.id,
      sku: data.sku,
      name: data.name,
      unit: data.unit,
      hs_code: data.hs_code,
      origin: data.origin,
      package: data.package,
      image: data.image,
      created_at: data.created_at,
      updated_at: data.updated_at
    }
  }

  protected mapCreateData(data: CreateProductData): any {
    return {
      id: uid("prod"),
      sku: data.sku,
      name: data.name,
      unit: data.unit,
      hs_code: data.hs_code,
      origin: data.origin,
      package: data.package,
      image: data.image
    }
  }

  protected mapUpdateData(data: UpdateProductData): any {
    const updateData: any = {}
    
    if (data.sku !== undefined) updateData.sku = data.sku
    if (data.name !== undefined) updateData.name = data.name
    if (data.unit !== undefined) updateData.unit = data.unit
    if (data.hs_code !== undefined) updateData.hs_code = data.hs_code
    if (data.origin !== undefined) updateData.origin = data.origin
    if (data.package !== undefined) updateData.package = data.package
    if (data.image !== undefined) updateData.image = data.image

    return updateData
  }

  // Custom validation for SKU duplicates
  private async checkDuplicateSKU(sku: string, excludeId?: string): Promise<void> {
    const existing = await this.context.db.query.products.findFirst({
      where: (products: any, { eq, and, ne }: any) => {
        const conditions = [eq(products.sku, sku)]
        if (excludeId) {
          conditions.push(ne(products.id, excludeId))
        }
        return and(...conditions)
      }
    })

    if (existing) {
      throw createDuplicateSKUError(sku)
    }
  }

  // Additional business methods
  async findBySKU(sku: string): Promise<Product | null> {
    try {
      const row = await this.context.db.query.products.findFirst({
        where: (products: any, { eq }: any) => eq(products.sku, sku)
      })

      return row ? this.mapToEntity(row) : null
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to find product by SKU"
      )
    }
  }

  async searchProducts(searchTerm: string): Promise<Product[]> {
    try {
      const rows = await this.context.db.query.products.findMany({
        where: (products: any, { or, like }: any) => or(
          like(products.sku, `%${searchTerm}%`),
          like(products.name, `%${searchTerm}%`)
        ),
        orderBy: (products: any, { asc }: any) => [asc(products.name)]
      })

      return rows.map(row => this.mapToEntity(row))
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to search products"
      )
    }
  }

  async getProductsByOrigin(origin: string): Promise<Product[]> {
    try {
      const rows = await this.context.db.query.products.findMany({
        where: (products: any, { eq }: any) => eq(products.origin, origin),
        orderBy: (products: any, { asc }: any) => [asc(products.name)]
      })

      return rows.map(row => this.mapToEntity(row))
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to get products by origin"
      )
    }
  }

  async getProductStats(): Promise<{
    total: number
    byOrigin: Record<string, number>
    byUnit: Record<string, number>
  }> {
    try {
      const allProducts = await this.context.db.query.products.findMany()
      
      const byOrigin: Record<string, number> = {}
      const byUnit: Record<string, number> = {}

      allProducts.forEach(product => {
        // Count by origin
        const origin = product.origin || "Unknown"
        byOrigin[origin] = (byOrigin[origin] || 0) + 1

        // Count by unit
        const unit = product.unit || "Unknown"
        byUnit[unit] = (byUnit[unit] || 0) + 1
      })

      return {
        total: allProducts.length,
        byOrigin,
        byUnit
      }
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to get product statistics"
      )
    }
  }

  // Get current stock levels for a product
  async getStockLevel(productId: string): Promise<{
    totalStock: number
    availableStock: number
    reservedStock: number
    stockLots: Array<{
      id: string
      qty: number
      location: string
      createdAt: string
    }>
  }> {
    try {
      const stockLots = await this.context.db.query.stockLots.findMany({
        where: (lots: any, { eq }: any) => eq(lots.productId, productId),
        orderBy: (lots: any, { asc }: any) => [asc(lots.created_at)]
      })

      const totalStock = stockLots.reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
      
      // For now, assume all stock is available (no reservations)
      // In a real system, you'd calculate reserved stock from pending orders
      const availableStock = totalStock
      const reservedStock = 0

      return {
        totalStock,
        availableStock,
        reservedStock,
        stockLots: stockLots.map(lot => ({
          id: lot.id,
          qty: parseFloat(lot.qty),
          location: lot.location,
          createdAt: lot.created_at
        }))
      }
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to get stock level"
      )
    }
  }
}
