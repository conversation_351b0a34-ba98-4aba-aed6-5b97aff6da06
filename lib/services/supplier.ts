import { BaseService, ServiceContext } from "./base"
import { suppliers } from "@/lib/schema-postgres"
import { uid } from "@/lib/db"
import { AuditResource } from "@/lib/audit"
import { supplierSchema } from "@/lib/validations"
import { sanitizeFormData } from "@/lib/sanitization"
import { BusinessLogicError, ErrorCode } from "@/lib/errors"

// Supplier types
export interface Supplier {
  id: string
  name: string
  contact_name?: string
  contact_phone?: string
  contact_email?: string
  address?: string
  tax_id?: string
  bank?: string
  status: string
  created_at: string
  updated_at: string
}

export interface CreateSupplierData {
  name: string
  contact_name?: string
  contact_phone?: string
  contact_email?: string
  address?: string
  tax_id?: string
  bank?: string
  status?: string
}

export interface UpdateSupplierData {
  name?: string
  contact_name?: string
  contact_phone?: string
  contact_email?: string
  address?: string
  tax_id?: string
  bank?: string
  status?: string
}

export class SupplierService extends BaseService<Supplier, CreateSupplierData, UpdateSupplierData> {
  protected tableName = "suppliers"
  protected auditResource = AuditResource.SUPPLIER

  protected getTable() {
    return suppliers
  }

  protected async validateCreate(data: CreateSupplierData): Promise<void> {
    // Sanitize input data
    const sanitized = sanitizeFormData(data)
    
    // Validate with Zod schema
    const validation = supplierSchema.safeParse(sanitized)
    if (!validation.success) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Invalid supplier data",
        { issues: validation.error.issues }
      )
    }

    // Check for duplicate supplier name
    await this.checkDuplicate(
      "name",
      data.name,
      undefined,
      `Supplier with name "${data.name}" already exists`
    )

    // Validate email format if provided
    if (data.contact_email && data.contact_email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.contact_email)) {
        throw new BusinessLogicError(
          ErrorCode.INVALID_FORMAT,
          "Invalid email format"
        )
      }
    }
  }

  protected async validateUpdate(id: string, data: UpdateSupplierData): Promise<void> {
    // Sanitize input data
    const sanitized = sanitizeFormData(data)
    
    // Validate with Zod schema (partial)
    const validation = supplierSchema.partial().safeParse(sanitized)
    if (!validation.success) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Invalid supplier data",
        { issues: validation.error.issues }
      )
    }

    // Check for duplicate name if name is being updated
    if (data.name) {
      await this.checkDuplicate(
        "name",
        data.name,
        id,
        `Supplier with name "${data.name}" already exists`
      )
    }

    // Validate email format if provided
    if (data.contact_email && data.contact_email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.contact_email)) {
        throw new BusinessLogicError(
          ErrorCode.INVALID_FORMAT,
          "Invalid email format"
        )
      }
    }
  }

  protected async validateDelete(id: string): Promise<void> {
    // Check if supplier has any purchase contracts
    const purchaseContracts = await this.context.db.query.purchaseContracts.findFirst({
      where: (contracts: any, { eq }: any) => eq(contracts.supplierId, id)
    })

    if (purchaseContracts) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Cannot delete supplier with existing purchase contracts"
      )
    }

    // Check if supplier has any AP invoices
    const apInvoices = await this.context.db.query.apInvoices.findFirst({
      where: (invoices: any, { eq }: any) => eq(invoices.supplierId, id)
    })

    if (apInvoices) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Cannot delete supplier with existing invoices"
      )
    }
  }

  protected mapToEntity(data: any): Supplier {
    return {
      id: data.id,
      name: data.name,
      contact_name: data.contact_name,
      contact_phone: data.contact_phone,
      contact_email: data.contact_email,
      address: data.address,
      tax_id: data.tax_id,
      bank: data.bank,
      status: data.status || "active",
      created_at: data.created_at,
      updated_at: data.updated_at
    }
  }

  protected mapCreateData(data: CreateSupplierData): any {
    return {
      id: uid("supp"),
      name: data.name,
      contact_name: data.contact_name,
      contact_phone: data.contact_phone,
      contact_email: data.contact_email,
      address: data.address,
      tax_id: data.tax_id,
      bank: data.bank,
      status: data.status || "active"
    }
  }

  protected mapUpdateData(data: UpdateSupplierData): any {
    const updateData: any = {}
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.contact_name !== undefined) updateData.contact_name = data.contact_name
    if (data.contact_phone !== undefined) updateData.contact_phone = data.contact_phone
    if (data.contact_email !== undefined) updateData.contact_email = data.contact_email
    if (data.address !== undefined) updateData.address = data.address
    if (data.tax_id !== undefined) updateData.tax_id = data.tax_id
    if (data.bank !== undefined) updateData.bank = data.bank
    if (data.status !== undefined) updateData.status = data.status

    return updateData
  }

  // Additional business methods
  async findByStatus(status: string): Promise<Supplier[]> {
    try {
      const rows = await this.context.db.query.suppliers.findMany({
        where: (suppliers: any, { eq }: any) => eq(suppliers.status, status),
        orderBy: (suppliers: any, { desc }: any) => [desc(suppliers.created_at)]
      })

      return rows.map(row => this.mapToEntity(row))
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to fetch suppliers by status"
      )
    }
  }

  async searchByName(searchTerm: string): Promise<Supplier[]> {
    try {
      const rows = await this.context.db.query.suppliers.findMany({
        where: (suppliers: any, { like }: any) => like(suppliers.name, `%${searchTerm}%`),
        orderBy: (suppliers: any, { asc }: any) => [asc(suppliers.name)]
      })

      return rows.map(row => this.mapToEntity(row))
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to search suppliers"
      )
    }
  }

  async updateStatus(id: string, status: string, user: any): Promise<Supplier> {
    const validStatuses = ["active", "inactive"]
    if (!validStatuses.includes(status)) {
      throw new BusinessLogicError(
        ErrorCode.INVALID_FORMAT,
        "Invalid status value"
      )
    }

    return this.update(id, { status }, user)
  }

  async getSupplierStats(): Promise<{
    total: number
    active: number
    inactive: number
  }> {
    try {
      const allSuppliers = await this.context.db.query.suppliers.findMany()
      
      const stats = {
        total: allSuppliers.length,
        active: allSuppliers.filter(s => s.status === "active").length,
        inactive: allSuppliers.filter(s => s.status === "inactive").length
      }

      return stats
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to get supplier statistics"
      )
    }
  }

  // Business logic for supplier evaluation
  async evaluateSupplier(id: string): Promise<{
    totalOrders: number
    totalValue: number
    averageOrderValue: number
    onTimeDeliveryRate: number
    qualityScore: number
  }> {
    try {
      // Get all purchase contracts for this supplier
      const contracts = await this.context.db.query.purchaseContracts.findMany({
        where: (contracts: any, { eq }: any) => eq(contracts.supplierId, id),
        with: {
          items: true
        }
      })

      const totalOrders = contracts.length
      const totalValue = contracts.reduce((sum, contract) => {
        return sum + contract.items.reduce((itemSum: number, item: any) => {
          return itemSum + (parseFloat(item.qty) * parseFloat(item.price))
        }, 0)
      }, 0)

      const averageOrderValue = totalOrders > 0 ? totalValue / totalOrders : 0

      // For now, return mock data for delivery and quality metrics
      // In a real system, this would be calculated from actual delivery and quality data
      const onTimeDeliveryRate = 0.85 // 85%
      const qualityScore = 4.2 // out of 5

      return {
        totalOrders,
        totalValue,
        averageOrderValue,
        onTimeDeliveryRate,
        qualityScore
      }
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to evaluate supplier"
      )
    }
  }
}
