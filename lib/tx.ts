import type { Sql } from "@neondatabase/serverless"

/**
 * Runs the given function in a transaction.
 * - Uses sql.begin if available.
 * - Falls back to manual BEGIN/COMMIT/ROLLBACK otherwise.
 */
export async function withTransaction<T>(sql: Sql, fn: (tx: Sql) => Promise<T>): Promise<T> {
  const anySql = sql as unknown as { begin?: (fn: (tx: Sql) => Promise<T>) => Promise<T> }
  if (typeof anySql.begin === "function") {
    return anySql.begin(fn)
  }
  await sql`begin`
  try {
    const res = await fn(sql)
    await sql`commit`
    return res
  } catch (e) {
    try {
      await sql`rollback`
    } catch {
      // ignore rollback errors
    }
    throw e
  }
}
