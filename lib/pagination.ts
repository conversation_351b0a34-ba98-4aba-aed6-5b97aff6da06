// Pagination utilities for performance optimization

export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

export interface PaginationResult<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
    nextPage?: number
    prevPage?: number
  }
}

export interface CursorPaginationParams {
  cursor?: string
  limit: number
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

export interface CursorPaginationResult<T> {
  data: T[]
  pagination: {
    limit: number
    hasNext: boolean
    hasPrev: boolean
    nextCursor?: string
    prevCursor?: string
  }
}

// Pagination utility class
export class PaginationHelper {
  
  // Calculate pagination metadata
  static calculatePagination(
    total: number,
    page: number,
    limit: number
  ): PaginationResult<any>["pagination"] {
    const totalPages = Math.ceil(total / limit)
    const hasNext = page < totalPages
    const hasPrev = page > 1

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev,
      nextPage: hasNext ? page + 1 : undefined,
      prevPage: hasPrev ? page - 1 : undefined
    }
  }

  // Create paginated result
  static createPaginatedResult<T>(
    data: T[],
    total: number,
    params: PaginationParams
  ): PaginationResult<T> {
    return {
      data,
      pagination: this.calculatePagination(total, params.page, params.limit)
    }
  }

  // Validate pagination parameters
  static validatePaginationParams(params: Partial<PaginationParams>): PaginationParams {
    const page = Math.max(1, params.page || 1)
    const limit = Math.min(100, Math.max(1, params.limit || 20))
    
    return {
      page,
      limit,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder || "desc"
    }
  }

  // Calculate offset for SQL queries
  static calculateOffset(page: number, limit: number): number {
    return (page - 1) * limit
  }

  // Create cursor from item
  static createCursor(item: any, sortBy: string): string {
    const value = item[sortBy]
    const id = item.id
    return Buffer.from(`${value}:${id}`).toString('base64')
  }

  // Parse cursor
  static parseCursor(cursor: string): { value: string; id: string } | null {
    try {
      const decoded = Buffer.from(cursor, 'base64').toString()
      const [value, id] = decoded.split(':')
      return { value, id }
    } catch {
      return null
    }
  }
}

// Enhanced service base class with pagination
export abstract class PaginatedService<T, TCreate, TUpdate> {
  
  // Paginated find all
  async findAllPaginated(
    params: PaginationParams,
    filters?: Record<string, any>
  ): Promise<PaginationResult<T>> {
    const validatedParams = PaginationHelper.validatePaginationParams(params)
    const offset = PaginationHelper.calculateOffset(validatedParams.page, validatedParams.limit)

    // This would be implemented by concrete services
    const { data, total } = await this.executeQuery(validatedParams, offset, filters)

    return PaginationHelper.createPaginatedResult(data, total, validatedParams)
  }

  // Cursor-based pagination (for real-time data)
  async findAllCursor(
    params: CursorPaginationParams,
    filters?: Record<string, any>
  ): Promise<CursorPaginationResult<T>> {
    const sortBy = params.sortBy || "created_at"
    const sortOrder = params.sortOrder || "desc"
    
    let whereCondition = ""
    let cursorData: { value: string; id: string } | null = null

    if (params.cursor) {
      cursorData = PaginationHelper.parseCursor(params.cursor)
      if (cursorData) {
        const operator = sortOrder === "desc" ? "<" : ">"
        whereCondition = `WHERE (${sortBy}, id) ${operator} ('${cursorData.value}', '${cursorData.id}')`
      }
    }

    const data = await this.executeCursorQuery(
      params.limit + 1, // Fetch one extra to check if there's more
      sortBy,
      sortOrder,
      whereCondition,
      filters
    )

    const hasNext = data.length > params.limit
    if (hasNext) {
      data.pop() // Remove the extra item
    }

    const nextCursor = hasNext && data.length > 0 
      ? PaginationHelper.createCursor(data[data.length - 1], sortBy)
      : undefined

    const prevCursor = cursorData && data.length > 0
      ? PaginationHelper.createCursor(data[0], sortBy)
      : undefined

    return {
      data,
      pagination: {
        limit: params.limit,
        hasNext,
        hasPrev: !!params.cursor,
        nextCursor,
        prevCursor
      }
    }
  }

  // Abstract methods to be implemented by concrete services
  protected abstract executeQuery(
    params: PaginationParams,
    offset: number,
    filters?: Record<string, any>
  ): Promise<{ data: T[]; total: number }>

  protected abstract executeCursorQuery(
    limit: number,
    sortBy: string,
    sortOrder: string,
    whereCondition: string,
    filters?: Record<string, any>
  ): Promise<T[]>
}

// React hook for pagination
export function usePagination(initialPage = 1, initialLimit = 20) {
  const [page, setPage] = useState(initialPage)
  const [limit, setLimit] = useState(initialLimit)
  const [sortBy, setSortBy] = useState<string>()
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  const goToPage = useCallback((newPage: number) => {
    setPage(Math.max(1, newPage))
  }, [])

  const goToNextPage = useCallback(() => {
    setPage(prev => prev + 1)
  }, [])

  const goToPrevPage = useCallback(() => {
    setPage(prev => Math.max(1, prev - 1))
  }, [])

  const changeLimit = useCallback((newLimit: number) => {
    setLimit(Math.min(100, Math.max(1, newLimit)))
    setPage(1) // Reset to first page when changing limit
  }, [])

  const changeSort = useCallback((field: string, order?: "asc" | "desc") => {
    setSortBy(field)
    setSortOrder(order || (sortBy === field && sortOrder === "asc" ? "desc" : "asc"))
    setPage(1) // Reset to first page when changing sort
  }, [sortBy, sortOrder])

  const reset = useCallback(() => {
    setPage(initialPage)
    setLimit(initialLimit)
    setSortBy(undefined)
    setSortOrder("desc")
  }, [initialPage, initialLimit])

  return {
    page,
    limit,
    sortBy,
    sortOrder,
    goToPage,
    goToNextPage,
    goToPrevPage,
    changeLimit,
    changeSort,
    reset,
    params: { page, limit, sortBy, sortOrder }
  }
}

// Pagination component helpers
export interface PaginationComponentProps {
  pagination: PaginationResult<any>["pagination"]
  onPageChange: (page: number) => void
  onLimitChange?: (limit: number) => void
  showLimitSelector?: boolean
  showPageInfo?: boolean
}

export function calculatePageNumbers(
  currentPage: number,
  totalPages: number,
  maxVisible = 5
): number[] {
  if (totalPages <= maxVisible) {
    return Array.from({ length: totalPages }, (_, i) => i + 1)
  }

  const half = Math.floor(maxVisible / 2)
  let start = Math.max(1, currentPage - half)
  let end = Math.min(totalPages, start + maxVisible - 1)

  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }

  return Array.from({ length: end - start + 1 }, (_, i) => start + i)
}

// Performance monitoring for pagination
export class PaginationPerformanceMonitor {
  private static metrics: Map<string, {
    totalQueries: number
    totalTime: number
    averageTime: number
    slowQueries: number
  }> = new Map()

  static startTimer(key: string): () => void {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      this.recordMetric(key, duration)
    }
  }

  private static recordMetric(key: string, duration: number): void {
    const existing = this.metrics.get(key) || {
      totalQueries: 0,
      totalTime: 0,
      averageTime: 0,
      slowQueries: 0
    }

    existing.totalQueries++
    existing.totalTime += duration
    existing.averageTime = existing.totalTime / existing.totalQueries
    
    if (duration > 1000) { // Queries taking more than 1 second
      existing.slowQueries++
    }

    this.metrics.set(key, existing)
  }

  static getMetrics(): Record<string, any> {
    return Object.fromEntries(this.metrics.entries())
  }

  static clearMetrics(): void {
    this.metrics.clear()
  }
}

// Import React hooks
import { useState, useCallback } from "react"
