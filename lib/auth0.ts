import {
  getSession,
  with<PERSON><PERSON><PERSON>uth<PERSON>equired,
  withPageAuthRequired
} from '@auth0/nextjs-auth0'
import { NextRequest, NextResponse } from 'next/server'
import { redirect } from 'next/navigation'

// Type definitions for Auth0 user
export interface Auth0User {
  sub: string // Auth0 user ID
  email: string
  email_verified: boolean
  name: string
  nickname?: string
  picture?: string
  updated_at: string
}

// Extended user type with company information
export interface UserWithCompany extends Auth0User {
  company?: {
    id: string
    name: string
    status: 'pending' | 'active' | 'inactive'
    onboarding_completed: boolean
  }
}

// Helper function to get current user session
export async function getCurrentUser(): Promise<Auth0User | null> {
  try {
    const session = await getSession()
    return session?.user as Auth0User || null
  } catch (error) {
    console.error('Error getting current user:', error)
    return null
  }
}

// Helper function to require authentication
export async function requireAuth(): Promise<Auth0User> {
  const user = await getCurrentUser()
  if (!user) {
    redirect('/api/auth/login')
  }
  return user
}

// Helper function to check if user has completed onboarding
export async function checkOnboardingStatus(userId: string): Promise<boolean> {
  try {
    // This will be implemented when we create the company API
    const response = await fetch(`/api/companies/by-user/${userId}`)
    if (response.ok) {
      const data = await response.json()
      return data.company?.onboarding_completed || false
    }
    return false
  } catch (error) {
    console.error('Error checking onboarding status:', error)
    return false
  }
}

// Middleware for protecting API routes
export function withAuth(handler: (req: NextRequest, user: Auth0User) => Promise<Response>) {
  return withApiAuthRequired(async (req: NextRequest) => {
    try {
      const session = await getSession(req)
      const user = session?.user as Auth0User

      if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      return await handler(req, user)
    } catch (error) {
      console.error('Auth middleware error:', error)
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 })
    }
  })
}

// Higher-order component for protecting pages
export function withAuthPage<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requireOnboarding?: boolean
    redirectTo?: string
  }
) {
  return withPageAuthRequired(Component, {
    returnTo: options?.redirectTo || '/dashboard'
  })
}

// Utility function to format Auth0 user for display
export function formatUserDisplay(user: Auth0User): string {
  return user.name || user.email || 'Unknown User'
}

// Utility function to get user avatar
export function getUserAvatar(user: Auth0User): string {
  return user.picture || `https://ui-avatars.com/api/?name=${encodeURIComponent(formatUserDisplay(user))}&background=0ea5e9&color=fff`
}

// Error handling for Auth0
export class AuthError extends Error {
  constructor(message: string, public code?: string) {
    super(message)
    this.name = 'AuthError'
  }
}

// Helper to handle Auth0 errors
export function handleAuthError(error: any): AuthError {
  if (error.code) {
    return new AuthError(error.message, error.code)
  }
  return new AuthError('Authentication error occurred')
}
