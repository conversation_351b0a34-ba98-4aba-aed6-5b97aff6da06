/**
 * Manufacturing ERP - Database Configuration Manager
 * 
 * Handles dual database support during PostgreSQL migration
 * Provides seamless switching between SQLite and PostgreSQL
 */

import { drizzle as drizzleSQLite } from "drizzle-orm/better-sqlite3";
import { drizzle as drizzlePostgreSQL } from "drizzle-orm/postgres-js";
import Database from "better-sqlite3";
import postgres from "postgres";
import * as schema from "./schema";
import path from "path";

// Migration configuration
interface MigrationConfig {
  usePostgreSQL: boolean;
  useSQLite: boolean;
  enableDualDatabase: boolean;
  migrationPhase: string;
  migrationDebug: boolean;
}

// Database connection configuration
interface DatabaseConfig {
  sqlite: {
    path: string;
    options: any;
  };
  postgresql: {
    url: string;
    options: any;
  };
}

/**
 * Get migration configuration from environment variables
 */
export function getMigrationConfig(): MigrationConfig {
  return {
    usePostgreSQL: process.env.USE_POSTGRESQL === 'true',
    useSQLite: process.env.USE_SQLITE !== 'false', // Default to true
    enableDualDatabase: process.env.ENABLE_DUAL_DATABASE === 'true',
    migrationPhase: process.env.MIGRATION_PHASE || 'development',
    migrationDebug: process.env.MIGRATION_DEBUG === 'true',
  };
}

/**
 * Get database configuration
 */
export function getDatabaseConfig(): DatabaseConfig {
  return {
    sqlite: {
      path: process.env.SQLITE_DATABASE_PATH || path.join(process.cwd(), "dev.db"),
      options: {
        verbose: process.env.MIGRATION_DEBUG === 'true' ? console.log : undefined,
      },
    },
    postgresql: {
      url: process.env.DATABASE_URL_POSTGRESQL || 
           process.env.POSTGRES_LOCAL_URL || 
           'postgresql://erp_user:SecureERP2024!@localhost:5432/manufacturing_erp',
      options: {
        max: 20, // Connection pool size
        idle_timeout: 20,
        connect_timeout: 10,
        debug: process.env.MIGRATION_DEBUG === 'true',
      },
    },
  };
}

/**
 * Create SQLite database connection
 */
export function createSQLiteConnection() {
  const config = getDatabaseConfig();
  const dbPath = config.sqlite.path;
  
  if (process.env.MIGRATION_DEBUG === 'true') {
    console.log(`🗄️ Connecting to SQLite: ${dbPath}`);
  }
  
  const sqlite = new Database(dbPath, config.sqlite.options);
  sqlite.pragma("journal_mode = WAL");
  sqlite.pragma("foreign_keys = ON");
  
  return drizzleSQLite(sqlite, { schema });
}

/**
 * Create PostgreSQL database connection
 */
export function createPostgreSQLConnection() {
  const config = getDatabaseConfig();
  const connectionUrl = config.postgresql.url;
  
  if (process.env.MIGRATION_DEBUG === 'true') {
    console.log(`🐘 Connecting to PostgreSQL: ${connectionUrl.replace(/:[^:@]*@/, ':***@')}`);
  }
  
  const client = postgres(connectionUrl, config.postgresql.options);
  
  return drizzlePostgreSQL(client, { schema });
}

/**
 * Get the primary database connection based on migration configuration
 */
export function getPrimaryDatabase() {
  const migrationConfig = getMigrationConfig();
  
  if (migrationConfig.usePostgreSQL) {
    if (process.env.MIGRATION_DEBUG === 'true') {
      console.log('📊 Using PostgreSQL as primary database');
    }
    return createPostgreSQLConnection();
  } else {
    if (process.env.MIGRATION_DEBUG === 'true') {
      console.log('📊 Using SQLite as primary database');
    }
    return createSQLiteConnection();
  }
}

/**
 * Get dual database connections for migration operations
 */
export function getDualDatabaseConnections() {
  const migrationConfig = getMigrationConfig();
  
  if (!migrationConfig.enableDualDatabase) {
    throw new Error('Dual database mode is not enabled. Set ENABLE_DUAL_DATABASE=true');
  }
  
  if (process.env.MIGRATION_DEBUG === 'true') {
    console.log('🔄 Creating dual database connections for migration');
  }
  
  return {
    sqlite: createSQLiteConnection(),
    postgresql: createPostgreSQLConnection(),
  };
}

/**
 * Test database connections
 */
export async function testDatabaseConnections() {
  const migrationConfig = getMigrationConfig();
  const results = {
    sqlite: { connected: false, error: null as string | null },
    postgresql: { connected: false, error: null as string | null },
  };

  // Test SQLite connection
  if (migrationConfig.useSQLite) {
    try {
      const sqliteDb = createSQLiteConnection();
      const result = await sqliteDb.select().from({ test: 'SELECT 1 as test' }).limit(1);
      results.sqlite.connected = true;
      console.log('✅ SQLite connection successful');
    } catch (error) {
      // Try alternative method for SQLite
      try {
        const config = getDatabaseConfig();
        const sqlite = new Database(config.sqlite.path);
        sqlite.prepare('SELECT 1 as test').get();
        results.sqlite.connected = true;
        console.log('✅ SQLite connection successful (direct)');
      } catch (innerError) {
        results.sqlite.error = error instanceof Error ? error.message : 'Unknown error';
        console.error('❌ SQLite connection failed:', results.sqlite.error);
      }
    }
  }

  // Test PostgreSQL connection
  if (migrationConfig.usePostgreSQL || migrationConfig.enableDualDatabase) {
    try {
      const postgresDb = createPostgreSQLConnection();
      const result = await postgresDb.select().from({ test: 'SELECT 1 as test' }).limit(1);
      results.postgresql.connected = true;
      console.log('✅ PostgreSQL connection successful');
    } catch (error) {
      results.postgresql.error = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ PostgreSQL connection failed:', results.postgresql.error);
    }
  }

  return results;
}

/**
 * Get database connection info for debugging
 */
export function getDatabaseInfo() {
  const migrationConfig = getMigrationConfig();
  const databaseConfig = getDatabaseConfig();
  
  return {
    migration: migrationConfig,
    config: {
      sqlite: {
        path: databaseConfig.sqlite.path,
        enabled: migrationConfig.useSQLite,
      },
      postgresql: {
        url: databaseConfig.postgresql.url.replace(/:[^:@]*@/, ':***@'),
        enabled: migrationConfig.usePostgreSQL || migrationConfig.enableDualDatabase,
      },
    },
    primary: migrationConfig.usePostgreSQL ? 'postgresql' : 'sqlite',
  };
}

/**
 * Utility function for generating UUIDs with prefixes (matching current system)
 */
export function uid(prefix = "id") {
  const g: any = globalThis as any;
  const id =
    g.crypto?.randomUUID?.() ??
    `${Math.random().toString(36).slice(2, 10)}${Math.random().toString(36).slice(2, 10)}`;
  return `${prefix}_${id}`;
}

// Export the primary database connection (backward compatibility)
export const db = getPrimaryDatabase();
