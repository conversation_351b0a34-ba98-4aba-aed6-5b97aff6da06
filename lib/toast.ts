import { toast as sonnerToast } from "sonner"
import { formatErrorForUser, isAppError, ErrorCode } from "./errors"

// Enhanced toast utilities with error handling
export const toast = {
  success: (message: string, description?: string) => {
    sonnerToast.success(message, {
      description,
      duration: 4000,
    })
  },

  error: (error: unknown, title?: string) => {
    const message = formatErrorForUser(error)
    const errorTitle = title || "Error"
    
    // Determine duration based on error type
    let duration = 6000 // Default 6 seconds for errors
    
    if (isAppError(error)) {
      switch (error.code) {
        case ErrorCode.VALIDATION_ERROR:
        case ErrorCode.REQUIRED_FIELD:
        case ErrorCode.INVALID_FORMAT:
          duration = 4000 // Shorter for validation errors
          break
        case ErrorCode.AUTHENTICATION_ERROR:
        case ErrorCode.AUTHORIZATION_ERROR:
          duration = 8000 // Longer for auth errors
          break
        default:
          duration = 6000
      }
    }

    sonnerToast.error(errorTitle, {
      description: message,
      duration,
    })
  },

  warning: (message: string, description?: string) => {
    sonnerToast.warning(message, {
      description,
      duration: 5000,
    })
  },

  info: (message: string, description?: string) => {
    sonnerToast.info(message, {
      description,
      duration: 4000,
    })
  },

  loading: (message: string, description?: string) => {
    return sonnerToast.loading(message, {
      description,
    })
  },

  promise: <T>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    }
  ) => {
    return sonnerToast.promise(promise, {
      loading,
      success,
      error: (err) => {
        const errorMessage = typeof error === 'function' ? error(err) : error
        return `${errorMessage}: ${formatErrorForUser(err)}`
      },
    })
  },

  dismiss: (id?: string | number) => {
    sonnerToast.dismiss(id)
  },

  // Specialized toasts for common operations
  saveSuccess: (itemType: string) => {
    toast.success(`${itemType} saved successfully`)
  },

  deleteSuccess: (itemType: string) => {
    toast.success(`${itemType} deleted successfully`)
  },

  updateSuccess: (itemType: string) => {
    toast.success(`${itemType} updated successfully`)
  },

  validationError: (message?: string) => {
    toast.error(
      message || "Please check your input and try again",
      "Validation Error"
    )
  },

  networkError: () => {
    toast.error(
      "Please check your internet connection and try again",
      "Network Error"
    )
  },

  permissionError: () => {
    toast.error(
      "You don't have permission to perform this action",
      "Permission Denied"
    )
  },

  // Business logic specific toasts
  insufficientStock: (productName: string, available: number, requested: number) => {
    toast.error(
      `Not enough stock for ${productName}. Available: ${available}, Requested: ${requested}`,
      "Insufficient Stock"
    )
  },

  duplicateSKU: (sku: string) => {
    toast.error(
      `A product with SKU "${sku}" already exists`,
      "Duplicate SKU"
    )
  },

  invalidStatusTransition: (from: string, to: string) => {
    toast.error(
      `Cannot change status from "${from}" to "${to}"`,
      "Invalid Status Change"
    )
  },

  // Async operation helpers
  async handleAsyncOperation<T>(
    operation: () => Promise<T>,
    {
      loadingMessage,
      successMessage,
      errorMessage,
    }: {
      loadingMessage: string
      successMessage: string | ((result: T) => string)
      errorMessage?: string
    }
  ): Promise<T | null> {
    const toastId = toast.loading(loadingMessage)
    
    try {
      const result = await operation()
      toast.dismiss(toastId)
      
      const message = typeof successMessage === 'function' 
        ? successMessage(result) 
        : successMessage
      toast.success(message)
      
      return result
    } catch (error) {
      toast.dismiss(toastId)
      toast.error(error, errorMessage)
      return null
    }
  },

  // Form submission helper
  async handleFormSubmission<T>(
    submitFn: () => Promise<T>,
    itemType: string
  ): Promise<T | null> {
    return this.handleAsyncOperation(submitFn, {
      loadingMessage: `Saving ${itemType}...`,
      successMessage: `${itemType} saved successfully`,
      errorMessage: `Failed to save ${itemType}`
    })
  },

  // Delete operation helper
  async handleDelete<T>(
    deleteFn: () => Promise<T>,
    itemType: string
  ): Promise<T | null> {
    return this.handleAsyncOperation(deleteFn, {
      loadingMessage: `Deleting ${itemType}...`,
      successMessage: `${itemType} deleted successfully`,
      errorMessage: `Failed to delete ${itemType}`
    })
  }
}
