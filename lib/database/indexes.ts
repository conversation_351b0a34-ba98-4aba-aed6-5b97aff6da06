import { sql } from "drizzle-orm"
import { db } from "@/lib/db"

// Database indexing strategy for performance optimization
export class DatabaseIndexManager {
  
  // Create indexes for frequently queried columns
  async createPerformanceIndexes(): Promise<void> {
    try {
      console.log("Creating performance indexes...")

      // Customer indexes
      await this.createIndex("idx_customers_name", "customers", ["name"])
      await this.createIndex("idx_customers_status", "customers", ["status"])
      await this.createIndex("idx_customers_created_at", "customers", ["created_at"])

      // Supplier indexes
      await this.createIndex("idx_suppliers_name", "suppliers", ["name"])
      await this.createIndex("idx_suppliers_status", "suppliers", ["status"])

      // Product indexes
      await this.createIndex("idx_products_sku", "products", ["sku"], true) // Unique
      await this.createIndex("idx_products_name", "products", ["name"])
      await this.createIndex("idx_products_origin", "products", ["origin"])
      await this.createIndex("idx_products_hs_code", "products", ["hs_code"])

      // Sample indexes
      await this.createIndex("idx_samples_code", "samples", ["code"], true) // Unique
      await this.createIndex("idx_samples_name", "samples", ["name"])
      await this.createIndex("idx_samples_date", "samples", ["date"])

      // Sales contract indexes
      await this.createIndex("idx_sales_contracts_number", "sales_contracts", ["number"], true)
      await this.createIndex("idx_sales_contracts_customer", "sales_contracts", ["customer_id"])
      await this.createIndex("idx_sales_contracts_status", "sales_contracts", ["status"])
      await this.createIndex("idx_sales_contracts_date", "sales_contracts", ["created_at"])

      // Purchase contract indexes
      await this.createIndex("idx_purchase_contracts_number", "purchase_contracts", ["number"], true)
      await this.createIndex("idx_purchase_contracts_supplier", "purchase_contracts", ["supplier_id"])
      await this.createIndex("idx_purchase_contracts_status", "purchase_contracts", ["status"])

      // Contract items indexes
      await this.createIndex("idx_sales_contract_items_contract", "sales_contract_items", ["contract_id"])
      await this.createIndex("idx_sales_contract_items_product", "sales_contract_items", ["product_id"])
      await this.createIndex("idx_purchase_contract_items_contract", "purchase_contract_items", ["contract_id"])
      await this.createIndex("idx_purchase_contract_items_product", "purchase_contract_items", ["product_id"])

      // Work order indexes
      await this.createIndex("idx_work_orders_number", "work_orders", ["number"], true)
      await this.createIndex("idx_work_orders_contract", "work_orders", ["sales_contract_id"])
      await this.createIndex("idx_work_orders_product", "work_orders", ["product_id"])
      await this.createIndex("idx_work_orders_status", "work_orders", ["status"])

      // Stock lot indexes
      await this.createIndex("idx_stock_lots_product", "stock_lots", ["product_id"])
      await this.createIndex("idx_stock_lots_location", "stock_lots", ["location"])
      await this.createIndex("idx_stock_lots_created", "stock_lots", ["created_at"])
      await this.createIndex("idx_stock_lots_product_location", "stock_lots", ["product_id", "location"])

      // Stock transaction indexes
      await this.createIndex("idx_stock_txns_product", "stock_txns", ["product_id"])
      await this.createIndex("idx_stock_txns_type", "stock_txns", ["type"])
      await this.createIndex("idx_stock_txns_location", "stock_txns", ["location"])
      await this.createIndex("idx_stock_txns_created", "stock_txns", ["created_at"])
      await this.createIndex("idx_stock_txns_product_type", "stock_txns", ["product_id", "type"])

      // Declaration indexes
      await this.createIndex("idx_declarations_number", "declarations", ["number"], true)
      await this.createIndex("idx_declarations_status", "declarations", ["status"])
      await this.createIndex("idx_declarations_date", "declarations", ["created_at"])

      // Declaration items indexes
      await this.createIndex("idx_declaration_items_declaration", "declaration_items", ["declaration_id"])
      await this.createIndex("idx_declaration_items_product", "declaration_items", ["product_id"])

      // AR Invoice indexes
      await this.createIndex("idx_ar_invoices_number", "ar_invoices", ["number"], true)
      await this.createIndex("idx_ar_invoices_customer", "ar_invoices", ["customer_id"])
      await this.createIndex("idx_ar_invoices_date", "ar_invoices", ["date"])
      await this.createIndex("idx_ar_invoices_status", "ar_invoices", ["status"])

      // AP Invoice indexes
      await this.createIndex("idx_ap_invoices_number", "ap_invoices", ["number"], true)
      await this.createIndex("idx_ap_invoices_supplier", "ap_invoices", ["supplier_id"])
      await this.createIndex("idx_ap_invoices_date", "ap_invoices", ["date"])
      await this.createIndex("idx_ap_invoices_status", "ap_invoices", ["status"])

      // Composite indexes for common queries
      await this.createIndex("idx_contracts_customer_status", "sales_contracts", ["customer_id", "status"])
      await this.createIndex("idx_contracts_status_date", "sales_contracts", ["status", "created_at"])
      await this.createIndex("idx_stock_product_location_qty", "stock_lots", ["product_id", "location", "qty"])

      console.log("Performance indexes created successfully")
    } catch (error) {
      console.error("Error creating performance indexes:", error)
      throw error
    }
  }

  // Create a single index
  private async createIndex(
    indexName: string, 
    tableName: string, 
    columns: string[], 
    unique = false
  ): Promise<void> {
    try {
      const uniqueKeyword = unique ? "UNIQUE" : ""
      const columnList = columns.join(", ")
      const query = `CREATE ${uniqueKeyword} INDEX IF NOT EXISTS ${indexName} ON ${tableName} (${columnList})`
      
      await db.execute(sql.raw(query))
      console.log(`Created index: ${indexName}`)
    } catch (error) {
      console.warn(`Failed to create index ${indexName}:`, error)
      // Don't throw - continue with other indexes
    }
  }

  // Drop all performance indexes (for cleanup)
  async dropPerformanceIndexes(): Promise<void> {
    const indexes = [
      "idx_customers_name", "idx_customers_status", "idx_customers_created_at",
      "idx_suppliers_name", "idx_suppliers_status",
      "idx_products_sku", "idx_products_name", "idx_products_origin", "idx_products_hs_code",
      "idx_samples_code", "idx_samples_name", "idx_samples_date",
      "idx_sales_contracts_number", "idx_sales_contracts_customer", "idx_sales_contracts_status", "idx_sales_contracts_date",
      "idx_purchase_contracts_number", "idx_purchase_contracts_supplier", "idx_purchase_contracts_status",
      "idx_sales_contract_items_contract", "idx_sales_contract_items_product",
      "idx_purchase_contract_items_contract", "idx_purchase_contract_items_product",
      "idx_work_orders_number", "idx_work_orders_contract", "idx_work_orders_product", "idx_work_orders_status",
      "idx_stock_lots_product", "idx_stock_lots_location", "idx_stock_lots_created", "idx_stock_lots_product_location",
      "idx_stock_txns_product", "idx_stock_txns_type", "idx_stock_txns_location", "idx_stock_txns_created", "idx_stock_txns_product_type",
      "idx_declarations_number", "idx_declarations_status", "idx_declarations_date",
      "idx_declaration_items_declaration", "idx_declaration_items_product",
      "idx_ar_invoices_number", "idx_ar_invoices_customer", "idx_ar_invoices_date", "idx_ar_invoices_status",
      "idx_ap_invoices_number", "idx_ap_invoices_supplier", "idx_ap_invoices_date", "idx_ap_invoices_status",
      "idx_contracts_customer_status", "idx_contracts_status_date", "idx_stock_product_location_qty"
    ]

    for (const indexName of indexes) {
      try {
        await db.execute(sql.raw(`DROP INDEX IF EXISTS ${indexName}`))
        console.log(`Dropped index: ${indexName}`)
      } catch (error) {
        console.warn(`Failed to drop index ${indexName}:`, error)
      }
    }
  }

  // Analyze query performance
  async analyzeQueryPerformance(query: string): Promise<any> {
    try {
      const explainQuery = `EXPLAIN QUERY PLAN ${query}`
      const result = await db.execute(sql.raw(explainQuery))
      return result
    } catch (error) {
      console.error("Error analyzing query performance:", error)
      return null
    }
  }

  // Get index usage statistics (SQLite specific)
  async getIndexStats(): Promise<any[]> {
    try {
      // This would work with PostgreSQL or MySQL
      // For SQLite, we'll return a mock response
      return [
        { index_name: "Performance indexes created", usage_count: "N/A (SQLite)" }
      ]
    } catch (error) {
      console.error("Error getting index stats:", error)
      return []
    }
  }

  // Optimize database (SQLite specific)
  async optimizeDatabase(): Promise<void> {
    try {
      console.log("Optimizing database...")
      
      // Analyze tables to update statistics
      await db.execute(sql.raw("ANALYZE"))
      
      // Vacuum to reclaim space and defragment
      await db.execute(sql.raw("VACUUM"))
      
      // Reindex all indexes
      await db.execute(sql.raw("REINDEX"))
      
      console.log("Database optimization completed")
    } catch (error) {
      console.error("Error optimizing database:", error)
      throw error
    }
  }
}

// Export singleton instance
export const dbIndexManager = new DatabaseIndexManager()

// Utility function to initialize performance indexes
export async function initializePerformanceIndexes(): Promise<void> {
  await dbIndexManager.createPerformanceIndexes()
}

// Query optimization helpers
export class QueryOptimizer {
  
  // Optimize pagination queries
  static buildPaginatedQuery(
    baseQuery: string,
    page: number = 1,
    limit: number = 20,
    orderBy: string = "created_at",
    orderDirection: "ASC" | "DESC" = "DESC"
  ): string {
    const offset = (page - 1) * limit
    return `
      ${baseQuery}
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT ${limit} OFFSET ${offset}
    `
  }

  // Build efficient search queries
  static buildSearchQuery(
    tableName: string,
    searchColumns: string[],
    searchTerm: string,
    additionalWhere?: string
  ): string {
    const searchConditions = searchColumns
      .map(col => `${col} LIKE '%${searchTerm}%'`)
      .join(" OR ")
    
    const whereClause = additionalWhere 
      ? `WHERE (${searchConditions}) AND (${additionalWhere})`
      : `WHERE (${searchConditions})`
    
    return `SELECT * FROM ${tableName} ${whereClause}`
  }

  // Build efficient filter queries
  static buildFilterQuery(
    tableName: string,
    filters: Record<string, any>,
    searchTerm?: string,
    searchColumns?: string[]
  ): string {
    const conditions: string[] = []
    
    // Add search conditions
    if (searchTerm && searchColumns) {
      const searchConditions = searchColumns
        .map(col => `${col} LIKE '%${searchTerm}%'`)
        .join(" OR ")
      conditions.push(`(${searchConditions})`)
    }
    
    // Add filter conditions
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        if (Array.isArray(value)) {
          const values = value.map(v => `'${v}'`).join(", ")
          conditions.push(`${key} IN (${values})`)
        } else {
          conditions.push(`${key} = '${value}'`)
        }
      }
    })
    
    const whereClause = conditions.length > 0 
      ? `WHERE ${conditions.join(" AND ")}`
      : ""
    
    return `SELECT * FROM ${tableName} ${whereClause}`
  }
}
