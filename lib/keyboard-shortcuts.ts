"use client"

import { useEffect, useCallback } from "react"

// Keyboard shortcut types
export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  description: string
  action: () => void
  preventDefault?: boolean
  stopPropagation?: boolean
  disabled?: boolean
}

export interface ShortcutGroup {
  name: string
  shortcuts: KeyboardShortcut[]
}

// Keyboard shortcut manager
class KeyboardShortcutManager {
  private shortcuts: Map<string, KeyboardShortcut> = new Map()
  private listeners: Set<(event: KeyboardEvent) => void> = new Set()

  constructor() {
    if (typeof window !== "undefined") {
      this.handleKeyDown = this.handleKeyDown.bind(this)
      document.addEventListener("keydown", this.handleKeyDown)
    }
  }

  private createShortcutKey(shortcut: Omit<KeyboardShortcut, "description" | "action">): string {
    const parts = []
    if (shortcut.ctrlKey) parts.push("ctrl")
    if (shortcut.altKey) parts.push("alt")
    if (shortcut.shiftKey) parts.push("shift")
    if (shortcut.metaKey) parts.push("meta")
    parts.push(shortcut.key.toLowerCase())
    return parts.join("+")
  }

  register(shortcut: KeyboardShortcut): () => void {
    const key = this.createShortcutKey(shortcut)
    this.shortcuts.set(key, shortcut)

    // Return unregister function
    return () => {
      this.shortcuts.delete(key)
    }
  }

  unregister(shortcut: Omit<KeyboardShortcut, "description" | "action">): void {
    const key = this.createShortcutKey(shortcut)
    this.shortcuts.delete(key)
  }

  private handleKeyDown(event: KeyboardEvent): void {
    // Don't trigger shortcuts when typing in inputs
    const target = event.target as HTMLElement
    if (
      target.tagName === "INPUT" ||
      target.tagName === "TEXTAREA" ||
      target.contentEditable === "true"
    ) {
      return
    }

    const key = this.createShortcutKey({
      key: event.key,
      ctrlKey: event.ctrlKey,
      altKey: event.altKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    })

    const shortcut = this.shortcuts.get(key)
    if (shortcut && !shortcut.disabled) {
      if (shortcut.preventDefault !== false) {
        event.preventDefault()
      }
      if (shortcut.stopPropagation !== false) {
        event.stopPropagation()
      }
      shortcut.action()
    }
  }

  getRegisteredShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values())
  }

  destroy(): void {
    if (typeof window !== "undefined") {
      document.removeEventListener("keydown", this.handleKeyDown)
    }
    this.shortcuts.clear()
  }
}

// Global shortcut manager instance
export const shortcutManager = new KeyboardShortcutManager()

// React hook for keyboard shortcuts
export function useKeyboardShortcut(shortcut: KeyboardShortcut): void {
  useEffect(() => {
    const unregister = shortcutManager.register(shortcut)
    return unregister
  }, [shortcut])
}

// Hook for multiple shortcuts
export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[]): void {
  useEffect(() => {
    const unregisterFunctions = shortcuts.map(shortcut => 
      shortcutManager.register(shortcut)
    )

    return () => {
      unregisterFunctions.forEach(unregister => unregister())
    }
  }, [shortcuts])
}

// Common shortcut patterns
export const createNavigationShortcuts = (
  onNew?: () => void,
  onSave?: () => void,
  onDelete?: () => void,
  onSearch?: () => void,
  onRefresh?: () => void
): KeyboardShortcut[] => {
  const shortcuts: KeyboardShortcut[] = []

  if (onNew) {
    shortcuts.push({
      key: "n",
      ctrlKey: true,
      description: "Create new item",
      action: onNew
    })
  }

  if (onSave) {
    shortcuts.push({
      key: "s",
      ctrlKey: true,
      description: "Save current item",
      action: onSave
    })
  }

  if (onDelete) {
    shortcuts.push({
      key: "Delete",
      description: "Delete selected item",
      action: onDelete
    })
  }

  if (onSearch) {
    shortcuts.push({
      key: "f",
      ctrlKey: true,
      description: "Focus search",
      action: onSearch
    })
  }

  if (onRefresh) {
    shortcuts.push({
      key: "r",
      ctrlKey: true,
      description: "Refresh data",
      action: onRefresh
    })
  }

  return shortcuts
}

// ERP-specific shortcuts
export const createERPShortcuts = (actions: {
  onNewCustomer?: () => void
  onNewProduct?: () => void
  onNewContract?: () => void
  onNewWorkOrder?: () => void
  onInventoryView?: () => void
  onDashboard?: () => void
  onHelp?: () => void
}): ShortcutGroup[] => {
  return [
    {
      name: "Navigation",
      shortcuts: [
        {
          key: "d",
          altKey: true,
          description: "Go to Dashboard",
          action: actions.onDashboard || (() => window.location.href = "/")
        },
        {
          key: "i",
          altKey: true,
          description: "Go to Inventory",
          action: actions.onInventoryView || (() => window.location.href = "/inventory")
        },
        {
          key: "?",
          description: "Show help",
          action: actions.onHelp || (() => console.log("Help"))
        }
      ]
    },
    {
      name: "Quick Actions",
      shortcuts: [
        ...(actions.onNewCustomer ? [{
          key: "c",
          ctrlKey: true,
          shiftKey: true,
          description: "New Customer",
          action: actions.onNewCustomer
        }] : []),
        ...(actions.onNewProduct ? [{
          key: "p",
          ctrlKey: true,
          shiftKey: true,
          description: "New Product",
          action: actions.onNewProduct
        }] : []),
        ...(actions.onNewContract ? [{
          key: "o",
          ctrlKey: true,
          shiftKey: true,
          description: "New Contract",
          action: actions.onNewContract
        }] : []),
        ...(actions.onNewWorkOrder ? [{
          key: "w",
          ctrlKey: true,
          shiftKey: true,
          description: "New Work Order",
          action: actions.onNewWorkOrder
        }] : [])
      ]
    }
  ]
}

// Shortcut display helper
export function formatShortcut(shortcut: Omit<KeyboardShortcut, "description" | "action">): string {
  const parts = []
  
  if (shortcut.ctrlKey) parts.push("Ctrl")
  if (shortcut.altKey) parts.push("Alt")
  if (shortcut.shiftKey) parts.push("Shift")
  if (shortcut.metaKey) parts.push("Cmd")
  
  // Format key name
  let keyName = shortcut.key
  if (keyName === " ") keyName = "Space"
  else if (keyName === "ArrowUp") keyName = "↑"
  else if (keyName === "ArrowDown") keyName = "↓"
  else if (keyName === "ArrowLeft") keyName = "←"
  else if (keyName === "ArrowRight") keyName = "→"
  else if (keyName === "Enter") keyName = "Enter"
  else if (keyName === "Escape") keyName = "Esc"
  else keyName = keyName.toUpperCase()
  
  parts.push(keyName)
  
  return parts.join(" + ")
}

// Hook for command palette functionality
export function useCommandPalette(
  commands: Array<{
    id: string
    label: string
    shortcut?: string
    action: () => void
    category?: string
  }>,
  isOpen: boolean,
  onClose: () => void
) {
  useKeyboardShortcut({
    key: "k",
    ctrlKey: true,
    description: "Open command palette",
    action: () => {
      if (isOpen) {
        onClose()
      } else {
        // Open command palette
      }
    }
  })

  useKeyboardShortcut({
    key: "Escape",
    description: "Close command palette",
    action: onClose,
    disabled: !isOpen
  })

  return {
    commands,
    formatShortcut
  }
}

// Global shortcuts for the ERP system
export function useGlobalShortcuts() {
  const shortcuts = createNavigationShortcuts(
    undefined, // onNew - context dependent
    undefined, // onSave - context dependent
    undefined, // onDelete - context dependent
    () => {
      // Focus search input
      const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i]') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
        searchInput.select()
      }
    },
    () => {
      // Refresh current page
      window.location.reload()
    }
  )

  const erpShortcuts = createERPShortcuts({
    onDashboard: () => window.location.href = "/",
    onInventoryView: () => window.location.href = "/inventory",
    onHelp: () => {
      // Show help modal or navigate to help page
      console.log("Help requested")
    }
  })

  const allShortcuts = [
    ...shortcuts,
    ...erpShortcuts.flatMap(group => group.shortcuts)
  ]

  useKeyboardShortcuts(allShortcuts)

  return {
    shortcuts: erpShortcuts,
    formatShortcut
  }
}
