// Input sanitization utilities to prevent XSS and injection attacks

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export function sanitizeHtml(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
}

/**
 * Sanitize text input by removing potentially dangerous characters
 */
export function sanitizeText(input: string): string {
  if (typeof input !== 'string') return ''
  
  // Remove null bytes and control characters except newlines and tabs
  return input.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
}

/**
 * Sanitize SQL input to prevent SQL injection
 * Note: This is a basic sanitizer. Always use parameterized queries!
 */
export function sanitizeSql(input: string): string {
  if (typeof input !== 'string') return ''
  
  // Remove or escape potentially dangerous SQL characters
  return input
    .replace(/'/g, "''") // Escape single quotes
    .replace(/;/g, '') // Remove semicolons
    .replace(/--/g, '') // Remove SQL comments
    .replace(/\/\*/g, '') // Remove block comment start
    .replace(/\*\//g, '') // Remove block comment end
}

/**
 * Sanitize file names to prevent directory traversal
 */
export function sanitizeFileName(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .replace(/[<>:"/\\|?*]/g, '') // Remove invalid filename characters
    .replace(/\.\./g, '') // Remove directory traversal
    .replace(/^\.+/, '') // Remove leading dots
    .trim()
    .substring(0, 255) // Limit length
}

/**
 * Sanitize URL to prevent malicious redirects
 */
export function sanitizeUrl(input: string): string {
  if (typeof input !== 'string') return ''
  
  try {
    const url = new URL(input)
    
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(url.protocol)) {
      return ''
    }
    
    return url.toString()
  } catch {
    return ''
  }
}

/**
 * Sanitize email address
 */
export function sanitizeEmail(input: string): string {
  if (typeof input !== 'string') return ''
  
  // Basic email sanitization
  return input
    .toLowerCase()
    .trim()
    .replace(/[<>]/g, '') // Remove angle brackets
}

/**
 * Sanitize phone number
 */
export function sanitizePhone(input: string): string {
  if (typeof input !== 'string') return ''
  
  // Keep only digits, spaces, hyphens, parentheses, and plus sign
  return input.replace(/[^0-9\s\-\(\)\+]/g, '').trim()
}

/**
 * Sanitize numeric input
 */
export function sanitizeNumber(input: string | number): number | null {
  if (typeof input === 'number') {
    return isFinite(input) ? input : null
  }
  
  if (typeof input !== 'string') return null
  
  const cleaned = input.replace(/[^0-9.\-]/g, '')
  const parsed = parseFloat(cleaned)
  
  return isFinite(parsed) ? parsed : null
}

/**
 * Sanitize integer input
 */
export function sanitizeInteger(input: string | number): number | null {
  const num = sanitizeNumber(input)
  return num !== null ? Math.floor(num) : null
}

/**
 * Sanitize object by applying sanitization to all string properties
 */
export function sanitizeObject<T extends Record<string, any>>(
  obj: T,
  sanitizer: (value: string) => string = sanitizeText
): T {
  const sanitized = { ...obj }
  
  for (const [key, value] of Object.entries(sanitized)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizer(value)
    } else if (Array.isArray(value)) {
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? sanitizer(item) : item
      )
    } else if (value && typeof value === 'object') {
      sanitized[key] = sanitizeObject(value, sanitizer)
    }
  }
  
  return sanitized
}

/**
 * Comprehensive sanitization for form data
 */
export function sanitizeFormData<T extends Record<string, any>>(data: T): T {
  const sanitized = { ...data }
  
  for (const [key, value] of Object.entries(sanitized)) {
    if (typeof value === 'string') {
      // Apply different sanitization based on field type
      if (key.includes('email')) {
        sanitized[key] = sanitizeEmail(value)
      } else if (key.includes('phone')) {
        sanitized[key] = sanitizePhone(value)
      } else if (key.includes('url') || key.includes('website')) {
        sanitized[key] = sanitizeUrl(value)
      } else if (key.includes('file') || key.includes('image')) {
        sanitized[key] = sanitizeFileName(value)
      } else {
        sanitized[key] = sanitizeText(value)
      }
    } else if (typeof value === 'number') {
      sanitized[key] = sanitizeNumber(value)
    } else if (Array.isArray(value)) {
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? sanitizeText(item) : item
      )
    } else if (value && typeof value === 'object') {
      sanitized[key] = sanitizeFormData(value)
    }
  }
  
  return sanitized
}

/**
 * Validate and sanitize search query
 */
export function sanitizeSearchQuery(query: string): string {
  if (typeof query !== 'string') return ''
  
  return query
    .trim()
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/['"]/g, '') // Remove quotes
    .substring(0, 100) // Limit length
}

/**
 * Sanitize sort parameters
 */
export function sanitizeSortParam(sort: string, allowedFields: string[]): string | null {
  if (typeof sort !== 'string') return null
  
  const cleaned = sort.toLowerCase().trim()
  
  // Check if it's in allowed fields
  if (allowedFields.includes(cleaned)) {
    return cleaned
  }
  
  return null
}

/**
 * Sanitize pagination parameters
 */
export function sanitizePagination(page?: string, limit?: string) {
  const pageNum = sanitizeInteger(page || '1')
  const limitNum = sanitizeInteger(limit || '20')
  
  return {
    page: Math.max(1, pageNum || 1),
    limit: Math.min(100, Math.max(1, limitNum || 20)) // Limit between 1-100
  }
}
