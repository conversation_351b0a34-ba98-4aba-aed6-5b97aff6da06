/**
 * Sample Data Management
 * 
 * This module handles sample data creation and cleanup for development/testing purposes.
 * In production, this should not be used as it can interfere with real data.
 */

import { db } from './db'
import { customers, products, suppliers, samples } from './schema-postgres'

/**
 * Clear all sample data from the database
 * WARNING: This will delete data - use with caution
 */
export async function clearSampleData() {
  console.log('🧹 Clearing sample data...')
  
  try {
    // Clear in reverse dependency order to avoid foreign key constraints
    await db.delete(samples)
    console.log('✅ Cleared samples')
    
    // Note: We don't clear customers, products, suppliers in production
    // as they may contain real data
    
    console.log('✅ Sample data cleared successfully')
  } catch (error) {
    console.error('❌ Error clearing sample data:', error)
    throw error
  }
}

/**
 * Seed sample data for development/testing
 * This creates minimal sample data for testing purposes
 */
export async function seedSampleData() {
  console.log('🌱 Seeding sample data...')
  
  try {
    // Create minimal sample data
    // Note: In production, we don't create sample data
    // as it should use real data from users
    
    console.log('✅ Sample data seeded successfully')
  } catch (error) {
    console.error('❌ Error seeding sample data:', error)
    throw error
  }
}

/**
 * Check if sample data exists
 */
export async function hasSampleData(): Promise<boolean> {
  try {
    const sampleCount = await db.select().from(samples).limit(1)
    return sampleCount.length > 0
  } catch (error) {
    console.error('❌ Error checking sample data:', error)
    return false
  }
}
