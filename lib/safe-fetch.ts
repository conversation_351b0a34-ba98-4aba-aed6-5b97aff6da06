export async function safeJson<T = unknown>(url: string, fallback: T, init?: RequestInit): Promise<T> {
  try {
    const res = await fetch(url, init)
    if (!res.ok) {
      // try to read text for easier debugging, but never throw to the caller
      const txt = await res.text().catch(() => "")
      console.warn("API error:", url, res.status, txt)
      return fallback
    }
    return (await res.json()) as T
  } catch (e) {
    console.warn("Network/parse error:", url, e)
    return fallback
  }
}
