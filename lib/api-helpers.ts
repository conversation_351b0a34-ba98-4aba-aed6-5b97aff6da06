import { NextResponse } from "next/server"
import {
  AppError,
  ErrorCode,
  formatErrorForUser,
  isAppError,
  ValidationError,
  BusinessLogicError,
  SystemError,
  createDatabaseError
} from "./errors"

export function jsonOk(data: unknown, init?: ResponseInit) {
  return Response.json(data, init)
}

export function jsonError(err: unknown, status = 500) {
  const message = err instanceof Error ? err.message : String(err)
  console.error(message)
  return Response.json({ error: message }, { status })
}

// Standardized error response function
export function createErrorResponse(error: unknown, defaultStatus = 500): NextResponse {
  // Handle validation errors
  if (error instanceof ValidationError) {
    return NextResponse.json({
      error: "Validation Error",
      message: error.message,
      code: error.code,
      field: error.field,
      details: error.details,
      timestamp: new Date().toISOString()
    }, { status: 400 })
  }

  // Handle business logic errors
  if (error instanceof BusinessLogicError) {
    return NextResponse.json({
      error: "Business Logic Error",
      message: error.message,
      code: error.code,
      details: error.details,
      timestamp: new Date().toISOString()
    }, { status: 422 })
  }

  // Handle system errors
  if (error instanceof SystemError) {
    const status = error.code === ErrorCode.AUTHENTICATION_ERROR ? 401 :
                   error.code === ErrorCode.AUTHORIZATION_ERROR ? 403 : 500

    return NextResponse.json({
      error: "System Error",
      message: error.message,
      code: error.code,
      details: error.details,
      timestamp: new Date().toISOString()
    }, { status })
  }

  // Handle AppError interface
  if (isAppError(error)) {
    const status = error.code === ErrorCode.VALIDATION_ERROR ? 400 :
                   error.code === ErrorCode.AUTHENTICATION_ERROR ? 401 :
                   error.code === ErrorCode.AUTHORIZATION_ERROR ? 403 :
                   error.code === ErrorCode.INSUFFICIENT_STOCK ? 422 : defaultStatus

    return NextResponse.json({
      error: "Application Error",
      message: error.message,
      code: error.code,
      field: error.field,
      details: error.details,
      timestamp: error.timestamp
    }, { status })
  }

  // Handle database errors
  if (error instanceof Error && error.message.includes('database')) {
    const dbError = createDatabaseError("operation", error.message)
    return NextResponse.json({
      error: "Database Error",
      message: "A database error occurred",
      code: dbError.code,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }

  // Handle generic errors
  const message = error instanceof Error ? error.message : String(error)
  console.error('Unhandled error:', error)

  return NextResponse.json({
    error: "Internal Server Error",
    message: process.env.NODE_ENV === 'development' ? message : "An unexpected error occurred",
    timestamp: new Date().toISOString()
  }, { status: defaultStatus })
}

// Success response helper
export function createSuccessResponse(data: unknown, message?: string, status = 200): NextResponse {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: new Date().toISOString()
  }, { status })
}

// Pagination response helper
export function createPaginatedResponse(
  data: unknown[],
  total: number,
  page: number,
  limit: number
): NextResponse {
  const totalPages = Math.ceil(total / limit)

  return NextResponse.json({
    success: true,
    data,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    },
    timestamp: new Date().toISOString()
  })
}
