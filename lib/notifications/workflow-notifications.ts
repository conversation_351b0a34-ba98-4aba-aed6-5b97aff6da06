import { User } from "@/lib/auth"

// Notification types
export interface Notification {
  id: string
  type: "info" | "warning" | "success" | "error"
  title: string
  message: string
  entityType: string
  entityId: string
  userId: string
  read: boolean
  createdAt: string
  actionUrl?: string
}

export interface NotificationTemplate {
  type: string
  title: string
  message: string
  recipients: (context: any) => Promise<string[]>
  actionUrl?: (context: any) => string
}

// In-memory notification storage (replace with database in production)
const notifications: Notification[] = []
const notificationTemplates: Map<string, NotificationTemplate> = new Map()

// Notification service
export class NotificationService {
  // Register notification templates
  registerTemplate(key: string, template: NotificationTemplate): void {
    notificationTemplates.set(key, template)
  }

  // Send notification
  async sendNotification(
    templateKey: string,
    context: any,
    notificationType: "info" | "warning" | "success" | "error" = "info"
  ): Promise<void> {
    const template = notificationTemplates.get(templateKey)
    if (!template) {
      console.warn(`Notification template "${templateKey}" not found`)
      return
    }

    try {
      // Get recipients
      const recipients = await template.recipients(context)
      
      // Create notifications for each recipient
      for (const userId of recipients) {
        const notification: Notification = {
          id: `notif_${Date.now()}_${Math.random().toString(36).substring(2)}`,
          type: notificationType,
          title: this.interpolateTemplate(template.title, context),
          message: this.interpolateTemplate(template.message, context),
          entityType: context.entityType || "unknown",
          entityId: context.entityId || "",
          userId,
          read: false,
          createdAt: new Date().toISOString(),
          actionUrl: template.actionUrl ? template.actionUrl(context) : undefined
        }

        notifications.push(notification)

        // In production, you would also:
        // 1. Save to database
        // 2. Send email notification
        // 3. Send push notification
        // 4. Send in-app notification via WebSocket
        
        console.log(`Notification sent to user ${userId}:`, notification)
      }
    } catch (error) {
      console.error(`Failed to send notification "${templateKey}":`, error)
    }
  }

  // Get notifications for a user
  getNotifications(userId: string, unreadOnly = false): Notification[] {
    return notifications
      .filter(n => n.userId === userId && (!unreadOnly || !n.read))
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }

  // Mark notification as read
  markAsRead(notificationId: string): void {
    const notification = notifications.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }

  // Mark all notifications as read for a user
  markAllAsRead(userId: string): void {
    notifications
      .filter(n => n.userId === userId)
      .forEach(n => n.read = true)
  }

  // Get unread count
  getUnreadCount(userId: string): number {
    return notifications.filter(n => n.userId === userId && !n.read).length
  }

  // Helper method to interpolate template variables
  private interpolateTemplate(template: string, context: any): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return context[key] || match
    })
  }
}

// Create singleton instance
export const notificationService = new NotificationService()

// Register workflow notification templates
notificationService.registerTemplate("contract_submitted_for_review", {
  type: "contract_workflow",
  title: "Contract Submitted for Review",
  message: "Contract {{contractNumber}} has been submitted for review by {{submittedBy}}",
  recipients: async (context) => {
    // Send to all managers and admins
    return ["manager-1", "admin-1"] // Mock user IDs
  },
  actionUrl: (context) => `/contracts/${context.contractId}`
})

notificationService.registerTemplate("contract_approved", {
  type: "contract_workflow",
  title: "Contract Approved",
  message: "Contract {{contractNumber}} has been approved by {{approvedBy}}",
  recipients: async (context) => {
    // Send to contract creator and sales team
    return [context.createdBy, "sales-team-1"]
  },
  actionUrl: (context) => `/contracts/${context.contractId}`
})

notificationService.registerTemplate("contract_rejected", {
  type: "contract_workflow",
  title: "Contract Rejected",
  message: "Contract {{contractNumber}} has been rejected. Reason: {{reason}}",
  recipients: async (context) => {
    // Send to contract creator
    return [context.createdBy]
  },
  actionUrl: (context) => `/contracts/${context.contractId}`
})

notificationService.registerTemplate("work_order_created", {
  type: "production_workflow",
  title: "New Work Order Created",
  message: "Work order {{workOrderNumber}} has been created for contract {{contractNumber}}",
  recipients: async (context) => {
    // Send to production managers and operators
    return ["production-manager-1", "operator-1"]
  },
  actionUrl: (context) => `/production/work-orders/${context.workOrderId}`
})

notificationService.registerTemplate("work_order_completed", {
  type: "production_workflow",
  title: "Work Order Completed",
  message: "Work order {{workOrderNumber}} has been completed. {{qty}} units of {{productName}} are ready.",
  recipients: async (context) => {
    // Send to sales team and inventory managers
    return ["sales-team-1", "inventory-manager-1"]
  },
  actionUrl: (context) => `/production/work-orders/${context.workOrderId}`
})

notificationService.registerTemplate("low_stock_alert", {
  type: "inventory_alert",
  title: "Low Stock Alert",
  message: "Product {{productName}} is running low in {{location}}. Current stock: {{currentStock}} {{unit}}",
  recipients: async (context) => {
    // Send to inventory managers and purchasing team
    return ["inventory-manager-1", "purchasing-team-1"]
  },
  actionUrl: (context) => `/inventory/products/${context.productId}`
})

notificationService.registerTemplate("declaration_submitted", {
  type: "export_workflow",
  title: "Export Declaration Submitted",
  message: "Export declaration {{declarationNumber}} has been submitted to customs",
  recipients: async (context) => {
    // Send to export team and management
    return ["export-team-1", "manager-1"]
  },
  actionUrl: (context) => `/export/declarations/${context.declarationId}`
})

notificationService.registerTemplate("declaration_cleared", {
  type: "export_workflow",
  title: "Export Declaration Cleared",
  message: "Export declaration {{declarationNumber}} has been cleared by customs",
  recipients: async (context) => {
    // Send to sales team and logistics
    return ["sales-team-1", "logistics-team-1"]
  },
  actionUrl: (context) => `/export/declarations/${context.declarationId}`
})

// Workflow notification helpers
export class WorkflowNotificationService {
  // Contract workflow notifications
  async notifyContractSubmittedForReview(
    contractId: string,
    contractNumber: string,
    submittedBy: User
  ): Promise<void> {
    await notificationService.sendNotification("contract_submitted_for_review", {
      entityType: "contract",
      entityId: contractId,
      contractId,
      contractNumber,
      submittedBy: submittedBy.name
    }, "info")
  }

  async notifyContractApproved(
    contractId: string,
    contractNumber: string,
    approvedBy: User,
    createdBy: string
  ): Promise<void> {
    await notificationService.sendNotification("contract_approved", {
      entityType: "contract",
      entityId: contractId,
      contractId,
      contractNumber,
      approvedBy: approvedBy.name,
      createdBy
    }, "success")
  }

  async notifyContractRejected(
    contractId: string,
    contractNumber: string,
    rejectedBy: User,
    createdBy: string,
    reason?: string
  ): Promise<void> {
    await notificationService.sendNotification("contract_rejected", {
      entityType: "contract",
      entityId: contractId,
      contractId,
      contractNumber,
      rejectedBy: rejectedBy.name,
      createdBy,
      reason: reason || "No reason provided"
    }, "warning")
  }

  // Production workflow notifications
  async notifyWorkOrderCreated(
    workOrderId: string,
    workOrderNumber: string,
    contractNumber: string
  ): Promise<void> {
    await notificationService.sendNotification("work_order_created", {
      entityType: "work_order",
      entityId: workOrderId,
      workOrderId,
      workOrderNumber,
      contractNumber
    }, "info")
  }

  async notifyWorkOrderCompleted(
    workOrderId: string,
    workOrderNumber: string,
    productName: string,
    qty: number
  ): Promise<void> {
    await notificationService.sendNotification("work_order_completed", {
      entityType: "work_order",
      entityId: workOrderId,
      workOrderId,
      workOrderNumber,
      productName,
      qty
    }, "success")
  }

  // Inventory notifications
  async notifyLowStock(
    productId: string,
    productName: string,
    location: string,
    currentStock: number,
    unit: string
  ): Promise<void> {
    await notificationService.sendNotification("low_stock_alert", {
      entityType: "product",
      entityId: productId,
      productId,
      productName,
      location,
      currentStock,
      unit
    }, "warning")
  }

  // Export workflow notifications
  async notifyDeclarationSubmitted(
    declarationId: string,
    declarationNumber: string
  ): Promise<void> {
    await notificationService.sendNotification("declaration_submitted", {
      entityType: "declaration",
      entityId: declarationId,
      declarationId,
      declarationNumber
    }, "info")
  }

  async notifyDeclarationCleared(
    declarationId: string,
    declarationNumber: string
  ): Promise<void> {
    await notificationService.sendNotification("declaration_cleared", {
      entityType: "declaration",
      entityId: declarationId,
      declarationId,
      declarationNumber
    }, "success")
  }
}

// Export singleton instance
export const workflowNotificationService = new WorkflowNotificationService()
