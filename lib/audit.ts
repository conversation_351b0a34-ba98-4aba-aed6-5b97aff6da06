import { User } from "./auth"

// Audit log types
export enum AuditAction {
  CREATE = "CREATE",
  UPDATE = "UPDATE", 
  DELETE = "DELETE",
  VIEW = "VIEW",
  LOGIN = "LOGIN",
  LOGOUT = "LOGOUT",
  EXPORT = "EXPORT",
  IMPORT = "IMPORT",
  APPROVE = "APPROVE",
  REJECT = "REJECT"
}

export enum AuditResource {
  CUSTOMER = "CUSTOMER",
  SUPPLIER = "SUPPLIER", 
  PRODUCT = "PRODUCT",
  SAMPLE = "SAMPLE",
  SALES_CONTRACT = "SALES_CONTRACT",
  PURCHASE_CONTRACT = "PURCHASE_CONTRACT",
  WORK_ORDER = "WORK_ORDER",
  STOCK_LOT = "STOCK_LOT",
  STOCK_TRANSACTION = "STOCK_TRANSACTION",
  DECLARATION = "DECLARATION",
  AR_INVOICE = "AR_INVOICE",
  AP_INVOICE = "AP_INVOICE",
  USER = "USER",
  SYSTEM = "SYSTEM"
}

export interface AuditLog {
  id: string
  timestamp: string
  userId: string
  userEmail: string
  userRole: string
  action: AuditAction
  resource: AuditResource
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  success: boolean
  errorMessage?: string
}

// In-memory audit log storage (replace with database in production)
const auditLogs: AuditLog[] = []

/**
 * Create an audit log entry
 */
export function createAuditLog(
  user: User | null,
  action: AuditAction,
  resource: AuditResource,
  resourceId?: string,
  details?: Record<string, any>,
  success = true,
  errorMessage?: string,
  ipAddress?: string,
  userAgent?: string
): AuditLog {
  const log: AuditLog = {
    id: `audit_${Date.now()}_${Math.random().toString(36).substring(2)}`,
    timestamp: new Date().toISOString(),
    userId: user?.id || 'anonymous',
    userEmail: user?.email || 'anonymous',
    userRole: user?.role || 'anonymous',
    action,
    resource,
    resourceId,
    details,
    success,
    errorMessage,
    ipAddress,
    userAgent
  }
  
  // Store in memory (in production, save to database)
  auditLogs.push(log)
  
  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Audit Log:', log)
  }
  
  return log
}

/**
 * Log successful operations
 */
export function logSuccess(
  user: User | null,
  action: AuditAction,
  resource: AuditResource,
  resourceId?: string,
  details?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): void {
  createAuditLog(user, action, resource, resourceId, details, true, undefined, ipAddress, userAgent)
}

/**
 * Log failed operations
 */
export function logFailure(
  user: User | null,
  action: AuditAction,
  resource: AuditResource,
  errorMessage: string,
  resourceId?: string,
  details?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): void {
  createAuditLog(user, action, resource, resourceId, details, false, errorMessage, ipAddress, userAgent)
}

/**
 * Log authentication events
 */
export function logAuth(
  action: AuditAction.LOGIN | AuditAction.LOGOUT,
  user: User | null,
  success: boolean,
  errorMessage?: string,
  ipAddress?: string,
  userAgent?: string
): void {
  createAuditLog(
    user,
    action,
    AuditResource.USER,
    user?.id,
    { email: user?.email },
    success,
    errorMessage,
    ipAddress,
    userAgent
  )
}

/**
 * Log data access events
 */
export function logDataAccess(
  user: User | null,
  resource: AuditResource,
  resourceId?: string,
  details?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): void {
  logSuccess(user, AuditAction.VIEW, resource, resourceId, details, ipAddress, userAgent)
}

/**
 * Log data modification events
 */
export function logDataModification(
  user: User | null,
  action: AuditAction.CREATE | AuditAction.UPDATE | AuditAction.DELETE,
  resource: AuditResource,
  resourceId?: string,
  oldData?: Record<string, any>,
  newData?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): void {
  const details: Record<string, any> = {}
  
  if (oldData) details.oldData = oldData
  if (newData) details.newData = newData
  
  logSuccess(user, action, resource, resourceId, details, ipAddress, userAgent)
}

/**
 * Log financial operations
 */
export function logFinancialOperation(
  user: User | null,
  action: AuditAction,
  resource: AuditResource.AR_INVOICE | AuditResource.AP_INVOICE,
  resourceId: string,
  amount: number,
  currency: string,
  ipAddress?: string,
  userAgent?: string
): void {
  logSuccess(
    user,
    action,
    resource,
    resourceId,
    { amount, currency },
    ipAddress,
    userAgent
  )
}

/**
 * Log contract operations
 */
export function logContractOperation(
  user: User | null,
  action: AuditAction,
  resource: AuditResource.SALES_CONTRACT | AuditResource.PURCHASE_CONTRACT,
  resourceId: string,
  contractNumber: string,
  status?: string,
  ipAddress?: string,
  userAgent?: string
): void {
  logSuccess(
    user,
    action,
    resource,
    resourceId,
    { contractNumber, status },
    ipAddress,
    userAgent
  )
}

/**
 * Log inventory operations
 */
export function logInventoryOperation(
  user: User | null,
  action: AuditAction,
  resource: AuditResource.STOCK_LOT | AuditResource.STOCK_TRANSACTION,
  resourceId: string,
  productId: string,
  quantity: number,
  location: string,
  ipAddress?: string,
  userAgent?: string
): void {
  logSuccess(
    user,
    action,
    resource,
    resourceId,
    { productId, quantity, location },
    ipAddress,
    userAgent
  )
}

/**
 * Get audit logs (for admin interface)
 */
export function getAuditLogs(
  filters?: {
    userId?: string
    action?: AuditAction
    resource?: AuditResource
    dateFrom?: string
    dateTo?: string
    success?: boolean
  },
  limit = 100,
  offset = 0
): AuditLog[] {
  let filtered = [...auditLogs]
  
  if (filters) {
    if (filters.userId) {
      filtered = filtered.filter(log => log.userId === filters.userId)
    }
    if (filters.action) {
      filtered = filtered.filter(log => log.action === filters.action)
    }
    if (filters.resource) {
      filtered = filtered.filter(log => log.resource === filters.resource)
    }
    if (filters.dateFrom) {
      filtered = filtered.filter(log => log.timestamp >= filters.dateFrom!)
    }
    if (filters.dateTo) {
      filtered = filtered.filter(log => log.timestamp <= filters.dateTo!)
    }
    if (filters.success !== undefined) {
      filtered = filtered.filter(log => log.success === filters.success)
    }
  }
  
  // Sort by timestamp (newest first)
  filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  
  // Apply pagination
  return filtered.slice(offset, offset + limit)
}

/**
 * Higher-order function to add audit logging to API handlers
 */
export function withAuditLog<T extends any[]>(
  action: AuditAction,
  resource: AuditResource,
  handler: (user: User | null, ...args: T) => Promise<Response>,
  getResourceId?: (...args: T) => string | undefined
) {
  return async (user: User | null, request: Request, ...args: T): Promise<Response> => {
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const resourceId = getResourceId ? getResourceId(...args) : undefined
    
    try {
      const response = await handler(user, ...args)
      
      if (response.ok) {
        logSuccess(user, action, resource, resourceId, undefined, ipAddress, userAgent)
      } else {
        logFailure(user, action, resource, `HTTP ${response.status}`, resourceId, undefined, ipAddress, userAgent)
      }
      
      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logFailure(user, action, resource, errorMessage, resourceId, undefined, ipAddress, userAgent)
      throw error
    }
  }
}
