import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { jsonError } from "./api-helpers"

export type ValidationResult<T> = {
  success: true
  data: T
} | {
  success: false
  error: {
    message: string
    issues: z.ZodIssue[]
  }
}

/**
 * Validates request body against a Zod schema
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<ValidationResult<T>> {
  try {
    const body = await request.json()
    const result = schema.safeParse(body)
    
    if (!result.success) {
      return {
        success: false,
        error: {
          message: "Validation failed",
          issues: result.error.issues
        }
      }
    }
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    return {
      success: false,
      error: {
        message: "Invalid JSON in request body",
        issues: []
      }
    }
  }
}

/**
 * Validates query parameters against a Zod schema
 */
export function validateQueryParams<T>(
  request: NextRequest,
  schema: z.Z<PERSON>chema<T>
): ValidationResult<T> {
  try {
    const { searchParams } = new URL(request.url)
    const params = Object.fromEntries(searchParams.entries())
    
    const result = schema.safeParse(params)
    
    if (!result.success) {
      return {
        success: false,
        error: {
          message: "Invalid query parameters",
          issues: result.error.issues
        }
      }
    }
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    return {
      success: false,
      error: {
        message: "Failed to parse query parameters",
        issues: []
      }
    }
  }
}

/**
 * Higher-order function to create validated API handlers
 */
export function withValidation<T>(
  schema: z.ZodSchema<T>,
  handler: (data: T, request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const validation = await validateRequestBody(request, schema)
    
    if (!validation.success) {
      return jsonError({
        message: validation.error.message,
        issues: validation.error.issues
      }, 400)
    }
    
    return handler(validation.data, request)
  }
}

/**
 * Formats validation errors for user-friendly display
 */
export function formatValidationErrors(issues: z.ZodIssue[]): Record<string, string[]> {
  const errors: Record<string, string[]> = {}
  
  for (const issue of issues) {
    const path = issue.path.join('.')
    if (!errors[path]) {
      errors[path] = []
    }
    errors[path].push(issue.message)
  }
  
  return errors
}

/**
 * Creates a standardized validation error response
 */
export function createValidationErrorResponse(issues: z.ZodIssue[]) {
  return NextResponse.json({
    error: "Validation failed",
    message: "Please check your input and try again",
    details: formatValidationErrors(issues)
  }, { status: 400 })
}

/**
 * Middleware for validating path parameters
 */
export function validatePathParams<T>(
  params: Record<string, string | string[]>,
  schema: z.ZodSchema<T>
): ValidationResult<T> {
  const result = schema.safeParse(params)
  
  if (!result.success) {
    return {
      success: false,
      error: {
        message: "Invalid path parameters",
        issues: result.error.issues
      }
    }
  }
  
  return {
    success: true,
    data: result.data
  }
}

// Common validation schemas for path parameters
export const idParamSchema = z.object({
  id: z.string().min(1, "ID is required")
})

// Common query parameter schemas
export const paginationSchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().min(1)).default("1"),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().min(1).max(100)).default("20"),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).default("desc")
})

export const statusFilterSchema = z.object({
  status: z.string().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional()
})

/**
 * Generic API route wrapper with validation
 */
export function createValidatedRoute<T>(
  schema: z.ZodSchema<T>,
  handler: (data: T, request: NextRequest) => Promise<Response>
) {
  return async (request: NextRequest): Promise<Response> => {
    try {
      const validation = await validateRequestBody(request, schema)

      if (!validation.success) {
        return createValidationErrorResponse(validation.error.issues)
      }

      return await handler(validation.data, request)
    } catch (error) {
      console.error("API route error:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }
}
