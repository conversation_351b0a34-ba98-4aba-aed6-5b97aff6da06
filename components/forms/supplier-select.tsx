"use client"

import * as React from "react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Plus, Building2 } from "lucide-react"

interface Supplier {
  id: string
  name: string
  contact_name?: string
  contact_email?: string
  contact_phone?: string
  address?: string
}

interface SupplierSelectProps {
  suppliers: Supplier[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  onSupplierAdded?: (supplier: Supplier) => void
}

// Quick add supplier form schema
const quickAddSchema = z.object({
  name: z.string().min(1, "Supplier name is required"),
  contact_name: z.string().optional(),
  contact_email: z.string().email("Valid email is required").optional().or(z.literal("")),
  contact_phone: z.string().optional(),
  address: z.string().optional(),
})

export function SupplierSelect({
  suppliers,
  value,
  onValueChange,
  placeholder = "Select supplier...",
  className,
  disabled = false,
  onSupplierAdded,
}: SupplierSelectProps) {
  const [showAddDialog, setShowAddDialog] = React.useState(false)
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  // Convert suppliers to searchable options
  const supplierOptions: SearchableSelectOption[] = React.useMemo(() => {
    return suppliers.map((supplier) => ({
      value: supplier.id,
      label: supplier.name,
      subtitle: supplier.contact_email || supplier.contact_name,
      description: supplier.address ? `📍 ${supplier.address}` : undefined,
    }))
  }, [suppliers])

  // Quick add form
  const form = useForm<z.infer<typeof quickAddSchema>>({
    resolver: zodResolver(quickAddSchema),
    defaultValues: {
      name: "",
      contact_name: "",
      contact_email: "",
      contact_phone: "",
      address: "",
    },
  })

  const handleAddNew = () => {
    setShowAddDialog(true)
    form.reset()
  }

  const onSubmit = async (values: z.infer<typeof quickAddSchema>) => {
    setIsSubmitting(true)
    try {
      const response = await fetch("/api/suppliers", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      })

      if (response.ok) {
        const result = await response.json()
        const newSupplier = result.data
        
        toast.success("Supplier added successfully!")
        setShowAddDialog(false)
        
        // Notify parent component about the new supplier
        onSupplierAdded?.(newSupplier)
        
        // Auto-select the newly created supplier
        onValueChange?.(newSupplier.id)
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || "Failed to add supplier")
      }
    } catch (error) {
      console.error("Error adding supplier:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <SearchableSelect
        options={supplierOptions}
        value={value}
        onValueChange={onValueChange}
        placeholder={placeholder}
        searchPlaceholder="Search suppliers..."
        emptyMessage="No suppliers found. Try adding a new one!"
        className={className}
        disabled={disabled}
        showAddNew={true}
        onAddNew={handleAddNew}
        addNewLabel="Add New Supplier"
        allowClear={true}
      />

      {/* Quick Add Supplier Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Building2 className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <DialogTitle>Add New Supplier</DialogTitle>
                <DialogDescription>
                  Quickly add a new supplier to your database
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter supplier name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contact_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Contact person name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contact_email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input 
                        type="email" 
                        placeholder="<EMAIL>" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contact_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input placeholder="Supplier address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddDialog(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Supplier
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}
