"use client"

import * as React from "react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Plus, Building2 } from "lucide-react"

interface Customer {
  id: string
  name: string
  contact_email?: string
  address?: string
  phone?: string
}

interface CustomerSelectProps {
  customers: Customer[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  onCustomerAdded?: (customer: Customer) => void
}

// Quick add customer form schema
const quickAddSchema = z.object({
  name: z.string().min(1, "Customer name is required"),
  contact_email: z.string().email("Valid email is required").optional().or(z.literal("")),
  phone: z.string().optional(),
  address: z.string().optional(),
})

export function CustomerSelect({
  customers,
  value,
  onValueChange,
  placeholder = "Select customer...",
  className,
  disabled = false,
  onCustomerAdded,
}: CustomerSelectProps) {
  const [showAddDialog, setShowAddDialog] = React.useState(false)
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  // Convert customers to searchable options
  const customerOptions: SearchableSelectOption[] = React.useMemo(() => {
    return customers.map((customer) => ({
      value: customer.id,
      label: customer.name,
      subtitle: customer.contact_email,
      description: customer.address ? `📍 ${customer.address}` : undefined,
    }))
  }, [customers])

  // Quick add form
  const form = useForm<z.infer<typeof quickAddSchema>>({
    resolver: zodResolver(quickAddSchema),
    defaultValues: {
      name: "",
      contact_email: "",
      phone: "",
      address: "",
    },
  })

  const handleAddNew = () => {
    setShowAddDialog(true)
    form.reset()
  }

  const onSubmit = async (values: z.infer<typeof quickAddSchema>) => {
    setIsSubmitting(true)
    try {
      const response = await fetch("/api/customers", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      })

      if (response.ok) {
        const result = await response.json()
        const newCustomer = result.data
        
        toast.success("Customer added successfully!")
        setShowAddDialog(false)
        
        // Notify parent component about the new customer
        onCustomerAdded?.(newCustomer)
        
        // Auto-select the newly created customer
        onValueChange?.(newCustomer.id)
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || "Failed to add customer")
      }
    } catch (error) {
      console.error("Error adding customer:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <SearchableSelect
        options={customerOptions}
        value={value}
        onValueChange={onValueChange}
        placeholder={placeholder}
        searchPlaceholder="Search customers..."
        emptyMessage="No customers found. Try adding a new one!"
        className={className}
        disabled={disabled}
        showAddNew={true}
        onAddNew={handleAddNew}
        addNewLabel="Add New Customer"
        allowClear={true}
      />

      {/* Quick Add Customer Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Building2 className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <DialogTitle>Add New Customer</DialogTitle>
                <DialogDescription>
                  Quickly add a new customer to your database
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter customer name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contact_email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input 
                        type="email" 
                        placeholder="<EMAIL>" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input placeholder="Customer address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddDialog(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Customer
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}
