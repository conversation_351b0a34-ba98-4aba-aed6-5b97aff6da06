"use client"

import * as React from "react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Eye, FileText, Calendar, User } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ContractTemplate {
  id: string
  name: string
  type: 'sales' | 'purchase'
  description?: string
  content: string
  created_at?: string
  updated_at?: string
}

interface TemplateSelectProps {
  templates: ContractTemplate[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  templateType?: 'sales' | 'purchase'
}

export function TemplateSelect({
  templates,
  value,
  onValueChange,
  placeholder = "Select template...",
  className,
  disabled = false,
  templateType = 'sales',
}: TemplateSelectProps) {
  const [showPreview, setShowPreview] = React.useState(false)
  const [previewTemplate, setPreviewTemplate] = React.useState<ContractTemplate | null>(null)

  // Filter templates by type
  const filteredTemplates = React.useMemo(() => {
    return templates.filter(template => template.type === templateType)
  }, [templates, templateType])

  // Convert templates to searchable options
  const templateOptions: SearchableSelectOption[] = React.useMemo(() => {
    const options: SearchableSelectOption[] = [
      {
        value: "none",
        label: "No template",
        description: "Create contract without using a template",
      }
    ]

    const templateOpts = filteredTemplates.map((template) => ({
      value: template.id,
      label: template.name,
      subtitle: template.description || `${templateType} contract template`,
      description: template.updated_at ? `📅 Updated ${new Date(template.updated_at).toLocaleDateString()}` : undefined,
    }))

    return [...options, ...templateOpts]
  }, [filteredTemplates, templateType])

  const handlePreview = (templateId: string) => {
    const template = filteredTemplates.find(t => t.id === templateId)
    if (template) {
      setPreviewTemplate(template)
      setShowPreview(true)
    }
  }

  const selectedTemplate = filteredTemplates.find(t => t.id === value)

  return (
    <>
      <div className="space-y-2">
        <SearchableSelect
          options={templateOptions}
          value={value}
          onValueChange={onValueChange}
          placeholder={placeholder}
          searchPlaceholder="Search templates..."
          emptyMessage="No templates found."
          className={className}
          disabled={disabled}
          allowClear={true}
        />
        
        {/* Selected Template Info */}
        {selectedTemplate && (
          <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <div className="font-medium text-blue-900">{selectedTemplate.name}</div>
                <div className="text-sm text-blue-600">
                  {selectedTemplate.description || `${templateType} contract template`}
                </div>
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => handlePreview(selectedTemplate.id)}
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
            >
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
          </div>
        )}
      </div>

      {/* Template Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <DialogTitle>{previewTemplate?.name}</DialogTitle>
                <DialogDescription>
                  {previewTemplate?.description || `${templateType} contract template preview`}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {previewTemplate && (
            <div className="space-y-4">
              {/* Template Metadata */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="text-xs">
                  <FileText className="h-3 w-3 mr-1" />
                  {previewTemplate.type} Template
                </Badge>
                {previewTemplate.updated_at && (
                  <Badge variant="outline" className="text-xs">
                    <Calendar className="h-3 w-3 mr-1" />
                    Updated {new Date(previewTemplate.updated_at).toLocaleDateString()}
                  </Badge>
                )}
              </div>

              {/* Template Content Preview */}
              <div className="border rounded-lg">
                <div className="bg-gray-50 px-4 py-2 border-b">
                  <h4 className="font-medium text-gray-900">Template Content</h4>
                  <p className="text-sm text-gray-600">Preview of the contract template</p>
                </div>
                <ScrollArea className="h-96">
                  <div className="p-6 prose prose-sm max-w-none">
                    <div 
                      className="whitespace-pre-wrap text-sm leading-relaxed"
                      dangerouslySetInnerHTML={{ 
                        __html: previewTemplate.content
                          .replace(/\n/g, '<br>')
                          .replace(/\{\{([^}]+)\}\}/g, '<span class="bg-yellow-100 px-1 py-0.5 rounded text-xs font-mono border">{{$1}}</span>')
                      }}
                    />
                  </div>
                </ScrollArea>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  Variables like <code className="bg-gray-100 px-1 py-0.5 rounded text-xs">{"{{company_name}}"}</code> will be replaced with actual data
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowPreview(false)}
                  >
                    Close
                  </Button>
                  <Button
                    onClick={() => {
                      onValueChange?.(previewTemplate.id)
                      setShowPreview(false)
                    }}
                  >
                    Use This Template
                  </Button>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
