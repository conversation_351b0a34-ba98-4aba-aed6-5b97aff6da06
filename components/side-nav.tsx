"use client"

import type React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Boxes,
  Handshake,
  Factory,
  Package,
  Ship,
  Coins,
  BookText,
  CheckCircle,
  Truck,
  BarChart3,
  FileText,
  Palette,
  TestTube,
  Shield,
  Users,
  Building,
  ShoppingCart,
  ShoppingBag,
  Settings,
} from "lucide-react"
import { useI18n } from "./i18n-provider"

type NavItem = {
  href: string
  key: string
  icon: React.ComponentType<{ className?: string }>
}

type NavGroup = {
  titleKey: string
  items: NavItem[]
}

export function SideNav({ onNavigate }: { onNavigate?: () => void }) {
  const pathname = usePathname()
  const { t } = useI18n()

  const navGroups: NavGroup[] = [
    {
      titleKey: "nav.group.overview",
      items: [{ href: "/", key: "nav.item.dashboard", icon: LayoutDashboard }],
    },
    {
      titleKey: "nav.group.master-data",
      items: [
        { href: "/customers", key: "nav.item.customers", icon: Users },
        { href: "/suppliers", key: "nav.item.suppliers", icon: Building },
        { href: "/products", key: "nav.item.products", icon: Package },
        { href: "/samples", key: "nav.item.samples", icon: TestTube },
      ],
    },
    {
      titleKey: "nav.group.sales-purchasing",
      items: [
        { href: "/sales-contracts", key: "nav.item.sales-contracts", icon: ShoppingCart },
        { href: "/purchase-contracts", key: "nav.item.purchase-contracts", icon: ShoppingBag },
        { href: "/contract-templates", key: "nav.item.contract-templates", icon: BookText },
      ],
    },
    {
      titleKey: "nav.group.production",
      items: [
        { href: "/production", key: "nav.item.work-orders", icon: Factory },
        { href: "/quality", key: "nav.item.quality-control", icon: CheckCircle },
      ],
    },
    {
      titleKey: "nav.group.inventory-logistics",
      items: [
        { href: "/inventory", key: "nav.item.inventory", icon: Boxes },
        { href: "/shipping", key: "nav.item.shipping", icon: Truck },
      ],
    },
    {
      titleKey: "nav.group.export-trade",
      items: [
        { href: "/export", key: "nav.item.export-declarations", icon: Ship },
        { href: "/trade-compliance", key: "nav.item.trade-compliance", icon: Shield },
        { href: "/plan-images", key: "nav.item.documentation", icon: FileText },
      ],
    },
    {
      titleKey: "nav.group.finance-reporting",
      items: [
        { href: "/finance", key: "nav.item.accounting", icon: Coins },
        { href: "/reports", key: "nav.item.reports", icon: BarChart3 },
      ],
    },
    {
      titleKey: "nav.group.settings",
      items: [
        { href: "/company-profile", key: "nav.item.company-profile", icon: Settings },
      ],
    },
  ]

  return (
    <aside className="h-full w-full bg-sidebar border-r">
      <div className="px-3 py-4">
        {navGroups.map((group) => (
          <div key={group.titleKey} className="mb-6">
            <div className="px-3 mb-3 text-xs font-semibold uppercase tracking-wider text-sidebar-foreground/70">
              {t(group.titleKey)}
            </div>
            <nav className="space-y-1">
              {group.items.map((item) => {
                const active = pathname === item.href
                const Icon = item.icon
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    onClick={onNavigate}
                    className={cn(
                      "flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200",
                      active
                        ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-sm"
                        : "text-sidebar-foreground/80 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                    )}
                    aria-current={active ? "page" : undefined}
                  >
                    <Icon className="h-4 w-4 flex-shrink-0" />
                    <span className="flex-1 truncate">{t(item.key)}</span>
                  </Link>
                )
              })}
            </nav>
          </div>
        ))}
      </div>
    </aside>
  )
}
