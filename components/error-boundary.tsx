"use client"

import <PERSON><PERSON>, { <PERSON>mpo<PERSON>, ErrorInfo, ReactNode } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatErrorForUser } from "@/lib/errors"

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo })
    
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo)
    }
    
    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo)
    
    // In production, you might want to send this to an error reporting service
    // Example: Sentry.captureException(error, { contexts: { react: errorInfo } })
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card className="max-w-lg mx-auto mt-8">
          <CardHeader>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <CardTitle className="text-red-700">Something went wrong</CardTitle>
            </div>
            <CardDescription>
              {formatErrorForUser(this.state.error)}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={this.handleRetry} className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                  Technical Details (Development Only)
                </summary>
                <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

// Specialized error boundaries for different parts of the app

interface FormErrorBoundaryProps {
  children: ReactNode
  onError?: (error: Error) => void
}

export function FormErrorBoundary({ children, onError }: FormErrorBoundaryProps) {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        onError?.(error)
      }}
      fallback={
        <div className="rounded-md bg-red-50 border border-red-200 p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Form Error
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>There was an error with the form. Please refresh the page and try again.</p>
              </div>
            </div>
          </div>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  )
}

interface TableErrorBoundaryProps {
  children: ReactNode
  onRetry?: () => void
}

export function TableErrorBoundary({ children, onRetry }: TableErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={
        <div className="rounded-md bg-red-50 border border-red-200 p-4 text-center">
          <AlertTriangle className="h-8 w-8 text-red-400 mx-auto mb-2" />
          <h3 className="text-sm font-medium text-red-800 mb-2">
            Failed to load data
          </h3>
          <p className="text-sm text-red-700 mb-4">
            There was an error loading the table data.
          </p>
          {onRetry && (
            <Button onClick={onRetry} size="sm" variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  )
}

interface PageErrorBoundaryProps {
  children: ReactNode
}

export function PageErrorBoundary({ children }: PageErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <Card className="max-w-md">
            <CardHeader className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <CardTitle className="text-red-700">Page Error</CardTitle>
              <CardDescription>
                This page encountered an error and couldn't be displayed.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Button onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Page
              </Button>
            </CardContent>
          </Card>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  )
}

// Hook for handling async errors in components
export function useErrorHandler() {
  return (error: Error) => {
    // In a real app, you might want to send this to an error reporting service
    console.error('Async error:', error)
    
    // You could also show a toast notification here
    // toast.error(formatErrorForUser(error))
  }
}
