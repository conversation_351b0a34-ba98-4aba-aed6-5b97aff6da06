"use client"

import React from 'react'
import { Document, Page, Text, View, StyleSheet, pdf } from '@react-pdf/renderer'

// Professional PDF styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Helvetica',
    fontSize: 12,
    lineHeight: 1.6,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
    paddingBottom: 20,
    borderBottomWidth: 2,
    borderBottomColor: '#2563eb',
  },
  logo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoBox: {
    width: 50,
    height: 50,
    backgroundColor: '#2563eb',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  companyInfo: {
    flexDirection: 'column',
  },
  companyName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
  },
  companySubtitle: {
    fontSize: 12,
    color: '#4b5563',
  },
  documentInfo: {
    textAlign: 'right',
    fontSize: 12,
    color: '#4b5563',
  },
  contractTitle: {
    textAlign: 'center',
    marginBottom: 30,
    paddingBottom: 20,
    borderBottomWidth: 2,
    borderBottomColor: '#1f2937',
  },
  contractTitleText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    textTransform: 'uppercase',
    letterSpacing: 2,
    marginBottom: 15,
  },
  titleUnderline: {
    width: 80,
    height: 3,
    backgroundColor: '#2563eb',
    alignSelf: 'center',
  },
  sectionDivider: {
    borderTopWidth: 2,
    borderTopColor: '#1f2937',
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    backgroundColor: '#f9fafb',
    padding: 12,
    marginTop: 25,
    marginBottom: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#2563eb',
  },
  subsectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1e40af',
    marginTop: 20,
    marginBottom: 10,
    paddingBottom: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#d1d5db',
  },
  paragraph: {
    marginBottom: 8,
    textAlign: 'justify',
    color: '#000000',
  },
  signatureSection: {
    marginTop: 40,
    flexDirection: 'row',
    alignItems: 'center',
  },
  signatureLine: {
    width: 200,
    borderBottomWidth: 2,
    borderBottomColor: '#1f2937',
    marginRight: 20,
  },
  dateText: {
    fontSize: 12,
    color: '#4b5563',
  },
  footer: {
    marginTop: 50,
    paddingTop: 25,
    borderTopWidth: 2,
    borderTopColor: '#1f2937',
  },
  footerContent: {
    backgroundColor: '#f9fafb',
    padding: 20,
    borderRadius: 8,
    textAlign: 'center',
  },
  footerTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 8,
  },
  footerText: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 4,
  },
})

interface PDFContractDocumentProps {
  contractNumber: string
  contractContent: string
  contractType: 'sales' | 'purchase'
}

export const PDFContractDocument: React.FC<PDFContractDocumentProps> = ({
  contractNumber,
  contractContent,
  contractType,
}) => {
  const parseContractContent = (content: string) => {
    const lines = content.split('\n')
    const elements: JSX.Element[] = []
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim()
      
      if (!trimmedLine) {
        elements.push(<View key={index} style={{ height: 10 }} />)
        return
      }
      
      // Contract title
      if (trimmedLine.includes('CONTRACT') && index < 5) {
        elements.push(
          <View key={index} style={styles.contractTitle}>
            <Text style={styles.contractTitleText}>{trimmedLine}</Text>
            <View style={styles.titleUnderline} />
          </View>
        )
        return
      }
      
      // Section dividers
      if (trimmedLine.includes('═══')) {
        elements.push(<View key={index} style={styles.sectionDivider} />)
        return
      }
      
      // Main section titles
      if (trimmedLine.match(/^[A-Z\s]+:?$/) && trimmedLine.length > 3) {
        elements.push(
          <Text key={index} style={styles.sectionTitle}>
            {trimmedLine}
          </Text>
        )
        return
      }
      
      // Numbered sections
      if (trimmedLine.match(/^\d+\./)) {
        elements.push(
          <Text key={index} style={styles.subsectionTitle}>
            {trimmedLine}
          </Text>
        )
        return
      }
      
      // Signature lines
      if (trimmedLine.includes('_________________________')) {
        elements.push(
          <View key={index} style={styles.signatureSection}>
            <View style={styles.signatureLine} />
            <Text style={styles.dateText}>Date: ___________</Text>
          </View>
        )
        return
      }
      
      // Regular paragraphs
      elements.push(
        <Text key={index} style={styles.paragraph}>
          {trimmedLine}
        </Text>
      )
    })
    
    return elements
  }

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Professional Header */}
        <View style={styles.header}>
          <View style={styles.logo}>
            <View style={styles.logoBox}>
              <Text style={styles.logoText}>ERP</Text>
            </View>
            <View style={styles.companyInfo}>
              <Text style={styles.companyName}>Manufacturing ERP</Text>
              <Text style={styles.companySubtitle}>Professional Contract Management System</Text>
            </View>
          </View>
          <View style={styles.documentInfo}>
            <Text style={{ fontWeight: 'bold' }}>Document: {contractNumber}</Text>
            <Text>{new Date().toLocaleDateString()}</Text>
          </View>
        </View>

        {/* Contract Content */}
        {parseContractContent(contractContent)}

        {/* Professional Footer */}
        <View style={styles.footer}>
          <View style={styles.footerContent}>
            <Text style={styles.footerTitle}>
              This document was generated electronically and is valid without signature.
            </Text>
            <Text style={styles.footerText}>Document ID: {contractNumber}</Text>
            <Text style={styles.footerText}>
              Generated on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
            </Text>
            <Text style={styles.footerText}>
              Manufacturing ERP System - Professional Contract Management
            </Text>
          </View>
        </View>
      </Page>
    </Document>
  )
}

// Export function to generate and download PDF
export const generateContractPDF = async (
  contractNumber: string,
  contractContent: string,
  contractType: 'sales' | 'purchase'
) => {
  const doc = (
    <PDFContractDocument
      contractNumber={contractNumber}
      contractContent={contractContent}
      contractType={contractType}
    />
  )
  
  const asPdf = pdf(doc)
  const blob = await asPdf.toBlob()
  
  // Create download link
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${contractType}-contract-${contractNumber}.pdf`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  return blob
}
