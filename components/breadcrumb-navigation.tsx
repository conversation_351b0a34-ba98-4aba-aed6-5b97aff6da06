"use client"

import Link from "next/link"
import { ChevronRight, Home } from "lucide-react"
import { cn } from "@/lib/utils"

export interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface BreadcrumbNavigationProps {
  items: BreadcrumbItem[]
  className?: string
}

export function BreadcrumbNavigation({ items, className }: BreadcrumbNavigationProps) {
  return (
    <nav className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {/* Home/Dashboard link */}
        <li>
          <Link
            href="/dashboard"
            className="flex items-center hover:text-foreground transition-colors"
          >
            <Home className="h-4 w-4" />
            <span className="sr-only">Dashboard</span>
          </Link>
        </li>
        
        {/* Breadcrumb items */}
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            <ChevronRight className="h-4 w-4 mx-1" />
            {item.current || !item.href ? (
              <span className={cn(
                "font-medium",
                item.current ? "text-foreground" : "text-muted-foreground"
              )}>
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href}
                className="hover:text-foreground transition-colors"
              >
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

// Utility function to generate common breadcrumb patterns
export function createContractBreadcrumbs(
  contractType: 'sales' | 'purchase',
  contractNumber?: string,
  action?: 'view' | 'edit' | 'add'
): BreadcrumbItem[] {
  const baseLabel = contractType === 'sales' ? 'Sales Contracts' : 'Purchase Contracts'
  const basePath = contractType === 'sales' ? '/sales-contracts' : '/purchase-contracts'
  
  const items: BreadcrumbItem[] = [
    {
      label: baseLabel,
      href: basePath
    }
  ]
  
  if (action === 'add') {
    items.push({
      label: `Add ${contractType === 'sales' ? 'Sales' : 'Purchase'} Contract`,
      current: true
    })
  } else if (action === 'view' && contractNumber) {
    items.push({
      label: `View Contract ${contractNumber}`,
      current: true
    })
  } else if (action === 'edit' && contractNumber) {
    items.push({
      label: `Edit Contract ${contractNumber}`,
      current: true
    })
  }
  
  return items
}

// Utility function for other common breadcrumb patterns
export function createModuleBreadcrumbs(
  moduleName: string,
  modulePath: string,
  itemName?: string,
  action?: string
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    {
      label: moduleName,
      href: modulePath
    }
  ]
  
  if (action && itemName) {
    items.push({
      label: `${action} ${itemName}`,
      current: true
    })
  } else if (itemName) {
    items.push({
      label: itemName,
      current: true
    })
  }
  
  return items
}
