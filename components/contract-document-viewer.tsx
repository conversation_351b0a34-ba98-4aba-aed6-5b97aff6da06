"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Download, FileText, Printer, Share2, X, AlertCircle, Building } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

interface ContractDocumentViewerProps {
  isOpen?: boolean
  onClose?: () => void
  contractNumber: string
  documentContent?: string
  content?: string
  contractType?: "sales" | "purchase"
  fullPage?: boolean
}

export function ContractDocumentViewer({
  isOpen = false,
  onClose,
  contractNumber,
  documentContent,
  content,
  contractType = "sales",
  fullPage = false
}: ContractDocumentViewerProps) {
  const [isExporting, setIsExporting] = useState(false)
  const documentRef = useRef<HTMLDivElement>(null)

  // Use content or documentContent
  const actualContent = content || documentContent || ""

  const exportToPDF = async () => {
    setIsExporting(true)
    try {
      // Use client-side PDF generation with the original working approach
      const { generateContractPDF } = await import('./pdf-contract-document')
      await generateContractPDF(contractNumber, actualContent, contractType)

      const fileName = `${contractType}-contract-${contractNumber}.pdf`
      toast.success(`Contract exported as ${fileName}`)
    } catch (error) {
      console.error('PDF export error:', error)
      toast.error('Failed to export PDF. Please try again.')
    } finally {
      setIsExporting(false)
    }
  }





  const printDocument = () => {
    if (!documentRef.current) return

    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Contract ${contractNumber}</title>
          <style>
            body { 
              font-family: 'Times New Roman', serif; 
              line-height: 1.6; 
              margin: 0; 
              padding: 20px;
              color: #000;
            }
            .contract-header { 
              text-align: center; 
              margin-bottom: 30px; 
              border-bottom: 2px solid #000;
              padding-bottom: 20px;
            }
            .contract-title { 
              font-size: 24px; 
              font-weight: bold; 
              margin-bottom: 10px; 
            }
            .contract-meta { 
              font-size: 14px; 
              margin-bottom: 5px; 
            }
            .section-divider { 
              border-top: 1px solid #000; 
              margin: 20px 0; 
            }
            .section-title { 
              font-weight: bold; 
              font-size: 16px; 
              margin: 20px 0 10px 0; 
            }
            .signature-section { 
              margin-top: 40px; 
              border-top: 2px solid #000; 
              padding-top: 20px; 
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          ${documentRef.current.innerHTML}
        </body>
      </html>
    `)

    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
    printWindow.close()
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(actualContent)
      toast.success('Contract content copied to clipboard')
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }

  // Check if company profile data is missing
  const hasPlaceholderData = actualContent.includes('[Your Company Name]') ||
    actualContent.includes('[Your Company Address]') ||
    actualContent.includes('[Your Email]') ||
    actualContent.includes('[Your Tax ID]')

  // If fullPage mode, render without dialog wrapper
  if (fullPage) {
    return (
      <div className="contract-document-viewer">
        {/* Company Profile Alert */}
        {hasPlaceholderData && (
          <Alert className="mb-6 border-orange-200 bg-orange-50">
            <AlertCircle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <div className="flex items-center justify-between">
                <span>
                  <strong>Company profile incomplete:</strong> Some fields show placeholder text.
                  Complete your company profile to display actual business information in contracts.
                </span>
                <Link href="/company-profile">
                  <Button size="sm" variant="outline" className="ml-4 border-orange-300 text-orange-700 hover:bg-orange-100">
                    <Building className="h-4 w-4 mr-2" />
                    Complete Profile
                  </Button>
                </Link>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <div
          ref={documentRef}
          className="bg-white shadow-lg border border-gray-300 mx-auto p-10 font-serif text-sm leading-relaxed text-black"
          style={{
            maxWidth: '900px',
            minHeight: '297mm'
          }}
        >
          <ContractDocument content={actualContent} contractNumber={contractNumber} />
        </div>
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[98vw] max-w-none h-[98vh] overflow-hidden flex flex-col p-0">
        <DialogHeader className="flex-shrink-0 border-b p-6 bg-white">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <DialogTitle className="flex items-center gap-2 text-xl">
                <FileText className="h-5 w-5" />
                Contract Document - {contractNumber}
              </DialogTitle>
              <DialogDescription className="text-sm text-muted-foreground mt-1">
                Professional {contractType} contract ready for client delivery
              </DialogDescription>
            </div>
            <div className="flex flex-wrap items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={copyToClipboard}
                className="flex items-center gap-1"
              >
                <Share2 className="h-4 w-4" />
                <span className="hidden sm:inline">Copy</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={printDocument}
                className="flex items-center gap-1"
              >
                <Printer className="h-4 w-4" />
                <span className="hidden sm:inline">Print</span>
              </Button>
              <Button
                onClick={exportToPDF}
                disabled={isExporting}
                className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700"
              >
                <Download className="h-4 w-4" />
                <span className="hidden sm:inline">{isExporting ? 'Exporting...' : 'Export PDF'}</span>
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Company Profile Alert */}
        {hasPlaceholderData && (
          <div className="flex-shrink-0 p-4 border-b bg-orange-50">
            <Alert className="border-orange-200 bg-orange-50">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <div className="flex items-center justify-between">
                  <span>
                    <strong>Company profile incomplete:</strong> Some fields show placeholder text.
                    Complete your company profile to display actual business information in contracts.
                  </span>
                  <Link href="/company-profile">
                    <Button size="sm" variant="outline" className="ml-4 border-orange-300 text-orange-700 hover:bg-orange-100">
                      <Building className="h-4 w-4 mr-2" />
                      Complete Profile
                    </Button>
                  </Link>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        )}

        <div className="flex-1 overflow-y-auto bg-gray-100 p-6">
          <div className="w-full max-w-none mx-auto">
            <div
              ref={documentRef}
              className="bg-white shadow-lg border border-gray-300 mx-auto"
              style={{
                width: '100%',
                maxWidth: '900px',
                minHeight: '297mm',
                padding: '40px',
                fontFamily: '"Times New Roman", serif',
                fontSize: '14pt',
                lineHeight: '1.6',
                color: '#000000',
                backgroundColor: '#ffffff'
              }}
            >
              <ContractDocument content={actualContent} contractNumber={contractNumber} />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

interface ContractDocumentProps {
  content: string
  contractNumber: string
}

function ContractDocument({ content, contractNumber }: ContractDocumentProps) {
  // Parse and format the contract content
  const formatContractContent = (content: string) => {
    const lines = content.split('\n')
    const formattedLines: JSX.Element[] = []

    lines.forEach((line, index) => {
      const trimmedLine = line.trim()

      // Skip empty lines but preserve spacing
      if (!trimmedLine) {
        formattedLines.push(<div key={index} className="h-3" />)
        return
      }

      // Contract title
      if (trimmedLine.includes('CONTRACT') && index < 5) {
        formattedLines.push(
          <div key={index} className="contract-header text-center mb-8 pb-6" style={{ borderBottom: '2px solid #1f2937' }}>
            <h1 className="text-4xl font-bold mb-4 tracking-wide uppercase" style={{ color: '#111827' }}>
              {trimmedLine}
            </h1>
            <div className="mx-auto" style={{ width: '96px', height: '4px', backgroundColor: '#2563eb' }}></div>
          </div>
        )
        return
      }

      // Section headers (lines with ═══)
      if (trimmedLine.includes('═══')) {
        formattedLines.push(
          <div key={index} className="section-divider my-6" style={{ borderTop: '2px solid #1f2937' }} />
        )
        return
      }

      // Main section titles
      if (trimmedLine.match(/^[A-Z\s]+:?$/) && trimmedLine.length > 3) {
        formattedLines.push(
          <h2 key={index} className="section-title text-xl font-bold mt-8 mb-4 p-3 rounded"
            style={{
              color: '#1f2937',
              backgroundColor: '#f9fafb',
              borderLeft: '4px solid #2563eb'
            }}>
            {trimmedLine}
          </h2>
        )
        return
      }

      // Numbered sections
      if (trimmedLine.match(/^\d+\./)) {
        formattedLines.push(
          <h3 key={index} className="subsection-title text-lg font-semibold mt-6 mb-3 pb-1"
            style={{
              color: '#1e40af',
              borderBottom: '1px solid #d1d5db'
            }}>
            {trimmedLine}
          </h3>
        )
        return
      }

      // Signature lines
      if (trimmedLine.includes('_________________________')) {
        formattedLines.push(
          <div key={index} className="signature-line mt-12 mb-4 flex items-center">
            <div className="w-80 mr-8" style={{ borderBottom: '2px solid #1f2937' }}></div>
            <div className="text-sm" style={{ color: '#4b5563' }}>
              <div>Date: ___________</div>
            </div>
          </div>
        )
        return
      }

      // Regular content
      formattedLines.push(
        <p key={index} className="mb-2 text-justify">
          {trimmedLine}
        </p>
      )
    })

    return formattedLines
  }

  return (
    <div className="contract-document">
      {/* Professional Header */}
      <div className="document-header mb-8 pb-6" style={{ borderBottom: '2px solid #2563eb' }}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-16 h-16 rounded-lg flex items-center justify-center mr-4"
              style={{ backgroundColor: '#2563eb' }}>
              <FileText className="h-8 w-8" style={{ color: '#ffffff' }} />
            </div>
            <div>
              <h1 className="text-2xl font-bold" style={{ color: '#111827' }}>Manufacturing ERP</h1>
              <p className="text-sm" style={{ color: '#4b5563' }}>Professional Contract Management System</p>
            </div>
          </div>
          <div className="text-right text-sm" style={{ color: '#4b5563' }}>
            <p className="font-medium">Document: {contractNumber}</p>
            <p>{new Date().toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {formatContractContent(content)}

      {/* Professional Footer */}
      <div className="mt-16 pt-8" style={{ borderTop: '2px solid #1f2937' }}>
        <div className="p-6 rounded-lg" style={{ backgroundColor: '#f9fafb' }}>
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-1" style={{ backgroundColor: '#2563eb' }}></div>
              <FileText className="h-6 w-6 mx-4" style={{ color: '#2563eb' }} />
              <div className="w-12 h-1" style={{ backgroundColor: '#2563eb' }}></div>
            </div>
            <p className="text-sm font-medium mb-2" style={{ color: '#374151' }}>
              This document was generated electronically and is valid without signature.
            </p>
            <div className="text-xs space-y-1" style={{ color: '#6b7280' }}>
              <p>Document ID: {contractNumber}</p>
              <p>Generated on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}</p>
              <p>Manufacturing ERP System - Professional Contract Management</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
