"use client"

import * as React from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/ui/loading"
import { AlertTriangle, Trash2, Check, X } from "lucide-react"

interface ConfirmationDialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  variant?: "default" | "destructive" | "warning"
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  children?: React.ReactNode
}

export function ConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "default",
  onConfirm,
  onCancel,
  isLoading = false,
  children
}: ConfirmationDialogProps) {
  const [internalLoading, setInternalLoading] = React.useState(false)
  
  const handleConfirm = async () => {
    try {
      setInternalLoading(true)
      await onConfirm()
      onOpenChange?.(false)
    } catch (error) {
      // Error handling is done by the parent component
    } finally {
      setInternalLoading(false)
    }
  }

  const handleCancel = () => {
    onCancel?.()
    onOpenChange?.(false)
  }

  const isProcessing = isLoading || internalLoading

  const getVariantStyles = () => {
    switch (variant) {
      case "destructive":
        return {
          icon: <Trash2 className="h-6 w-6 text-red-600" />,
          confirmClass: "bg-red-600 hover:bg-red-700 text-white"
        }
      case "warning":
        return {
          icon: <AlertTriangle className="h-6 w-6 text-yellow-600" />,
          confirmClass: "bg-yellow-600 hover:bg-yellow-700 text-white"
        }
      default:
        return {
          icon: <Check className="h-6 w-6 text-blue-600" />,
          confirmClass: "bg-blue-600 hover:bg-blue-700 text-white"
        }
    }
  }

  const { icon, confirmClass } = getVariantStyles()

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {children && <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>}
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            {icon}
            <AlertDialogTitle>{title}</AlertDialogTitle>
          </div>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={isProcessing}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isProcessing}
            className={confirmClass}
          >
            {isProcessing && <LoadingSpinner size="sm" className="mr-2" />}
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// Specialized delete confirmation dialog
interface DeleteConfirmationProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  itemName: string
  itemType?: string
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  children?: React.ReactNode
}

export function DeleteConfirmation({
  open,
  onOpenChange,
  itemName,
  itemType = "item",
  onConfirm,
  onCancel,
  isLoading,
  children
}: DeleteConfirmationProps) {
  return (
    <ConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      title={`Delete ${itemType}`}
      description={`Are you sure you want to delete "${itemName}"? This action cannot be undone.`}
      confirmText="Delete"
      cancelText="Cancel"
      variant="destructive"
      onConfirm={onConfirm}
      onCancel={onCancel}
      isLoading={isLoading}
    >
      {children}
    </ConfirmationDialog>
  )
}

// Bulk delete confirmation
interface BulkDeleteConfirmationProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  count: number
  itemType?: string
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  children?: React.ReactNode
}

export function BulkDeleteConfirmation({
  open,
  onOpenChange,
  count,
  itemType = "items",
  onConfirm,
  onCancel,
  isLoading,
  children
}: BulkDeleteConfirmationProps) {
  return (
    <ConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      title={`Delete ${count} ${itemType}`}
      description={`Are you sure you want to delete ${count} ${itemType}? This action cannot be undone.`}
      confirmText={`Delete ${count} ${itemType}`}
      cancelText="Cancel"
      variant="destructive"
      onConfirm={onConfirm}
      onCancel={onCancel}
      isLoading={isLoading}
    >
      {children}
    </ConfirmationDialog>
  )
}

// Status change confirmation
interface StatusChangeConfirmationProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  itemName: string
  currentStatus: string
  newStatus: string
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  children?: React.ReactNode
}

export function StatusChangeConfirmation({
  open,
  onOpenChange,
  itemName,
  currentStatus,
  newStatus,
  onConfirm,
  onCancel,
  isLoading,
  children
}: StatusChangeConfirmationProps) {
  return (
    <ConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Change Status"
      description={`Change status of "${itemName}" from "${currentStatus}" to "${newStatus}"?`}
      confirmText="Change Status"
      cancelText="Cancel"
      variant="warning"
      onConfirm={onConfirm}
      onCancel={onCancel}
      isLoading={isLoading}
    >
      {children}
    </ConfirmationDialog>
  )
}

// Hook for managing confirmation dialogs
export function useConfirmationDialog() {
  const [isOpen, setIsOpen] = React.useState(false)
  const [isLoading, setIsLoading] = React.useState(false)
  
  const openDialog = React.useCallback(() => setIsOpen(true), [])
  const closeDialog = React.useCallback(() => setIsOpen(false), [])
  
  const withConfirmation = React.useCallback(async (action: () => Promise<void>) => {
    try {
      setIsLoading(true)
      await action()
      closeDialog()
    } catch (error) {
      // Error is handled by the action itself
    } finally {
      setIsLoading(false)
    }
  }, [closeDialog])
  
  return {
    isOpen,
    isLoading,
    openDialog,
    closeDialog,
    withConfirmation
  }
}

// Simple confirmation hook for quick use
export function useSimpleConfirmation() {
  return React.useCallback((message: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const confirmed = window.confirm(message)
      resolve(confirmed)
    })
  }, [])
}
