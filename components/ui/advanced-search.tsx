"use client"

import * as React from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { 
  Search, 
  Filter, 
  X, 
  Calendar as CalendarIcon,
  ChevronDown
} from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

// Filter types
export interface FilterOption {
  value: string
  label: string
  count?: number
}

export interface FilterField {
  key: string
  label: string
  type: "text" | "select" | "date" | "dateRange" | "number" | "boolean"
  options?: FilterOption[]
  placeholder?: string
  multiple?: boolean
}

export interface FilterValue {
  key: string
  value: any
  label?: string
}

export interface SearchFilters {
  search?: string
  filters: FilterValue[]
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

// Search and filter context
interface SearchFilterContextType {
  filters: SearchFilters
  updateSearch: (search: string) => void
  updateFilter: (key: string, value: any, label?: string) => void
  removeFilter: (key: string) => void
  clearFilters: () => void
  updateSort: (sortBy: string, sortOrder?: "asc" | "desc") => void
}

const SearchFilterContext = React.createContext<SearchFilterContextType | null>(null)

// Search filter provider
interface SearchFilterProviderProps {
  children: React.ReactNode
  initialFilters?: SearchFilters
  onFiltersChange?: (filters: SearchFilters) => void
}

export function SearchFilterProvider({ 
  children, 
  initialFilters = { filters: [] },
  onFiltersChange 
}: SearchFilterProviderProps) {
  const [filters, setFilters] = React.useState<SearchFilters>(initialFilters)

  const updateSearch = React.useCallback((search: string) => {
    const newFilters = { ...filters, search }
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }, [filters, onFiltersChange])

  const updateFilter = React.useCallback((key: string, value: any, label?: string) => {
    const newFilters = {
      ...filters,
      filters: [
        ...filters.filters.filter(f => f.key !== key),
        { key, value, label }
      ]
    }
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }, [filters, onFiltersChange])

  const removeFilter = React.useCallback((key: string) => {
    const newFilters = {
      ...filters,
      filters: filters.filters.filter(f => f.key !== key)
    }
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }, [filters, onFiltersChange])

  const clearFilters = React.useCallback(() => {
    const newFilters = { search: "", filters: [] }
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }, [onFiltersChange])

  const updateSort = React.useCallback((sortBy: string, sortOrder: "asc" | "desc" = "asc") => {
    const newFilters = { ...filters, sortBy, sortOrder }
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }, [filters, onFiltersChange])

  const value = React.useMemo(() => ({
    filters,
    updateSearch,
    updateFilter,
    removeFilter,
    clearFilters,
    updateSort
  }), [filters, updateSearch, updateFilter, removeFilter, clearFilters, updateSort])

  return (
    <SearchFilterContext.Provider value={value}>
      {children}
    </SearchFilterContext.Provider>
  )
}

// Hook to use search filters
export function useSearchFilters() {
  const context = React.useContext(SearchFilterContext)
  if (!context) {
    throw new Error("useSearchFilters must be used within a SearchFilterProvider")
  }
  return context
}

// Search input component
interface SearchInputProps {
  placeholder?: string
  className?: string
  debounceMs?: number
}

export function SearchInput({ 
  placeholder = "Search...", 
  className,
  debounceMs = 300
}: SearchInputProps) {
  const { filters, updateSearch } = useSearchFilters()
  const [localValue, setLocalValue] = React.useState(filters.search || "")
  const timeoutRef = React.useRef<NodeJS.Timeout>()

  React.useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      updateSearch(localValue)
    }, debounceMs)

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [localValue, updateSearch, debounceMs])

  return (
    <div className={cn("relative", className)}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      <Input
        value={localValue}
        onChange={(e) => setLocalValue(e.target.value)}
        placeholder={placeholder}
        className="pl-10"
      />
    </div>
  )
}

// Filter dropdown component
interface FilterDropdownProps {
  field: FilterField
  className?: string
}

export function FilterDropdown({ field, className }: FilterDropdownProps) {
  const { filters, updateFilter, removeFilter } = useSearchFilters()
  const currentFilter = filters.filters.find(f => f.key === field.key)

  const handleValueChange = (value: string) => {
    if (value === "clear") {
      removeFilter(field.key)
      return
    }

    const option = field.options?.find(opt => opt.value === value)
    updateFilter(field.key, value, option?.label || value)
  }

  return (
    <Select value={currentFilter?.value || ""} onValueChange={handleValueChange}>
      <SelectTrigger className={cn("w-[180px]", className)}>
        <SelectValue placeholder={field.placeholder || `Select ${field.label}`} />
      </SelectTrigger>
      <SelectContent>
        {currentFilter && (
          <>
            <SelectItem value="clear">
              <span className="text-gray-500">Clear filter</span>
            </SelectItem>
            <div className="border-t my-1" />
          </>
        )}
        {field.options?.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            <div className="flex items-center justify-between w-full">
              <span>{option.label}</span>
              {option.count !== undefined && (
                <Badge variant="secondary" className="ml-2 text-xs">
                  {option.count}
                </Badge>
              )}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

// Date range picker component
interface DateRangePickerProps {
  field: FilterField
  className?: string
}

export function DateRangePicker({ field, className }: DateRangePickerProps) {
  const { filters, updateFilter, removeFilter } = useSearchFilters()
  const currentFilter = filters.filters.find(f => f.key === field.key)
  const [isOpen, setIsOpen] = React.useState(false)

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      updateFilter(field.key, date.toISOString(), format(date, "PPP"))
    } else {
      removeFilter(field.key)
    }
    setIsOpen(false)
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-[240px] justify-start text-left font-normal",
            !currentFilter && "text-muted-foreground",
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {currentFilter?.label || field.placeholder || `Select ${field.label}`}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={currentFilter?.value ? new Date(currentFilter.value) : undefined}
          onSelect={handleDateSelect}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}

// Advanced filter panel
interface AdvancedFilterPanelProps {
  fields: FilterField[]
  className?: string
}

export function AdvancedFilterPanel({ fields, className }: AdvancedFilterPanelProps) {
  const [isOpen, setIsOpen] = React.useState(false)

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className={className}>
          <Filter className="mr-2 h-4 w-4" />
          Filters
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="start">
        <div className="space-y-4">
          <div className="font-medium">Advanced Filters</div>
          {fields.map((field) => (
            <div key={field.key} className="space-y-2">
              <Label>{field.label}</Label>
              {field.type === "select" && <FilterDropdown field={field} />}
              {field.type === "date" && <DateRangePicker field={field} />}
              {field.type === "text" && (
                <Input placeholder={field.placeholder} />
              )}
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Active filters display
export function ActiveFilters({ className }: { className?: string }) {
  const { filters, removeFilter, clearFilters } = useSearchFilters()
  const activeFilters = filters.filters.filter(f => f.value !== undefined && f.value !== "")

  if (activeFilters.length === 0 && !filters.search) {
    return null
  }

  return (
    <div className={cn("flex flex-wrap items-center gap-2", className)}>
      {filters.search && (
        <Badge variant="secondary" className="gap-1">
          Search: "{filters.search}"
          <X 
            className="h-3 w-3 cursor-pointer" 
            onClick={() => removeFilter("search")}
          />
        </Badge>
      )}
      
      {activeFilters.map((filter) => (
        <Badge key={filter.key} variant="secondary" className="gap-1">
          {filter.label || filter.value}
          <X 
            className="h-3 w-3 cursor-pointer" 
            onClick={() => removeFilter(filter.key)}
          />
        </Badge>
      ))}
      
      {(activeFilters.length > 0 || filters.search) && (
        <Button
          variant="ghost"
          size="sm"
          onClick={clearFilters}
          className="h-6 px-2 text-xs"
        >
          Clear all
        </Button>
      )}
    </div>
  )
}

// Complete search and filter bar
interface SearchFilterBarProps {
  fields: FilterField[]
  searchPlaceholder?: string
  className?: string
}

export function SearchFilterBar({ 
  fields, 
  searchPlaceholder,
  className 
}: SearchFilterBarProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center gap-4">
        <SearchInput 
          placeholder={searchPlaceholder}
          className="flex-1 max-w-md"
        />
        <AdvancedFilterPanel fields={fields} />
      </div>
      <ActiveFilters />
    </div>
  )
}
