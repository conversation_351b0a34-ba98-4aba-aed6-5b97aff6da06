"use client"

import * as React from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { But<PERSON> } from "@/components/ui/button"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { 
  Trash2, 
  Edit, 
  Download, 
  Upload, 
  MoreHorizontal, 
  CheckSquare, 
  Square,
  X
} from "lucide-react"
import { cn } from "@/lib/utils"
import { BulkDeleteConfirmation, useConfirmationDialog } from "./confirmation-dialog"

// Bulk selection context
interface BulkSelectionContextType<T> {
  selectedItems: Set<string>
  selectItem: (id: string) => void
  deselectItem: (id: string) => void
  selectAll: (items: T[]) => void
  deselectAll: () => void
  isSelected: (id: string) => boolean
  isAllSelected: (items: T[]) => boolean
  isIndeterminate: (items: T[]) => boolean
  selectedCount: number
}

const BulkSelectionContext = React.createContext<BulkSelectionContextType<any> | null>(null)

// Bulk selection provider
interface BulkSelectionProviderProps<T extends { id: string }> {
  children: React.ReactNode
  onSelectionChange?: (selectedIds: string[]) => void
}

export function BulkSelectionProvider<T extends { id: string }>({ 
  children, 
  onSelectionChange 
}: BulkSelectionProviderProps<T>) {
  const [selectedItems, setSelectedItems] = React.useState<Set<string>>(new Set())

  const selectItem = React.useCallback((id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      newSet.add(id)
      onSelectionChange?.(Array.from(newSet))
      return newSet
    })
  }, [onSelectionChange])

  const deselectItem = React.useCallback((id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      newSet.delete(id)
      onSelectionChange?.(Array.from(newSet))
      return newSet
    })
  }, [onSelectionChange])

  const selectAll = React.useCallback((items: T[]) => {
    const allIds = items.map(item => item.id)
    setSelectedItems(new Set(allIds))
    onSelectionChange?.(allIds)
  }, [onSelectionChange])

  const deselectAll = React.useCallback(() => {
    setSelectedItems(new Set())
    onSelectionChange?.([])
  }, [onSelectionChange])

  const isSelected = React.useCallback((id: string) => {
    return selectedItems.has(id)
  }, [selectedItems])

  const isAllSelected = React.useCallback((items: T[]) => {
    return items.length > 0 && items.every(item => selectedItems.has(item.id))
  }, [selectedItems])

  const isIndeterminate = React.useCallback((items: T[]) => {
    const selectedCount = items.filter(item => selectedItems.has(item.id)).length
    return selectedCount > 0 && selectedCount < items.length
  }, [selectedItems])

  const value = React.useMemo(() => ({
    selectedItems,
    selectItem,
    deselectItem,
    selectAll,
    deselectAll,
    isSelected,
    isAllSelected,
    isIndeterminate,
    selectedCount: selectedItems.size
  }), [
    selectedItems,
    selectItem,
    deselectItem,
    selectAll,
    deselectAll,
    isSelected,
    isAllSelected,
    isIndeterminate
  ])

  return (
    <BulkSelectionContext.Provider value={value}>
      {children}
    </BulkSelectionContext.Provider>
  )
}

// Hook to use bulk selection
export function useBulkSelection<T extends { id: string }>() {
  const context = React.useContext(BulkSelectionContext)
  if (!context) {
    throw new Error("useBulkSelection must be used within a BulkSelectionProvider")
  }
  return context as BulkSelectionContextType<T>
}

// Bulk selection header checkbox
interface BulkSelectHeaderProps<T extends { id: string }> {
  items: T[]
  className?: string
}

export function BulkSelectHeader<T extends { id: string }>({ 
  items, 
  className 
}: BulkSelectHeaderProps<T>) {
  const { selectAll, deselectAll, isAllSelected, isIndeterminate } = useBulkSelection<T>()

  const handleChange = (checked: boolean) => {
    if (checked) {
      selectAll(items)
    } else {
      deselectAll()
    }
  }

  return (
    <Checkbox
      checked={isAllSelected(items)}
      onCheckedChange={handleChange}
      className={className}
      ref={(ref) => {
        if (ref) {
          ref.indeterminate = isIndeterminate(items)
        }
      }}
    />
  )
}

// Bulk selection row checkbox
interface BulkSelectRowProps {
  itemId: string
  className?: string
}

export function BulkSelectRow({ itemId, className }: BulkSelectRowProps) {
  const { selectItem, deselectItem, isSelected } = useBulkSelection()

  const handleChange = (checked: boolean) => {
    if (checked) {
      selectItem(itemId)
    } else {
      deselectItem(itemId)
    }
  }

  return (
    <Checkbox
      checked={isSelected(itemId)}
      onCheckedChange={handleChange}
      className={className}
    />
  )
}

// Bulk action toolbar
interface BulkAction {
  id: string
  label: string
  icon?: React.ComponentType<{ className?: string }>
  variant?: "default" | "destructive" | "outline" | "secondary"
  onClick: (selectedIds: string[]) => void | Promise<void>
  disabled?: boolean
}

interface BulkActionToolbarProps {
  actions: BulkAction[]
  className?: string
  itemType?: string
}

export function BulkActionToolbar({ 
  actions, 
  className,
  itemType = "items"
}: BulkActionToolbarProps) {
  const { selectedCount, selectedItems, deselectAll } = useBulkSelection()
  const [isLoading, setIsLoading] = React.useState<string | null>(null)

  if (selectedCount === 0) {
    return null
  }

  const handleAction = async (action: BulkAction) => {
    if (action.disabled) return

    try {
      setIsLoading(action.id)
      await action.onClick(Array.from(selectedItems))
      deselectAll()
    } catch (error) {
      console.error(`Bulk action ${action.id} failed:`, error)
    } finally {
      setIsLoading(null)
    }
  }

  return (
    <div className={cn(
      "flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg",
      className
    )}>
      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
        {selectedCount} {itemType} selected
      </Badge>
      
      <div className="flex items-center gap-1 ml-auto">
        {actions.map((action) => {
          const Icon = action.icon
          const loading = isLoading === action.id

          return (
            <Button
              key={action.id}
              variant={action.variant || "outline"}
              size="sm"
              onClick={() => handleAction(action)}
              disabled={action.disabled || loading}
              className="h-8"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
              ) : (
                Icon && <Icon className="h-4 w-4" />
              )}
              <span className="ml-1">{action.label}</span>
            </Button>
          )
        })}
        
        <Button
          variant="ghost"
          size="sm"
          onClick={deselectAll}
          className="h-8 ml-2"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

// Pre-built bulk actions
export const createBulkDeleteAction = (
  onDelete: (ids: string[]) => Promise<void>,
  itemType = "items"
): BulkAction => ({
  id: "delete",
  label: "Delete",
  icon: Trash2,
  variant: "destructive",
  onClick: onDelete
})

export const createBulkEditAction = (
  onEdit: (ids: string[]) => void
): BulkAction => ({
  id: "edit",
  label: "Edit",
  icon: Edit,
  variant: "outline",
  onClick: onEdit
})

export const createBulkExportAction = (
  onExport: (ids: string[]) => Promise<void>
): BulkAction => ({
  id: "export",
  label: "Export",
  icon: Download,
  variant: "outline",
  onClick: onExport
})

// Bulk operations hook with common actions
export function useBulkOperations<T extends { id: string }>(
  items: T[],
  options: {
    onDelete?: (ids: string[]) => Promise<void>
    onEdit?: (ids: string[]) => void
    onExport?: (ids: string[]) => Promise<void>
    onStatusChange?: (ids: string[], status: string) => Promise<void>
    itemType?: string
    customActions?: BulkAction[]
  }
) {
  const { 
    onDelete, 
    onEdit, 
    onExport, 
    onStatusChange, 
    itemType = "items",
    customActions = []
  } = options

  const deleteDialog = useConfirmationDialog()

  const actions: BulkAction[] = React.useMemo(() => {
    const baseActions: BulkAction[] = []

    if (onEdit) {
      baseActions.push(createBulkEditAction(onEdit))
    }

    if (onExport) {
      baseActions.push(createBulkExportAction(onExport))
    }

    if (onDelete) {
      baseActions.push({
        id: "delete",
        label: "Delete",
        icon: Trash2,
        variant: "destructive",
        onClick: (ids) => {
          deleteDialog.openDialog()
          deleteDialog.withConfirmation(() => onDelete(ids))
        }
      })
    }

    return [...baseActions, ...customActions]
  }, [onDelete, onEdit, onExport, customActions, deleteDialog])

  const statusActions = React.useMemo(() => {
    if (!onStatusChange) return []

    return [
      {
        id: "activate",
        label: "Activate",
        onClick: (ids: string[]) => onStatusChange(ids, "active")
      },
      {
        id: "deactivate", 
        label: "Deactivate",
        onClick: (ids: string[]) => onStatusChange(ids, "inactive")
      }
    ]
  }, [onStatusChange])

  return {
    actions,
    statusActions,
    deleteDialog
  }
}

// Complete bulk operations table wrapper
interface BulkOperationsTableProps<T extends { id: string }> {
  items: T[]
  children: React.ReactNode
  onDelete?: (ids: string[]) => Promise<void>
  onEdit?: (ids: string[]) => void
  onExport?: (ids: string[]) => Promise<void>
  onStatusChange?: (ids: string[], status: string) => Promise<void>
  itemType?: string
  customActions?: BulkAction[]
  className?: string
}

export function BulkOperationsTable<T extends { id: string }>({
  items,
  children,
  onDelete,
  onEdit,
  onExport,
  onStatusChange,
  itemType = "items",
  customActions = [],
  className
}: BulkOperationsTableProps<T>) {
  const { actions, deleteDialog } = useBulkOperations(items, {
    onDelete,
    onEdit,
    onExport,
    onStatusChange,
    itemType,
    customActions
  })

  return (
    <BulkSelectionProvider>
      <div className={cn("space-y-4", className)}>
        <BulkActionToolbar actions={actions} itemType={itemType} />
        {children}
        
        <BulkDeleteConfirmation
          open={deleteDialog.isOpen}
          onOpenChange={(open) => !open && deleteDialog.closeDialog()}
          count={0} // This would be passed from the actual selection
          itemType={itemType}
          onConfirm={() => {}} // This would be handled by the action
          isLoading={deleteDialog.isLoading}
        />
      </div>
    </BulkSelectionProvider>
  )
}
