"use client"

import * as React from "react"
import { FieldError, FieldPath, FieldValues, UseFormRegister } from "react-hook-form"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LoadingSpinner } from "@/components/ui/loading"

interface FormFieldProps<T extends FieldValues> {
  name: FieldPath<T>
  label: string
  register: UseFormRegister<T>
  error?: FieldError
  type?: "text" | "email" | "number" | "password" | "tel" | "url"
  placeholder?: string
  required?: boolean
  className?: string
  disabled?: boolean
}

interface TextareaFieldProps<T extends FieldValues> extends Omit<FormFieldProps<T>, "type"> {
  rows?: number
}

interface SelectFieldProps<T extends FieldValues> extends Omit<FormFieldProps<T>, "type" | "placeholder"> {
  options: Array<{ value: string; label: string }>
  placeholder?: string
  onValueChange?: (value: string) => void
}

export function FormField<T extends FieldValues>({
  name,
  label,
  register,
  error,
  type = "text",
  placeholder,
  required = false,
  className,
  disabled = false
}: FormFieldProps<T>) {
  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={name} className={cn(required && "after:content-['*'] after:text-red-500 after:ml-1")}>
        {label}
      </Label>
      <Input
        id={name}
        type={type}
        placeholder={placeholder}
        disabled={disabled}
        className={cn(error && "border-red-500 focus-visible:ring-red-500")}
        {...register(name, { 
          valueAsNumber: type === "number" ? true : false 
        })}
      />
      {error && (
        <p className="text-sm text-red-500 mt-1">{error.message}</p>
      )}
    </div>
  )
}

export function TextareaField<T extends FieldValues>({
  name,
  label,
  register,
  error,
  placeholder,
  required = false,
  className,
  disabled = false,
  rows = 3
}: TextareaFieldProps<T>) {
  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={name} className={cn(required && "after:content-['*'] after:text-red-500 after:ml-1")}>
        {label}
      </Label>
      <Textarea
        id={name}
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        className={cn(error && "border-red-500 focus-visible:ring-red-500")}
        {...register(name)}
      />
      {error && (
        <p className="text-sm text-red-500 mt-1">{error.message}</p>
      )}
    </div>
  )
}

export function SelectField<T extends FieldValues>({
  name,
  label,
  register,
  error,
  options,
  placeholder = "Select an option",
  required = false,
  className,
  disabled = false,
  onValueChange
}: SelectFieldProps<T>) {
  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={name} className={cn(required && "after:content-['*'] after:text-red-500 after:ml-1")}>
        {label}
      </Label>
      <Select
        disabled={disabled}
        onValueChange={onValueChange}
      >
        <SelectTrigger className={cn(error && "border-red-500 focus:ring-red-500")}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <input type="hidden" {...register(name)} />
      {error && (
        <p className="text-sm text-red-500 mt-1">{error.message}</p>
      )}
    </div>
  )
}

// Form error display component
interface FormErrorsProps {
  errors: Record<string, string>
  className?: string
}

export function FormErrors({ errors, className }: FormErrorsProps) {
  const errorEntries = Object.entries(errors)
  
  if (errorEntries.length === 0) return null

  return (
    <div className={cn("rounded-md bg-red-50 border border-red-200 p-4", className)}>
      <div className="flex">
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            Please fix the following errors:
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <ul className="list-disc pl-5 space-y-1">
              {errorEntries.map(([field, message]) => (
                <li key={field}>{message}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

// Form success message component
interface FormSuccessProps {
  message: string
  className?: string
}

export function FormSuccess({ message, className }: FormSuccessProps) {
  return (
    <div className={cn("rounded-md bg-green-50 border border-green-200 p-4", className)}>
      <div className="flex">
        <div className="ml-3">
          <p className="text-sm font-medium text-green-800">{message}</p>
        </div>
      </div>
    </div>
  )
}

// Loading form component
interface FormLoadingProps {
  message?: string
  className?: string
}

export function FormLoading({ message = "Loading...", className }: FormLoadingProps) {
  return (
    <div className={cn("rounded-md bg-blue-50 border border-blue-200 p-4", className)}>
      <div className="flex items-center">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <div className="ml-3">
          <p className="text-sm font-medium text-blue-800">{message}</p>
        </div>
      </div>
    </div>
  )
}

// Form field with validation state
interface ValidatedFieldProps<T extends FieldValues> extends FormFieldProps<T> {
  isValidating?: boolean
  validationMessage?: string
}

export function ValidatedField<T extends FieldValues>({
  isValidating,
  validationMessage,
  ...props
}: ValidatedFieldProps<T>) {
  return (
    <div className="relative">
      <FormField {...props} />
      {isValidating && (
        <div className="absolute right-2 top-8">
          <LoadingSpinner size="sm" />
        </div>
      )}
      {validationMessage && !props.error && (
        <p className="text-sm text-blue-600 mt-1">{validationMessage}</p>
      )}
    </div>
  )
}

// Form with loading state
interface LoadingFormProps {
  isLoading: boolean
  children: React.ReactNode
  className?: string
}

export function LoadingForm({ isLoading, children, className }: LoadingFormProps) {
  return (
    <div className={cn("relative", className)}>
      <div className={cn("transition-opacity", isLoading && "opacity-50 pointer-events-none")}>
        {children}
      </div>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50">
          <LoadingSpinner size="lg" />
        </div>
      )}
    </div>
  )
}
