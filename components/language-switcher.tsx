"use client"

import { useI18n } from "./i18n-provider"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Languages } from "lucide-react"

export function LanguageSwitcher() {
  const { locale, setLocale } = useI18n()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" aria-label="Change language" className="gap-2 bg-transparent">
          <Languages className="h-4 w-4" />
          <span className="hidden sm:inline">{locale === "zh" ? "中文" : "English"}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setLocale("en")}>{locale === "en" ? "✓ " : ""}English</DropdownMenuItem>
        <DropdownMenuItem onClick={() => setLocale("zh")}>{locale === "zh" ? "✓ " : ""}中文</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
