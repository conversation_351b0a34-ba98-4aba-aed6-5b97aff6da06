"use client"

import { useEffect, useMemo, useState } from "react"
import { useRouter } from "next/navigation"
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Boxes, Coins, Factory, Handshake, LayoutDashboard, Package, Search, Ship } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { useI18n } from "./i18n-provider"

export function CommandMenu() {
  const { t } = useI18n()
  const router = useRouter()
  const [open, setOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const isMac = useMemo(
    () => typeof window !== "undefined" && /Mac|iPod|iPhone|iPad/.test(window.navigator.platform),
    [],
  )

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      if ((isMac ? e.metaKey : e.ctrlKey) && e.key.toLowerCase() === "k") {
        e.preventDefault()
        setOpen((o) => !o)
      }
    }
    window.addEventListener("keydown", handler)
    return () => window.removeEventListener("keydown", handler)
  }, [isMac])

  const navItems = [
    { href: "/", label: t("nav.item.dashboard"), icon: LayoutDashboard },
    { href: "/customers", label: t("nav.item.customers"), icon: Boxes },
    { href: "/products", label: t("nav.item.products"), icon: Package },
    { href: "/sales-contracts", label: t("nav.item.sales-contracts"), icon: Handshake },
    { href: "/production", label: t("nav.item.work-orders"), icon: Factory },
    { href: "/inventory", label: t("nav.item.inventory"), icon: Package },
    { href: "/export", label: t("nav.item.export-declarations"), icon: Ship },
    { href: "/finance", label: t("nav.item.accounting"), icon: Coins },
  ]

  return (
    <>
      <Button
        variant="outline"
        className="hidden md:flex items-center gap-2 text-muted-foreground bg-transparent"
        asChild
        aria-label="Open command menu"
      >
        <Link href="#" onClick={(e) => (e.preventDefault(), setOpen(true))}>
          <Search className="h-4 w-4" />
          <span>{t("cmd.placeholder")}</span>
          <kbd className="ml-2 rounded border bg-muted px-1.5 py-0.5 text-[10px]">
            {mounted ? (isMac ? "⌘ K" : "Ctrl K") : "Ctrl K"}
          </kbd>
        </Link>
      </Button>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput placeholder={t("cmd.placeholder")} />
        <CommandList>
          <CommandEmpty>No results</CommandEmpty>
          <CommandGroup heading="Navigate">
            {navItems.map((item) => {
              const Icon = item.icon
              return (
                <CommandItem
                  key={item.href}
                  value={item.label}
                  onSelect={() => {
                    setOpen(false)
                    router.push(item.href)
                  }}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {item.label}
                </CommandItem>
              )
            })}
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  )
}
