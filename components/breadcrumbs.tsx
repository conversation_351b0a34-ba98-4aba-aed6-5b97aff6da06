"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight } from "lucide-react"
import { useI18n } from "./i18n-provider"

const pathKeyMap: Record<string, string> = {
  "/": "nav.item.dashboard",
  "/customers": "nav.item.customers",
  "/suppliers": "nav.item.suppliers",
  "/products": "nav.item.products",
  "/samples": "nav.item.samples",
  "/sales-contracts": "nav.item.sales-contracts",
  "/purchase-contracts": "nav.item.purchase-contracts",
  "/contract-templates": "nav.item.contract-templates",
  "/production": "nav.item.work-orders",
  "/quality": "nav.item.quality-control",
  "/inventory": "nav.item.inventory",
  "/shipping": "nav.item.shipping",
  "/export": "nav.item.export-declarations",
  "/trade-compliance": "nav.item.trade-compliance",
  "/plan-images": "nav.item.documentation",
  "/finance": "nav.item.accounting",
  "/reports": "nav.item.reports",
}

export function Breadcrumbs() {
  const pathname = usePathname()
  const { t } = useI18n()
  const crumbs = [{ href: "/", label: t("nav.item.dashboard") }]

  if (pathname !== "/") {
    const key = pathKeyMap[pathname] || pathname
    crumbs.push({ href: pathname, label: t(key) })
  }

  return (
    <nav aria-label="Breadcrumb" className="mb-4 text-sm text-muted-foreground">
      <ol className="flex items-center gap-1">
        {crumbs.map((c, idx) => (
          <li key={c.href} className="flex items-center">
            {idx > 0 && <ChevronRight className="mx-1 h-4 w-4" aria-hidden="true" />}
            {idx < crumbs.length - 1 ? (
              <Link className="hover:underline" href={c.href}>
                {c.label}
              </Link>
            ) : (
              <span aria-current="page" className="text-foreground">
                {c.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}
