{"extends": ["next/core-web-vitals", "eslint:recommended", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "prefer-const": "error", "no-var": "error", "no-console": ["warn", {"allow": ["warn", "error"]}]}, "env": {"browser": true, "node": true, "es2021": true, "jest": true}, "overrides": [{"files": ["**/__tests__/**/*", "**/*.test.*", "**/*.spec.*"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off", "no-console": "off"}}, {"files": ["e2e/**/*"], "rules": {"@typescript-eslint/no-explicit-any": "off", "no-console": "off"}}], "ignorePatterns": ["node_modules/", ".next/", "out/", "dist/", "coverage/", "test-results/", "*.config.js", "*.config.ts"]}