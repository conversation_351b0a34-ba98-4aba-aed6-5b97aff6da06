# Sentry Integration Setup Guide - Manufacturing ERP

## 🎯 Current Sentry Configuration Status

### ✅ Already Configured
- **Sentry Package**: `@sentry/nextjs` installed
- **Configuration Files**: Created (sentry.client.config.ts, sentry.server.config.ts, sentry.edge.config.ts)
- **Environment Variables**: Set in both local and production
- **Enhanced Error Tracking**: Added to critical API endpoints

### 🔧 Sentry Project Details
- **Organization**: `dassodev`
- **Project**: `manufacturing-erp`
- **Project ID**: `4509869772374016`
- **DSN**: `https://<EMAIL>/4509869772374016`
- **Dashboard**: https://sentry.io/organizations/dassodev/projects/manufacturing-erp/

## 🚨 Current Issue Being Tracked
**Production Database Connection Error**:
```
Error: getaddrinfo ENOTFOUND db.jwdryepnhrigricdxenc.supabase.co
```

## 📁 Sentry Configuration Files

### 1. sentry.client.config.ts
```typescript
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  integrations: [
    Sentry.replayIntegration({
      maskAllText: true,
      blockAllMedia: true,
    }),
  ],
  tracesSampleRate: 1.0,
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
  debug: false,
});
```

### 2. sentry.server.config.ts
```typescript
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  tracesSampleRate: 1.0,
  debug: false,
});
```

### 3. sentry.edge.config.ts
```typescript
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  tracesSampleRate: 1.0,
  debug: false,
});
```

## 🔍 Enhanced Error Tracking Implementation

### API Endpoints with Sentry Integration

#### /api/companies/ensure
```typescript
import * as Sentry from '@sentry/nextjs'

try {
  // Company creation logic
} catch (error) {
  Sentry.captureException(error, {
    tags: {
      endpoint: '/api/companies/ensure',
      operation: 'company_creation',
    },
    extra: {
      userEmail: session?.user?.email,
      userId: session?.user?.sub,
      errorType: error instanceof Error ? error.constructor.name : 'Unknown',
      databaseUrl: process.env.DATABASE_URL ? 'SET' : 'MISSING',
      databaseUrlPostgresql: process.env.DATABASE_URL_POSTGRESQL ? 'SET' : 'MISSING',
      nodeEnv: process.env.NODE_ENV,
      usePostgresql: process.env.USE_POSTGRESQL,
    }
  })
  return jsonError(error)
}
```

#### /api/health
```typescript
import * as Sentry from '@sentry/nextjs'

try {
  // Database health check
} catch (e) {
  Sentry.captureException(e, {
    tags: {
      endpoint: '/api/health',
      operation: 'database_health_check',
    },
    extra: {
      databaseUrl: process.env.DATABASE_URL ? 'SET (length: ' + process.env.DATABASE_URL.length + ')' : 'MISSING',
      databaseUrlPostgresql: process.env.DATABASE_URL_POSTGRESQL ? 'SET (length: ' + process.env.DATABASE_URL_POSTGRESQL.length + ')' : 'MISSING',
      nodeEnv: process.env.NODE_ENV,
      usePostgresql: process.env.USE_POSTGRESQL,
      errorType: e instanceof Error ? e.constructor.name : 'Unknown',
      errorMessage: e instanceof Error ? e.message : String(e),
    }
  })
  return jsonError(e)
}
```

## 🔧 VSCode Sentry Extension Setup

### 1. Install Sentry VSCode Extension
```bash
# Extension ID: sentry.sentry-vscode
```

### 2. Configure Sentry Extension
1. Open VSCode Command Palette (Cmd/Ctrl + Shift + P)
2. Type "Sentry: Configure"
3. Enter your organization: `dassodev`
4. Enter your project: `manufacturing-erp`
5. Enter your auth token (get from Sentry settings)

### 3. Sentry Auth Token Setup
1. Go to: https://sentry.io/settings/account/api/auth-tokens/
2. Create new token with scopes: `project:read`, `project:write`, `org:read`
3. Add to VSCode Sentry extension settings

## 🧪 Testing Sentry Integration

### Local Development Testing
```bash
# Start development server
npm run dev

# Test health endpoint (should trigger error)
curl http://localhost:3000/api/health

# Check Sentry dashboard for errors
```

### Production Testing
```bash
# Test production health endpoint
curl https://silk-road-john.vercel.app/api/health

# Visit production site to trigger company creation
open https://silk-road-john.vercel.app
```

## 📊 Monitoring Dashboard

### Key Metrics to Watch
1. **Error Rate**: Database connection failures
2. **Performance**: API response times
3. **User Impact**: Which users are affected
4. **Environment Context**: Database URLs, Node.js versions

### Sentry Dashboard Sections
- **Issues**: Real-time error tracking
- **Performance**: API endpoint monitoring
- **Releases**: Deployment tracking
- **Alerts**: Email/Slack notifications

## 🎯 Next Steps for VSCode Development

1. **Open Workspace**: Use `manufacturing-erp.code-workspace`
2. **Install Extensions**: Sentry VSCode extension included in recommendations
3. **Configure Environment**: Ensure `.env.local` has correct Sentry DSN
4. **Test Locally**: Run `npm run dev` and trigger errors
5. **Monitor Production**: Watch Sentry dashboard for real-time errors
6. **Fix Database Issue**: Update database URL configuration

## 🚀 Expected Sentry Data

Once properly connected, you should see:
- **Database connection errors** with full stack traces
- **User context** (email, session info)
- **Environment details** (database URLs, Node.js version)
- **Request context** (API endpoint, HTTP method)
- **Performance metrics** (response times, throughput)

This setup will provide comprehensive error tracking and monitoring for the Manufacturing ERP system, helping identify and resolve the current database connection issue.
