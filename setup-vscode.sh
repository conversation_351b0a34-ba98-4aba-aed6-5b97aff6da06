#!/bin/bash

# Manufacturing ERP - VSCode Workspace Setup Script
# This script sets up the complete development environment

echo "🚀 Manufacturing ERP - VSCode Workspace Setup"
echo "=============================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

echo "📁 Current directory: $(pwd)"
echo "📋 Project: Manufacturing ERP System"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "⚠️  Warning: .env.local not found. Please create it with the configuration from WORKSPACE_SETUP.md"
else
    echo "✅ Environment configuration found"
fi

# Check Sentry configuration
if grep -q "SENTRY_DSN" .env.local 2>/dev/null; then
    echo "✅ Sentry configuration found in .env.local"
else
    echo "⚠️  Warning: Sentry DSN not found in .env.local"
fi

# Create VSCode settings directory if it doesn't exist
mkdir -p .vscode

# Create VSCode settings.json
cat > .vscode/settings.json << 'EOF'
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.css": "tailwindcss",
    "*.tsx": "typescriptreact",
    "*.ts": "typescript"
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
EOF

# Create VSCode extensions.json
cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",
    "ms-vscode.vscode-css-peek",
    "sentry.sentry-vscode",
    "ms-vscode.vscode-postgres"
  ]
}
EOF

echo "✅ VSCode configuration created"

# Test database connection
echo ""
echo "🔍 Testing database connection..."
if command -v curl &> /dev/null; then
    echo "Starting development server to test database..."
    npm run dev &
    DEV_PID=$!
    
    # Wait for server to start
    sleep 5
    
    # Test health endpoint
    HEALTH_RESPONSE=$(curl -s http://localhost:3000/api/health 2>/dev/null || echo "Connection failed")
    echo "Health check response: $HEALTH_RESPONSE"
    
    # Stop development server
    kill $DEV_PID 2>/dev/null
else
    echo "⚠️  curl not found, skipping health check"
fi

echo ""
echo "📚 Documentation Files Created:"
echo "  - WORKSPACE_SETUP.md (Complete setup guide)"
echo "  - AI_MEMORY_CONTEXT.md (Full project context)"
echo "  - SENTRY_SETUP_GUIDE.md (Sentry integration guide)"
echo "  - manufacturing-erp.code-workspace (VSCode workspace file)"

echo ""
echo "🎯 Next Steps:"
echo "1. Open VSCode: code manufacturing-erp.code-workspace"
echo "2. Install recommended extensions when prompted"
echo "3. Review WORKSPACE_SETUP.md for complete configuration"
echo "4. Check SENTRY_SETUP_GUIDE.md for error tracking setup"
echo "5. Run 'npm run dev' to start development server"
echo "6. Visit http://localhost:3000 to test the application"

echo ""
echo "🚨 Current Priority: Fix Database Connection"
echo "   - Check WORKSPACE_SETUP.md for database URL configuration"
echo "   - Monitor Sentry dashboard for production errors"
echo "   - Test both local and production environments"

echo ""
echo "✅ VSCode workspace setup complete!"
echo "🔗 Production URL: https://silk-road-john.vercel.app"
echo "🔗 Sentry Dashboard: https://sentry.io/organizations/dassodev/projects/manufacturing-erp/"
