-- Quality Control Database Schema Proposal
-- This schema needs to be added to lib/schema.ts

-- Quality Inspections Table
export const qualityInspections = sqliteTable("quality_inspections", {
  id: text("id").primaryKey(),
  work_order_id: text("work_order_id").notNull().references(() => workOrders.id),
  inspection_type: text("inspection_type").notNull(), // "incoming", "in-process", "final", "outgoing"
  inspector: text("inspector").notNull(),
  scheduled_date: integer("scheduled_date", { mode: "timestamp" }),
  completed_date: integer("completed_date", { mode: "timestamp" }),
  status: text("status").default("pending"), // "pending", "in-progress", "passed", "failed"
  notes: text("notes"),
  created_at: integer("created_at", { mode: "timestamp" }).$defaultFn(() => new Date()),
  updated_at: integer("updated_at", { mode: "timestamp" }).$defaultFn(() => new Date()),
})

-- Quality Defects Table
export const qualityDefects = sqliteTable("quality_defects", {
  id: text("id").primaryKey(),
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  work_order_id: text("work_order_id").notNull().references(() => workOrders.id),
  product_id: text("product_id").notNull().references(() => products.id),
  defect_type: text("defect_type").notNull(), // "dimensional", "visual", "functional", "material"
  severity: text("severity").notNull(), // "minor", "major", "critical"
  quantity: integer("quantity").notNull(),
  description: text("description").notNull(),
  corrective_action: text("corrective_action"),
  status: text("status").default("open"), // "open", "in-progress", "resolved", "closed"
  created_at: integer("created_at", { mode: "timestamp" }).$defaultFn(() => new Date()),
  resolved_at: integer("resolved_at", { mode: "timestamp" }),
})

-- Quality Standards Table
export const qualityStandards = sqliteTable("quality_standards", {
  id: text("id").primaryKey(),
  product_id: text("product_id").notNull().references(() => products.id),
  standard_name: text("standard_name").notNull(),
  specification: text("specification").notNull(),
  tolerance: text("tolerance"),
  test_method: text("test_method"),
  acceptance_criteria: text("acceptance_criteria"),
  created_at: integer("created_at", { mode: "timestamp" }).$defaultFn(() => new Date()),
})

-- Quality Certificates Table
export const qualityCertificates = sqliteTable("quality_certificates", {
  id: text("id").primaryKey(),
  inspection_id: text("inspection_id").notNull().references(() => qualityInspections.id),
  certificate_number: text("certificate_number").notNull().unique(),
  certificate_type: text("certificate_type").notNull(), // "COA", "COC", "test_report"
  issued_date: integer("issued_date", { mode: "timestamp" }),
  valid_until: integer("valid_until", { mode: "timestamp" }),
  file_path: text("file_path"),
  created_at: integer("created_at", { mode: "timestamp" }).$defaultFn(() => new Date()),
})

-- Inspection Results Table
export const inspectionResults = sqliteTable("inspection_results", {
  id: text("id").primaryKey(),
  inspection_id: text("inspection_id").notNull().references(() => qualityInspections.id),
  standard_id: text("standard_id").notNull().references(() => qualityStandards.id),
  measured_value: text("measured_value"),
  result: text("result").notNull(), // "pass", "fail", "conditional"
  notes: text("notes"),
  created_at: integer("created_at", { mode: "timestamp" }).$defaultFn(() => new Date()),
})

-- Relations
export const qualityInspectionsRelations = relations(qualityInspections, ({ one, many }) => ({
  workOrder: one(workOrders, {
    fields: [qualityInspections.work_order_id],
    references: [workOrders.id],
  }),
  defects: many(qualityDefects),
  certificates: many(qualityCertificates),
  results: many(inspectionResults),
}))

export const qualityDefectsRelations = relations(qualityDefects, ({ one }) => ({
  inspection: one(qualityInspections, {
    fields: [qualityDefects.inspection_id],
    references: [qualityInspections.id],
  }),
  workOrder: one(workOrders, {
    fields: [qualityDefects.work_order_id],
    references: [workOrders.id],
  }),
  product: one(products, {
    fields: [qualityDefects.product_id],
    references: [products.id],
  }),
}))

export const qualityStandardsRelations = relations(qualityStandards, ({ one, many }) => ({
  product: one(products, {
    fields: [qualityStandards.product_id],
    references: [products.id],
  }),
  results: many(inspectionResults),
}))

export const qualityCertificatesRelations = relations(qualityCertificates, ({ one }) => ({
  inspection: one(qualityInspections, {
    fields: [qualityCertificates.inspection_id],
    references: [qualityInspections.id],
  }),
}))

export const inspectionResultsRelations = relations(inspectionResults, ({ one }) => ({
  inspection: one(qualityInspections, {
    fields: [inspectionResults.inspection_id],
    references: [qualityInspections.id],
  }),
  standard: one(qualityStandards, {
    fields: [inspectionResults.standard_id],
    references: [qualityStandards.id],
  }),
}))
