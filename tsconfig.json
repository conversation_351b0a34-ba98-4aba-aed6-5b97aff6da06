{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "typeRoots": ["./node_modules/@types", "./types"], "skipDefaultLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "types": []}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "__tests__/**/*.ts", "__tests__/**/*.tsx", "e2e/**/*.ts", "jest.setup.js", "jest.config.js", "playwright.config.ts"], "exclude": ["node_modules", ".next", "out", "dist", "coverage", "test-results"]}