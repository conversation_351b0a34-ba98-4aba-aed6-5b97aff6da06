/>

- In components/i18n-provider.tsx, add the following keys to both dictionaries:

  In the en object, add:
  const en: Dict = {
    ...
    "header.status": "Status",
    "header.items": "Items",
    "header.operations": "Operations",
    "header.type": "Type",
    "header.time": "Time",
  }

  In the zh object, add:
  const zh: Dict = {
    ...
    "header.status": "状态",
    "header.items": "明细数",
    "header.operations": "工序",
    "header.type": "类型",
    "header.time": "时间",
  }

- In app/contracts/page.tsx, localize the Sales table headers:
  Replace:
    <TableHead>Status</TableHead>
    <TableHead>Items</TableHead>
  With:
    <TableHead>{t("header.status")}</TableHead>
    <TableHead>{t("header.items")}</TableHead>

  And localize the Purchase table headers:
  Replace:
    <TableHead>Status</TableHead>
    <TableHead>Items</TableHead>
  With:
    <TableHead>{t("header.status")}</TableHead>
    <TableHead>{t("header.items")}</TableHead>

- In app/export/page.tsx, localize the table headers:
  Replace:
    <TableHead>Status</TableHead>
    <TableHead>Items</TableHead>
  With:
    <TableHead>{t("header.status")}</TableHead>
    <TableHead>{t("header.items")}</TableHead>

- In app/production/page.tsx, localize the table header:
  Replace:
    <TableHead>Operations</TableHead>
  With:
    <TableHead>{t("header.operations")}</TableHead>

- In app/inventory/page.tsx, localize the Transactions table headers:
  Replace:
    <TableHead>Type</TableHead>
    <TableHead>Time</TableHead>
  With:
    <TableHead>{t("header.type")}</TableHead>
    <TableHead>{t("header.time")}</TableHead>

</CodeProject>

All targeted headers now switch to Chinese when you choose 中文 in the language switcher.

<Actions>
  <Action name="Translate status values" description="Map data values like draft/approved/in-progress to zh labels" />
  <Action name="Localize remaining labels" description="Translate 'Lot', 'WO' and any other static strings" />
  <Action name="Add pluralization/interpolation" description="Support t('module.export.declarationsCount', { n }) formatting" />
  <Action name="Persist locale in URL" description="Enable /zh and /en routes with Next.js i18n routing" />
</Actions>
