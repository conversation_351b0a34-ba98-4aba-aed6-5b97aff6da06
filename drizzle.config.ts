import "dotenv/config";
import type { Config } from "drizzle-kit";

/**
 * Manufacturing ERP - PostgreSQL Drizzle Configuration
 *
 * Clean PostgreSQL-only configuration for the Manufacturing ERP system
 * Migrated from SQLite to PostgreSQL for enterprise-grade performance
 */

// PostgreSQL connection URL with fallback options
const postgresUrl = process.env.DATABASE_URL_POSTGRESQL ||
                   process.env.POSTGRES_LOCAL_URL ||
                   process.env.DATABASE_URL ||
                   'postgresql://erp_user:SecureERP2024!@localhost:5432/manufacturing_erp';

if (!postgresUrl) {
  throw new Error("PostgreSQL DATABASE_URL is missing from environment variables");
}

console.log('🐘 Drizzle Config: Using PostgreSQL database');

export default {
  schema: "./lib/schema-postgres.ts",
  out: "./drizzle",
  dialect: "postgresql",
  dbCredentials: {
    url: postgresUrl,
  },
  verbose: true,
  strict: true,
  casing: "snake_case",
} satisfies Config;
