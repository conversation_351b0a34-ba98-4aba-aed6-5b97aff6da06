CREATE TABLE `ap_invoices` (
	`id` text PRIMARY KEY NOT NULL,
	`number` text NOT NULL,
	`supplier_id` text NOT NULL,
	`date` text NOT NULL,
	`amount` text NOT NULL,
	`status` text DEFAULT 'pending',
	`created_at` integer,
	FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `ap_invoices_number_unique` ON `ap_invoices` (`number`);--> statement-breakpoint
CREATE TABLE `ar_invoices` (
	`id` text PRIMARY KEY NOT NULL,
	`number` text NOT NULL,
	`customer_id` text NOT NULL,
	`date` text NOT NULL,
	`amount` text NOT NULL,
	`status` text DEFAULT 'pending',
	`created_at` integer,
	FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `ar_invoices_number_unique` ON `ar_invoices` (`number`);--> statement-breakpoint
CREATE TABLE `customers` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`contact_name` text,
	`contact_phone` text,
	`contact_email` text,
	`address` text,
	`tax_id` text,
	`bank` text,
	`incoterm` text,
	`payment_term` text,
	`status` text DEFAULT 'active',
	`created_at` integer
);
--> statement-breakpoint
CREATE TABLE `declaration_items` (
	`id` text PRIMARY KEY NOT NULL,
	`declaration_id` text NOT NULL,
	`product_id` text NOT NULL,
	`qty` text NOT NULL,
	`created_at` integer,
	FOREIGN KEY (`declaration_id`) REFERENCES `declarations`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `declarations` (
	`id` text PRIMARY KEY NOT NULL,
	`number` text NOT NULL,
	`status` text DEFAULT 'draft',
	`created_at` integer
);
--> statement-breakpoint
CREATE UNIQUE INDEX `declarations_number_unique` ON `declarations` (`number`);--> statement-breakpoint
CREATE TABLE `products` (
	`id` text PRIMARY KEY NOT NULL,
	`sku` text NOT NULL,
	`name` text NOT NULL,
	`unit` text NOT NULL,
	`hs_code` text,
	`origin` text,
	`package` text,
	`image` text,
	`created_at` integer
);
--> statement-breakpoint
CREATE UNIQUE INDEX `products_sku_unique` ON `products` (`sku`);--> statement-breakpoint
CREATE TABLE `purchase_contract_items` (
	`id` text PRIMARY KEY NOT NULL,
	`contract_id` text NOT NULL,
	`product_id` text NOT NULL,
	`qty` text NOT NULL,
	`price` text NOT NULL,
	`created_at` integer,
	FOREIGN KEY (`contract_id`) REFERENCES `purchase_contracts`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `purchase_contracts` (
	`id` text PRIMARY KEY NOT NULL,
	`number` text NOT NULL,
	`supplier_id` text NOT NULL,
	`date` text NOT NULL,
	`status` text DEFAULT 'draft',
	`created_at` integer,
	FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `purchase_contracts_number_unique` ON `purchase_contracts` (`number`);--> statement-breakpoint
CREATE TABLE `sales_contract_items` (
	`id` text PRIMARY KEY NOT NULL,
	`contract_id` text NOT NULL,
	`product_id` text NOT NULL,
	`qty` text NOT NULL,
	`price` text NOT NULL,
	`created_at` integer,
	FOREIGN KEY (`contract_id`) REFERENCES `sales_contracts`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `sales_contracts` (
	`id` text PRIMARY KEY NOT NULL,
	`number` text NOT NULL,
	`customer_id` text NOT NULL,
	`date` text NOT NULL,
	`status` text DEFAULT 'draft',
	`created_at` integer,
	FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `sales_contracts_number_unique` ON `sales_contracts` (`number`);--> statement-breakpoint
CREATE TABLE `samples` (
	`id` text PRIMARY KEY NOT NULL,
	`code` text NOT NULL,
	`name` text NOT NULL,
	`date` text NOT NULL,
	`customer_id` text,
	`created_at` integer,
	FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `samples_code_unique` ON `samples` (`code`);--> statement-breakpoint
CREATE TABLE `stock_lots` (
	`id` text PRIMARY KEY NOT NULL,
	`product_id` text NOT NULL,
	`qty` text NOT NULL,
	`location` text NOT NULL,
	`note` text,
	`created_at` integer,
	FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `stock_txns` (
	`id` text PRIMARY KEY NOT NULL,
	`type` text NOT NULL,
	`product_id` text NOT NULL,
	`qty` text NOT NULL,
	`location` text NOT NULL,
	`ref` text,
	`created_at` integer,
	FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `suppliers` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`contact_name` text,
	`contact_phone` text,
	`contact_email` text,
	`address` text,
	`tax_id` text,
	`bank` text,
	`status` text DEFAULT 'active',
	`created_at` integer
);
--> statement-breakpoint
CREATE TABLE `work_orders` (
	`id` text PRIMARY KEY NOT NULL,
	`number` text NOT NULL,
	`sales_contract_id` text,
	`product_id` text NOT NULL,
	`qty` text NOT NULL,
	`status` text DEFAULT 'pending',
	`created_at` integer,
	FOREIGN KEY (`sales_contract_id`) REFERENCES `sales_contracts`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `work_orders_number_unique` ON `work_orders` (`number`);