CREATE TABLE `company_profiles` (
	`id` text PRIMARY KEY NOT NULL,
	`auth0_user_id` text NOT NULL,
	`company_name` text NOT NULL,
	`company_address` text,
	`company_city` text,
	`company_state` text,
	`company_country` text,
	`company_postal_code` text,
	`tax_id` text,
	`registration_number` text,
	`business_type` text,
	`contact_person` text NOT NULL,
	`contact_email` text NOT NULL,
	`contact_phone` text,
	`website` text,
	`bank_name` text,
	`bank_account` text,
	`bank_swift` text,
	`default_currency` text DEFAULT 'USD',
	`default_incoterm` text,
	`default_payment_terms` text,
	`logo_url` text,
	`is_active` integer DEFAULT true,
	`onboarding_completed` integer DEFAULT false,
	`created_at` integer,
	`updated_at` integer
);
--> statement-breakpoint
CREATE UNIQUE INDEX `company_profiles_auth0_user_id_unique` ON `company_profiles` (`auth0_user_id`);--> statement-breakpoint
CREATE TABLE `contract_templates` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`type` text NOT NULL,
	`content` text NOT NULL,
	`currency` text,
	`payment_terms` text,
	`delivery_terms` text,
	`language` text DEFAULT 'en',
	`version` integer DEFAULT 1,
	`is_active` integer DEFAULT true,
	`created_at` integer,
	`updated_at` integer
);
--> statement-breakpoint
ALTER TABLE `purchase_contracts` ADD `template_id` text REFERENCES contract_templates(id);--> statement-breakpoint
ALTER TABLE `purchase_contracts` ADD `currency` text;--> statement-breakpoint
ALTER TABLE `sales_contracts` ADD `template_id` text REFERENCES contract_templates(id);--> statement-breakpoint
ALTER TABLE `sales_contracts` ADD `currency` text;