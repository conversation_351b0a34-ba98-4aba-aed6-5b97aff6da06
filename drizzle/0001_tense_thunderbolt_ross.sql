ALTER TABLE "companies" ADD COLUMN "bank_address" text;--> statement-breakpoint
ALTER TABLE "companies" ADD COLUMN "export_license" text;--> statement-breakpoint
ALTER TABLE "companies" ADD COLUMN "customs_code" text;--> statement-breakpoint
ALTER TABLE "companies" ADD COLUMN "preferred_incoterms" text DEFAULT 'FOB';--> statement-breakpoint
ALTER TABLE "companies" ADD COLUMN "preferred_payment_terms" text DEFAULT '30 days';--> statement-breakpoint
ALTER TABLE "companies" ADD COLUMN "onboarding_completed" text DEFAULT 'false';--> statement-breakpoint
ALTER TABLE "companies" ADD COLUMN "onboarding_step" text DEFAULT 'basic_info';--> statement-breakpoint
ALTER TABLE "contract_templates" ADD COLUMN "currency" text;--> statement-breakpoint
ALTER TABLE "contract_templates" ADD COLUMN "payment_terms" text;--> statement-breakpoint
ALTER TABLE "contract_templates" ADD COLUMN "delivery_terms" text;--> statement-breakpoint
ALTER TABLE "contract_templates" ADD COLUMN "language" text DEFAULT 'en';--> statement-breakpoint
ALTER TABLE "contract_templates" ADD COLUMN "version" text DEFAULT '1';--> statement-breakpoint
ALTER TABLE "contract_templates" ADD COLUMN "is_active" text DEFAULT 'true';--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "hs_code" text;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "origin" text;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "package" text;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "image" text;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "inspection_required" text DEFAULT 'false';--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "quality_tolerance" text;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "quality_notes" text;--> statement-breakpoint
ALTER TABLE "purchase_contracts" ADD COLUMN "currency" text;--> statement-breakpoint
ALTER TABLE "sales_contracts" ADD COLUMN "currency" text;