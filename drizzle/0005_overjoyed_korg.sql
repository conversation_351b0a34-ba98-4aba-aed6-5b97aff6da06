CREATE TABLE `companies` (
	`id` text PRIMARY KEY NOT NULL,
	`auth0_user_id` text NOT NULL,
	`name` text NOT NULL,
	`legal_name` text,
	`registration_number` text,
	`tax_id` text,
	`vat_number` text,
	`email` text NOT NULL,
	`phone` text,
	`website` text,
	`address_line1` text,
	`address_line2` text,
	`city` text,
	`state_province` text,
	`postal_code` text,
	`country` text,
	`industry` text,
	`business_type` text,
	`employee_count` text,
	`annual_revenue` text,
	`bank_name` text,
	`bank_account` text,
	`bank_swift` text,
	`bank_address` text,
	`export_license` text,
	`customs_code` text,
	`preferred_incoterms` text DEFAULT 'FOB',
	`preferred_payment_terms` text DEFAULT '30 days',
	`status` text DEFAULT 'active',
	`onboarding_completed` text DEFAULT 'false',
	`onboarding_step` text DEFAULT 'basic_info',
	`created_at` integer,
	`updated_at` integer
);
--> statement-breakpoint
CREATE UNIQUE INDEX `companies_auth0_user_id_unique` ON `companies` (`auth0_user_id`);--> statement-breakpoint
CREATE INDEX `companies_auth0_user_id_idx` ON `companies` (`auth0_user_id`);--> statement-breakpoint
CREATE INDEX `companies_name_idx` ON `companies` (`name`);--> statement-breakpoint
DROP TABLE `company_profiles`;