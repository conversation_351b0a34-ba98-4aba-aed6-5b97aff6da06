ALTER TABLE `products` ADD `inspection_required` text DEFAULT 'false';--> statement-breakpoint
ALTER TABLE `products` ADD `quality_tolerance` text;--> statement-breakpoint
ALTER TABLE `products` ADD `quality_notes` text;--> statement-breakpoint
ALTER TABLE `stock_lots` ADD `quality_status` text DEFAULT 'pending';--> statement-breakpoint
ALTER TABLE `stock_lots` ADD `inspection_id` text REFERENCES quality_inspections(id);--> statement-breakpoint
ALTER TABLE `stock_lots` ADD `quality_notes` text;--> statement-breakpoint
ALTER TABLE `work_orders` ADD `quality_required` text DEFAULT 'auto';--> statement-breakpoint
ALTER TABLE `work_orders` ADD `quality_status` text DEFAULT 'pending';