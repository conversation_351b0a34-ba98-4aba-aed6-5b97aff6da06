CREATE TABLE `inspection_results` (
	`id` text PRIMARY KEY NOT NULL,
	`inspection_id` text NOT NULL,
	`standard_id` text,
	`measured_value` text,
	`result` text NOT NULL,
	`notes` text,
	`created_at` integer,
	FOREIGN KEY (`inspection_id`) REFERENCES `quality_inspections`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`standard_id`) REFERENCES `quality_standards`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `quality_certificates` (
	`id` text PRIMARY KEY NOT NULL,
	`inspection_id` text,
	`certificate_number` text NOT NULL,
	`certificate_type` text NOT NULL,
	`issued_date` text NOT NULL,
	`valid_until` text,
	`file_path` text,
	`created_at` integer,
	FOREIGN KEY (`inspection_id`) REFERENCES `quality_inspections`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `quality_certificates_certificate_number_unique` ON `quality_certificates` (`certificate_number`);--> statement-breakpoint
CREATE TABLE `quality_defects` (
	`id` text PRIMARY KEY NOT NULL,
	`inspection_id` text,
	`work_order_id` text,
	`product_id` text,
	`defect_type` text NOT NULL,
	`severity` text NOT NULL,
	`quantity` text NOT NULL,
	`description` text NOT NULL,
	`corrective_action` text,
	`status` text DEFAULT 'open',
	`created_at` integer,
	FOREIGN KEY (`inspection_id`) REFERENCES `quality_inspections`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`work_order_id`) REFERENCES `work_orders`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `quality_inspections` (
	`id` text PRIMARY KEY NOT NULL,
	`work_order_id` text,
	`inspection_type` text NOT NULL,
	`inspector` text NOT NULL,
	`scheduled_date` text,
	`completed_date` text,
	`status` text DEFAULT 'scheduled',
	`notes` text,
	`created_at` integer,
	FOREIGN KEY (`work_order_id`) REFERENCES `work_orders`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `quality_standards` (
	`id` text PRIMARY KEY NOT NULL,
	`product_id` text,
	`standard_name` text NOT NULL,
	`specification` text NOT NULL,
	`tolerance` text,
	`test_method` text,
	`acceptance_criteria` text NOT NULL,
	`created_at` integer,
	FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON UPDATE no action ON DELETE no action
);
