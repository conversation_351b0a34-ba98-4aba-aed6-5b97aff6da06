ALTER TABLE `ap_invoices` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `ap_invoices_company_id_idx` ON `ap_invoices` (`company_id`);--> statement-breakpoint
ALTER TABLE `ar_invoices` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `ar_invoices_company_id_idx` ON `ar_invoices` (`company_id`);--> statement-breakpoint
ALTER TABLE `contract_templates` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `contract_templates_company_id_idx` ON `contract_templates` (`company_id`);--> statement-breakpoint
ALTER TABLE `customers` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `customers_company_id_idx` ON `customers` (`company_id`);--> statement-breakpoint
ALTER TABLE `declarations` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `declarations_company_id_idx` ON `declarations` (`company_id`);--> statement-breakpoint
ALTER TABLE `products` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `products_company_id_idx` ON `products` (`company_id`);--> statement-breakpoint
CREATE INDEX `products_sku_company_idx` ON `products` (`sku`,`company_id`);--> statement-breakpoint
ALTER TABLE `purchase_contracts` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `purchase_contracts_company_id_idx` ON `purchase_contracts` (`company_id`);--> statement-breakpoint
ALTER TABLE `quality_certificates` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `quality_certificates_company_id_idx` ON `quality_certificates` (`company_id`);--> statement-breakpoint
ALTER TABLE `quality_defects` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `quality_defects_company_id_idx` ON `quality_defects` (`company_id`);--> statement-breakpoint
ALTER TABLE `quality_inspections` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `quality_inspections_company_id_idx` ON `quality_inspections` (`company_id`);--> statement-breakpoint
ALTER TABLE `quality_standards` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `quality_standards_company_id_idx` ON `quality_standards` (`company_id`);--> statement-breakpoint
ALTER TABLE `sales_contracts` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `sales_contracts_company_id_idx` ON `sales_contracts` (`company_id`);--> statement-breakpoint
ALTER TABLE `samples` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `samples_company_id_idx` ON `samples` (`company_id`);--> statement-breakpoint
ALTER TABLE `stock_lots` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `stock_lots_company_id_idx` ON `stock_lots` (`company_id`);--> statement-breakpoint
ALTER TABLE `stock_txns` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `stock_txns_company_id_idx` ON `stock_txns` (`company_id`);--> statement-breakpoint
ALTER TABLE `suppliers` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `suppliers_company_id_idx` ON `suppliers` (`company_id`);--> statement-breakpoint
ALTER TABLE `work_orders` ADD `company_id` text NOT NULL REFERENCES companies(id);--> statement-breakpoint
CREATE INDEX `work_orders_company_id_idx` ON `work_orders` (`company_id`);