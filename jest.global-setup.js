// Jest global setup

module.exports = async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.DATABASE_URL = 'file:./test.db'
  process.env.NEXTAUTH_SECRET = 'test-secret'
  process.env.NEXTAUTH_URL = 'http://localhost:3000'
  
  // Initialize test database
  console.log('Setting up test environment...')
  
  // You can add database setup here if needed
  // For example, running migrations or seeding test data
  
  console.log('Test environment setup complete')
}
