// API integration tests for customers endpoint

import { createMocks } from 'node-mocks-http'
import { GET, POST } from '@/app/api/customers/route'
import { NextRequest } from 'next/server'

// Mock dependencies
jest.mock('@/lib/db', () => ({
  db: {
    query: {
      customers: {
        findMany: jest.fn(),
        findFirst: jest.fn(),
      },
    },
    insert: jest.fn(),
  },
  uid: jest.fn(() => 'cust_test_id'),
}))

jest.mock('@/lib/auth', () => ({
  getCurrentUser: jest.fn(() => ({
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'admin',
  })),
  withAuth: jest.fn((handler, permission) => {
    return async (user, request) => {
      return handler(user, request)
    }
  }),
}))

jest.mock('@/lib/services/customer', () => ({
  CustomerService: jest.fn().mockImplementation(() => ({
    findAll: jest.fn(),
    create: jest.fn(),
  })),
}))

jest.mock('@/lib/services/base', () => ({
  ServiceFactory: jest.fn().mockImplementation(() => ({
    createService: jest.fn().mockImplementation((ServiceClass) => new ServiceClass()),
  })),
}))

describe('/api/customers', () => {
  let mockCustomerService: any

  beforeEach(() => {
    const { CustomerService } = require('@/lib/services/customer')
    mockCustomerService = new CustomerService()
    
    const { ServiceFactory } = require('@/lib/services/base')
    const mockServiceFactory = new ServiceFactory()
    mockServiceFactory.createService.mockReturnValue(mockCustomerService)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/customers', () => {
    it('should return all customers', async () => {
      const mockCustomers = [
        {
          id: 'cust-1',
          name: 'Customer 1',
          contact_email: '<EMAIL>',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 'cust-2',
          name: 'Customer 2',
          contact_email: '<EMAIL>',
          status: 'active',
          created_at: '2024-01-02T00:00:00Z',
          updated_at: '2024-01-02T00:00:00Z',
        },
      ]

      mockCustomerService.findAll.mockResolvedValue(mockCustomers)

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'GET',
        headers: {
          'x-forwarded-for': '127.0.0.1',
          'user-agent': 'test-agent',
        },
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(2)
      expect(data.data[0].name).toBe('Customer 1')
      expect(mockCustomerService.findAll).toHaveBeenCalled()
    })

    it('should handle service errors', async () => {
      mockCustomerService.findAll.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'GET',
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBeDefined()
    })
  })

  describe('POST /api/customers', () => {
    const validCustomerData = {
      name: 'New Customer',
      contact_name: 'John Doe',
      contact_email: '<EMAIL>',
      contact_phone: '+1234567890',
      address: '123 Test St',
      status: 'active',
    }

    it('should create a new customer', async () => {
      const createdCustomer = {
        id: 'cust_test_id',
        ...validCustomerData,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      }

      mockCustomerService.create.mockResolvedValue(createdCustomer)

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1',
          'user-agent': 'test-agent',
        },
        body: JSON.stringify(validCustomerData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.id).toBe('cust_test_id')
      expect(data.data.name).toBe('New Customer')
      expect(data.message).toBe('Customer created successfully')
      expect(mockCustomerService.create).toHaveBeenCalledWith(
        validCustomerData,
        expect.objectContaining({
          id: 'user-1',
          name: 'Test User',
        })
      )
    })

    it('should handle validation errors', async () => {
      const invalidData = {
        // Missing required name field
        contact_email: 'invalid-email',
      }

      const validationError = new Error('Validation failed')
      validationError.name = 'BusinessLogicError'
      mockCustomerService.create.mockRejectedValue(validationError)

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBeDefined()
    })

    it('should handle duplicate customer errors', async () => {
      const duplicateError = new Error('Customer already exists')
      duplicateError.name = 'BusinessLogicError'
      mockCustomerService.create.mockRejectedValue(duplicateError)

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validCustomerData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Customer already exists')
    })

    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: 'invalid json',
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBeDefined()
    })

    it('should handle missing request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
    })

    it('should handle service errors during creation', async () => {
      mockCustomerService.create.mockRejectedValue(new Error('Database connection failed'))

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validCustomerData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBeDefined()
    })
  })

  describe('Authentication and Authorization', () => {
    it('should require authentication for POST requests', async () => {
      // Mock auth to return null (no user)
      const { getCurrentUser } = require('@/lib/auth')
      getCurrentUser.mockReturnValue(null)

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validCustomerData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toContain('authentication')
    })

    it('should check permissions for write operations', async () => {
      // Mock auth to return user without write permissions
      const { getCurrentUser } = require('@/lib/auth')
      getCurrentUser.mockReturnValue({
        id: 'user-1',
        name: 'Read Only User',
        email: '<EMAIL>',
        role: 'viewer',
      })

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validCustomerData),
      })

      // This would depend on how withAuth is implemented
      // For now, we'll assume it passes through
      const response = await POST(request)
      
      // The actual behavior would depend on the permission system
      expect(response.status).toBeGreaterThanOrEqual(200)
    })
  })

  describe('Request Headers and Context', () => {
    it('should pass IP address and user agent to service', async () => {
      const createdCustomer = {
        id: 'cust_test_id',
        ...validCustomerData,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      }

      mockCustomerService.create.mockResolvedValue(createdCustomer)

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '*************',
          'user-agent': 'Mozilla/5.0 Test Browser',
        },
        body: JSON.stringify(validCustomerData),
      })

      await POST(request)

      // Verify that ServiceFactory was called with correct context
      const { ServiceFactory } = require('@/lib/services/base')
      expect(ServiceFactory).toHaveBeenCalledWith(
        expect.objectContaining({
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 Test Browser',
        })
      )
    })

    it('should handle missing headers gracefully', async () => {
      const createdCustomer = {
        id: 'cust_test_id',
        ...validCustomerData,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      }

      mockCustomerService.create.mockResolvedValue(createdCustomer)

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validCustomerData),
      })

      const response = await POST(request)

      expect(response.status).toBe(201)

      // Verify that ServiceFactory was called with default values
      const { ServiceFactory } = require('@/lib/services/base')
      expect(ServiceFactory).toHaveBeenCalledWith(
        expect.objectContaining({
          ipAddress: 'unknown',
          userAgent: 'unknown',
        })
      )
    })
  })
})
