// Unit tests for CustomerService

import { CustomerService, CreateCustomerData, UpdateCustomerData } from '@/lib/services/customer'
import { ServiceContext } from '@/lib/services/base'
import { BusinessLogicError, ErrorCode } from '@/lib/errors'

// Mock dependencies
jest.mock('@/lib/db', () => ({
  db: {
    query: {
      customers: {
        findMany: jest.fn(),
        findFirst: jest.fn(),
      },
      salesContracts: {
        findFirst: jest.fn(),
      },
      arInvoices: {
        findFirst: jest.fn(),
      },
    },
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  uid: jest.fn(() => 'cust_test_id'),
}))

jest.mock('@/lib/audit', () => ({
  logSuccess: jest.fn(),
  logFailure: jest.fn(),
  AuditResource: {
    CUSTOMER: 'customer',
  },
  AuditAction: {
    CREATE: 'create',
    UPDATE: 'update',
    DELETE: 'delete',
    VIEW: 'view',
  },
}))

jest.mock('@/lib/sanitization', () => ({
  sanitizeFormData: jest.fn((data) => data),
}))

describe('CustomerService', () => {
  let customerService: CustomerService
  let mockContext: ServiceContext
  let mockUser: any

  beforeEach(() => {
    mockUser = {
      id: 'user-1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'admin',
    }

    mockContext = {
      db: require('@/lib/db').db,
      user: mockUser,
      ipAddress: '127.0.0.1',
      userAgent: 'test-agent',
    }

    customerService = new CustomerService(mockContext)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('create', () => {
    const validCustomerData: CreateCustomerData = {
      name: 'Test Customer',
      contact_name: 'John Doe',
      contact_email: '<EMAIL>',
      contact_phone: '+**********',
      address: '123 Test St',
      status: 'active',
    }

    it('should create a customer successfully', async () => {
      // Mock database responses
      mockContext.db.query.customers.findFirst.mockResolvedValue(null) // No duplicate
      mockContext.db.insert.mockResolvedValue(undefined)
      mockContext.db.query.customers.findFirst
        .mockResolvedValueOnce(null) // For duplicate check
        .mockResolvedValueOnce({ // For findById after creation
          id: 'cust_test_id',
          ...validCustomerData,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        })

      const result = await customerService.create(validCustomerData, mockUser)

      expect(result).toEqual({
        id: 'cust_test_id',
        name: 'Test Customer',
        contact_name: 'John Doe',
        contact_email: '<EMAIL>',
        contact_phone: '+**********',
        address: '123 Test St',
        tax_id: undefined,
        bank: undefined,
        incoterm: undefined,
        payment_term: undefined,
        status: 'active',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      })

      expect(mockContext.db.insert).toHaveBeenCalledWith(
        expect.anything() // customers table
      )
    })

    it('should throw error for duplicate customer name', async () => {
      // Mock existing customer with same name
      mockContext.db.query.customers.findFirst.mockResolvedValue({
        id: 'existing-id',
        name: 'Test Customer',
      })

      await expect(
        customerService.create(validCustomerData, mockUser)
      ).rejects.toThrow(BusinessLogicError)

      expect(mockContext.db.insert).not.toHaveBeenCalled()
    })

    it('should throw error for invalid email format', async () => {
      const invalidData = {
        ...validCustomerData,
        contact_email: 'invalid-email',
      }

      mockContext.db.query.customers.findFirst.mockResolvedValue(null)

      await expect(
        customerService.create(invalidData, mockUser)
      ).rejects.toThrow(BusinessLogicError)
    })

    it('should handle missing optional fields', async () => {
      const minimalData: CreateCustomerData = {
        name: 'Minimal Customer',
      }

      mockContext.db.query.customers.findFirst.mockResolvedValue(null)
      mockContext.db.insert.mockResolvedValue(undefined)
      mockContext.db.query.customers.findFirst
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce({
          id: 'cust_test_id',
          name: 'Minimal Customer',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        })

      const result = await customerService.create(minimalData, mockUser)

      expect(result.name).toBe('Minimal Customer')
      expect(result.status).toBe('active')
    })
  })

  describe('update', () => {
    const updateData: UpdateCustomerData = {
      name: 'Updated Customer',
      contact_email: '<EMAIL>',
    }

    it('should update customer successfully', async () => {
      const existingCustomer = {
        id: 'cust-1',
        name: 'Original Customer',
        contact_email: '<EMAIL>',
        status: 'active',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      }

      const updatedCustomer = {
        ...existingCustomer,
        ...updateData,
        updated_at: '2024-01-02T00:00:00Z',
      }

      // Mock findById calls
      mockContext.db.query.customers.findFirst
        .mockResolvedValueOnce(existingCustomer) // For existence check
        .mockResolvedValueOnce(null) // For duplicate name check
        .mockResolvedValueOnce(updatedCustomer) // For final result

      mockContext.db.update.mockResolvedValue(undefined)

      const result = await customerService.update('cust-1', updateData, mockUser)

      expect(result.name).toBe('Updated Customer')
      expect(result.contact_email).toBe('<EMAIL>')
      expect(mockContext.db.update).toHaveBeenCalled()
    })

    it('should throw error when customer not found', async () => {
      mockContext.db.query.customers.findFirst.mockResolvedValue(null)

      await expect(
        customerService.update('non-existent', updateData, mockUser)
      ).rejects.toThrow(BusinessLogicError)

      expect(mockContext.db.update).not.toHaveBeenCalled()
    })

    it('should throw error for duplicate name on update', async () => {
      const existingCustomer = { id: 'cust-1', name: 'Original' }
      const duplicateCustomer = { id: 'cust-2', name: 'Updated Customer' }

      mockContext.db.query.customers.findFirst
        .mockResolvedValueOnce(existingCustomer) // For existence check
        .mockResolvedValueOnce(duplicateCustomer) // For duplicate check

      await expect(
        customerService.update('cust-1', updateData, mockUser)
      ).rejects.toThrow(BusinessLogicError)
    })
  })

  describe('delete', () => {
    it('should delete customer successfully', async () => {
      const existingCustomer = {
        id: 'cust-1',
        name: 'Test Customer',
        status: 'active',
      }

      mockContext.db.query.customers.findFirst.mockResolvedValue(existingCustomer)
      mockContext.db.query.salesContracts.findFirst.mockResolvedValue(null)
      mockContext.db.query.arInvoices.findFirst.mockResolvedValue(null)
      mockContext.db.delete.mockResolvedValue(undefined)

      await customerService.delete('cust-1', mockUser)

      expect(mockContext.db.delete).toHaveBeenCalled()
    })

    it('should throw error when customer has sales contracts', async () => {
      const existingCustomer = { id: 'cust-1', name: 'Test Customer' }
      const existingContract = { id: 'contract-1', customerId: 'cust-1' }

      mockContext.db.query.customers.findFirst.mockResolvedValue(existingCustomer)
      mockContext.db.query.salesContracts.findFirst.mockResolvedValue(existingContract)

      await expect(
        customerService.delete('cust-1', mockUser)
      ).rejects.toThrow(BusinessLogicError)

      expect(mockContext.db.delete).not.toHaveBeenCalled()
    })

    it('should throw error when customer has invoices', async () => {
      const existingCustomer = { id: 'cust-1', name: 'Test Customer' }
      const existingInvoice = { id: 'invoice-1', customerId: 'cust-1' }

      mockContext.db.query.customers.findFirst.mockResolvedValue(existingCustomer)
      mockContext.db.query.salesContracts.findFirst.mockResolvedValue(null)
      mockContext.db.query.arInvoices.findFirst.mockResolvedValue(existingInvoice)

      await expect(
        customerService.delete('cust-1', mockUser)
      ).rejects.toThrow(BusinessLogicError)

      expect(mockContext.db.delete).not.toHaveBeenCalled()
    })
  })

  describe('findAll', () => {
    it('should return all customers', async () => {
      const mockCustomers = [
        {
          id: 'cust-1',
          name: 'Customer 1',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 'cust-2',
          name: 'Customer 2',
          status: 'inactive',
          created_at: '2024-01-02T00:00:00Z',
          updated_at: '2024-01-02T00:00:00Z',
        },
      ]

      mockContext.db.query.customers.findMany.mockResolvedValue(mockCustomers)

      const result = await customerService.findAll()

      expect(result).toHaveLength(2)
      expect(result[0].name).toBe('Customer 1')
      expect(result[1].name).toBe('Customer 2')
    })
  })

  describe('findByStatus', () => {
    it('should return customers by status', async () => {
      const activeCustomers = [
        {
          id: 'cust-1',
          name: 'Active Customer',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ]

      mockContext.db.query.customers.findMany.mockResolvedValue(activeCustomers)

      const result = await customerService.findByStatus('active')

      expect(result).toHaveLength(1)
      expect(result[0].status).toBe('active')
    })
  })

  describe('searchByName', () => {
    it('should return customers matching search term', async () => {
      const searchResults = [
        {
          id: 'cust-1',
          name: 'Test Customer Inc',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ]

      mockContext.db.query.customers.findMany.mockResolvedValue(searchResults)

      const result = await customerService.searchByName('Test')

      expect(result).toHaveLength(1)
      expect(result[0].name).toContain('Test')
    })
  })

  describe('updateStatus', () => {
    it('should update customer status', async () => {
      const existingCustomer = {
        id: 'cust-1',
        name: 'Test Customer',
        status: 'active',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      }

      const updatedCustomer = {
        ...existingCustomer,
        status: 'inactive',
        updated_at: '2024-01-02T00:00:00Z',
      }

      mockContext.db.query.customers.findFirst
        .mockResolvedValueOnce(existingCustomer)
        .mockResolvedValueOnce(updatedCustomer)

      mockContext.db.update.mockResolvedValue(undefined)

      const result = await customerService.updateStatus('cust-1', 'inactive', mockUser)

      expect(result.status).toBe('inactive')
    })

    it('should throw error for invalid status', async () => {
      await expect(
        customerService.updateStatus('cust-1', 'invalid-status', mockUser)
      ).rejects.toThrow(BusinessLogicError)
    })
  })

  describe('getCustomerStats', () => {
    it('should return customer statistics', async () => {
      const mockCustomers = [
        { id: 'cust-1', status: 'active' },
        { id: 'cust-2', status: 'active' },
        { id: 'cust-3', status: 'inactive' },
      ]

      mockContext.db.query.customers.findMany.mockResolvedValue(mockCustomers)

      const result = await customerService.getCustomerStats()

      expect(result).toEqual({
        total: 3,
        active: 2,
        inactive: 1,
      })
    })
  })
})
