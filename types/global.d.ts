// Global type declarations for the ERP system

// D3 type declarations (fallback if @types packages don't work)
declare module 'd3-array' {
  export function extent<T>(array: T[], accessor?: (d: T) => number | string | Date): [number | string | Date, number | string | Date] | [undefined, undefined]
  export function max<T>(array: T[], accessor?: (d: T) => number): number | undefined
  export function min<T>(array: T[], accessor?: (d: T) => number): number | undefined
  export function range(start: number, stop?: number, step?: number): number[]
  export function bisect<T>(array: T[], x: T, lo?: number, hi?: number): number
}

declare module 'd3-color' {
  export interface RGBColor {
    r: number
    g: number
    b: number
    opacity: number
  }
  export function rgb(color: string): RGBColor
  export function hsl(color: string): any
}

declare module 'd3-ease' {
  export function easeLinear(t: number): number
  export function easeQuad(t: number): number
  export function easeCubic(t: number): number
  export function easeElastic(t: number): number
}

declare module 'd3-interpolate' {
  export function interpolate(a: any, b: any): (t: number) => any
  export function interpolateNumber(a: number, b: number): (t: number) => number
  export function interpolateString(a: string, b: string): (t: number) => string
}

declare module 'd3-path' {
  export interface Path {
    moveTo(x: number, y: number): void
    lineTo(x: number, y: number): void
    closePath(): void
    toString(): string
  }
  export function path(): Path
}

declare module 'd3-scale' {
  export interface ScaleLinear<Range, Output> {
    (value: number): Output
    domain(): number[]
    domain(domain: number[]): this
    range(): Range[]
    range(range: Range[]): this
  }
  export function scaleLinear(): ScaleLinear<number, number>
  export function scaleTime(): any
  export function scaleOrdinal(): any
}

declare module 'd3-shape' {
  export interface Line<T> {
    (data: T[]): string | null
    x(): (d: T, i: number, data: T[]) => number
    x(x: (d: T, i: number, data: T[]) => number): this
    y(): (d: T, i: number, data: T[]) => number
    y(y: (d: T, i: number, data: T[]) => number): this
  }
  export function line<T>(): Line<T>
  export function area<T>(): any
  export function arc(): any
  export function pie(): any
}

declare module 'd3-time' {
  export function timeDay(date: Date): Date
  export function timeWeek(date: Date): Date
  export function timeMonth(date: Date): Date
  export function timeYear(date: Date): Date
}

declare module 'd3-timer' {
  export function timer(callback: (elapsed: number) => void, delay?: number, time?: number): Timer
  export interface Timer {
    restart(callback: (elapsed: number) => void, delay?: number, time?: number): void
    stop(): void
  }
}

// Jest global types
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidDate(): R
      toHaveValidationError(field: string): R
    }
  }

  // Global test utilities
  var testUtils: {
    mockUser: {
      id: string
      name: string
      email: string
      role: string
    }
    mockDbResponse: (data: any) => Promise<any>
    mockApiResponse: (data: any, status?: number) => any
    waitFor: (ms?: number) => Promise<void>
    createMockFormData: (data: Record<string, any>) => FormData
  }
}

// Environment variables
declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test'
    DATABASE_URL: string
    NEXTAUTH_SECRET: string
    NEXTAUTH_URL: string
    REDIS_URL?: string
    SMTP_HOST?: string
    SMTP_PORT?: string
    SMTP_USER?: string
    SMTP_PASS?: string
    AWS_ACCESS_KEY_ID?: string
    AWS_SECRET_ACCESS_KEY?: string
    AWS_REGION?: string
    AWS_S3_BUCKET?: string
    SENTRY_DSN?: string
    LOG_LEVEL?: string
  }
}

// Extend Window interface for browser globals
declare interface Window {
  // Add any window globals here
  gtag?: (...args: any[]) => void
}

// Module augmentations for libraries
declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      name: string
      email: string
      role: string
      permissions: string[]
    }
  }

  interface User {
    id: string
    name: string
    email: string
    role: string
    permissions: string[]
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string
    role: string
    permissions: string[]
  }
}

// Drizzle ORM type extensions
declare module 'drizzle-orm' {
  interface QueryBuilder {
    // Add any custom query builder methods
  }
}

// Custom utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form types
export interface FormState {
  isSubmitting: boolean
  errors: Record<string, string>
  touched: Record<string, boolean>
}

// Chart data types
export interface ChartDataPoint {
  label: string
  value: number
  color?: string
}

export interface TimeSeriesDataPoint {
  date: string
  value: number
  category?: string
}

// Export all types
export {}
